class CustomerDocumentScreen2Model {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  CustomerDocumentScreen2Model(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CustomerDocumentScreen2Model.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _workcategoryId;
  String? _workcategory;
  String? _documentAvailable;

  Data(
      {String? workcategoryId,
        String? workcategory,
        String? documentAvailable}) {
    if (workcategoryId != null) {
      this._workcategoryId = workcategoryId;
    }
    if (workcategory != null) {
      this._workcategory = workcategory;
    }
    if (documentAvailable != null) {
      this._documentAvailable = documentAvailable;
    }
  }

  String? get workcategoryId => _workcategoryId;
  set workcategoryId(String? workcategoryId) =>
      _workcategoryId = workcategoryId;
  String? get workcategory => _workcategory;
  set workcategory(String? workcategory) => _workcategory = workcategory;
  String? get documentAvailable => _documentAvailable;
  set documentAvailable(String? documentAvailable) =>
      _documentAvailable = documentAvailable;

  Data.fromJson(Map<String, dynamic> json) {
    _workcategoryId = json['workcategory_id'];
    _workcategory = json['workcategory'];
    _documentAvailable = json['document_available'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['workcategory_id'] = this._workcategoryId;
    data['workcategory'] = this._workcategory;
    data['document_available'] = this._documentAvailable;
    return data;
  }
}
