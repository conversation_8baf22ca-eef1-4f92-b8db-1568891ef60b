import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:erpcacustomer/activity/LoginActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/ReceiptModel.dart';
import 'package:flutter/material.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:get/get.dart';

class ReceiptDetailsFragment extends StatefulWidget {
  String? billing_id;

  ReceiptDetailsFragment({
    Key? key,
    required this.billing_id,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return ReceiptDetailsFragmentState();
  }
}

class ReceiptDetailsFragmentState extends State<ReceiptDetailsFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var savingObs = false.obs;
  var selectedIndexObs = 0.obs;
  var imageObs = Rxn<File>();
  var selectedUserObs = ''.obs;
  var selectedBuisnessObs = ''.obs;

  // Original variables (keeping for compatibility)
  String actualName = "";
  String actualEmail = "";
  String profilePic = "";
  String departmentName = "";
  String designationName = "";
  String dateOfBirth = "";
  DateTime? dateOfBirthDate;
  String actualContact = "";
  ImagePickerHandler? imagePicker;
  AnimationController? _controller;
  bool _saving = false;
  TabController? _tabController;
  int _selectedIndex = 0;
  File? _image;
  String selectedUser = "";
  String selectedBuisness = "";

  @override
  Widget build(BuildContext context) {
    // ✅ OPTIMIZED: Only wrap specific reactive widgets with Obx
    return Obx(() => ModalProgressHUD(
      inAsyncCall: savingObs.value,
      child: Scaffold(
        body: FutureBuilder<ReceiptModel>(
          future: billreceiptCall(
              Constants.ACC_ID, Constants.USER_ID, widget.billing_id.toString(), context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? (snapshot.data!.data!.length > 0)
                    ? ListView.separated(
                        physics: ClampingScrollPhysics(),
                        shrinkWrap: true,
                        separatorBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 85.0),
                          );
                        },
                        padding: EdgeInsets.zero,
                        itemCount: snapshot.data!.data!.length,
                        itemBuilder: (BuildContext context, int index) {
                          return _gstBottomValue(snapshot.data!.data![index]);
                        },
                      )
                    : Center(child: Text(snapshot.data!.message.toString()))
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
      ),
    ));
  }

  postBillreceiptCall(String acc_id, String user_id,
      String billing_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await billreceiptApi(acc_id, user_id, billing_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        ReceiptModel currentParsedResponse =
            ReceiptModel.fromJson(responseJson);

        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<ReceiptModel> billreceiptCall(String acc_id, String user_id,
      String billing_id, BuildContext context) async {
    return await postBillreceiptCall(acc_id, user_id, billing_id, context);
  }

  _signoutMethodCall() async {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LoginActivity()),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    Future<String> token = new PreferenceManagerUtil().getMainUser();
    token.then((value) {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedUser = value;
      selectedUserObs.value = value;
      // setState(() { selectedUser = value; }); // Removed
    });
    Future<String> tokenBuisness =
        new PreferenceManagerUtil().getBuisnessName();
    tokenBuisness.then((value) {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedBuisness = value;
      selectedBuisnessObs.value = value;
      // setState(() { selectedBuisness = value; }); // Removed
    });
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
    _tabController = TabController(length: 3, vsync: this);
    _controller!.addListener(() {
      // ✅ OPTIMIZED: GetX reactive state management
      _selectedIndex = _tabController!.index;
      selectedIndexObs.value = _selectedIndex;
      // setState(() { _selectedIndex = _tabController!.index; }); // Removed
    });
  }

  Widget _gstBottomValue(Data data) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        color: Color(new CommonColor().white_Color),
        elevation: 2,
        shadowColor: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 13.0, right: 13, top: 15),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Receipt Date",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Text(
                          data.insDate.toString(),
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                      ),
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Amount",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Text(
                          "₹ " + data.amount.toString() + "/-",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            /*  Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 15),
              child: Text(
                data.invoiceType,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/
            /* Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 5),
              child: Text(
                data.billDescription,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/

            Divider(),
            Padding(
              padding: const EdgeInsets.only(left: 13.0, right: 13, top: 5),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Payment Mode",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Text(
                          data.paymentMode.toString(),
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                      ),
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        "Payment Ref. No.",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Text(
                          data.insNo.toString(),
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Divider(),
            Padding(
              padding: const EdgeInsets.only(
                  left: 13.0, right: 13, top: 5, bottom: 15),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "TDS Deducted",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      (data.tdsDeducted != "-" && data.tdsDeducted != "0")
                          ? Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                "₹ " + data.tdsDeducted.toString() + "/-",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                "-",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            )
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(
                        "Discount",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      (data.waivedOff != "-" && data.waivedOff != "0")
                          ? Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                "₹ " + data.waivedOff.toString() + "/-",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            )
                          : Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                "-",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            )
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    // ✅ OPTIMIZED: GetX reactive state management
    this._image = _image;
    imageObs.value = _image;
    // setState(() { this._image = _image; }); // Removed
  }

  DateTime convertDateFromString(String strDate) {
    DateTime todayDate = DateTime.parse(strDate);
    ;
    return todayDate;
  }
}
