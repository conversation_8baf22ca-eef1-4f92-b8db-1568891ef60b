// Invoice Service
// Handles invoice-related API operations

import 'package:get/get.dart';
import 'api_service.dart';

class InvoiceService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get all invoices
  Future<List<Map<String, dynamic>>> getAllInvoices() async {
    try {
      final response = await _apiService.get('/invoices');
      if (response['success']) {
        return List<Map<String, dynamic>>.from(response['data']['invoices']);
      }
      return [];
    } catch (e) {
      print('Get invoices error: $e');
      return [];
    }
  }

  // Get invoice by ID
  Future<Map<String, dynamic>?> getInvoiceById(String invoiceId) async {
    try {
      final response = await _apiService.get('/invoices/$invoiceId');
      if (response['success']) {
        return response['data']['invoice'];
      }
      return null;
    } catch (e) {
      print('Get invoice error: $e');
      return null;
    }
  }

  // Get invoice PDF
  Future<Map<String, dynamic>> getInvoicePdf(String invoiceId) async {
    try {
      return await _apiService.get('/invoices/$invoiceId/pdf');
    } catch (e) {
      return {'success': false, 'message': 'Failed to get invoice PDF'};
    }
  }

  // Pay invoice
  Future<Map<String, dynamic>> payInvoice(String invoiceId, Map<String, dynamic> paymentData) async {
    try {
      return await _apiService.post('/invoices/$invoiceId/pay', body: paymentData);
    } catch (e) {
      return {'success': false, 'message': 'Failed to process payment'};
    }
  }
}
