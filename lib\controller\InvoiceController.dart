import 'dart:convert';
import 'dart:developer';

import 'package:erpcacustomer/common/Constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';

String otp = '';
String mobile_no = '';
var responseJson;
//
//void verifyLogin(String mobile_Number, BuildContext context) {
//  if (mobile_Number.isEmpty) {
//    MyUtils.showToast("Please Enter Mobile Number.");
//  } else if (mobile_Number.length != 10) {
//    MyUtils.showToast("Please Enter Proper Mobile Number.");
//  } else {
////    MyUtils.showToast("Api Call");
//    callLoginApi(mobile_Number, context);
//  }
//}

Future<dynamic> invoiceListApi(
  String acc_id,
  String user_id,
  String bill_type,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'bill_type': bill_type,
  };
  return postWithContent(Constants.POST_INVOICE_LIST_URL, body, context);
}

Future<dynamic> ordersApi(
    headers,
    int amount,
    String currency,
    BuildContext context,
    ) async {
  Map body = {
    'amount': amount,
    'currency': currency,
  };

  return postWithContent1(Constants.GET_ORDER_ID_RAZOR_PAY,headers, body, context);
}


Future<dynamic> razorPayAction(
    headers,
    String acc_id,
    String pay_id,
    String order_id,
    String status,
    String remark,
    BuildContext context,
    ) async {

  Map body = {
    'acc_id': acc_id,
    'pay_id': pay_id,
    'order_id': order_id,
    'status': status,
    'remark': remark,
  };
  return postWithContent2(Constants.RAZOR_PAY_ACTION, headers,body, context);
}

Future<dynamic> storeAction(
    headers,
    String acc_id,
    String billing_id,
    String customer_id,
    String order_id,
    BuildContext context,
    ) async {

  Map body = {
    'acc_id': acc_id,
    'billing_id': billing_id,
    'customer_id': customer_id,
    'order_id': order_id,
  };
  return postWithContent3('https://www.erpca.in/dev/api-customer-app/store_oder_api.php', headers,body, context);
}





Future<dynamic> checkPaymentApi(
    String acc_id, context
    ) async {
  Map<String, String> body = {
    'acc_id': acc_id,
  };
  return postWithContent(Constants.CHECK_PAYMENT_GATEWAY, body, context);
}

Future<dynamic> getAmount(
    String acc_id, String billingId, context
    ) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'billing_id' : billingId,
  };
  return postWithContent(Constants.GET_NET_AMOUNT, body, context);
}
Future<dynamic> workCategoryListApi(
  String acc_id,
  BuildContext context,
) async {
  Map<String, String> body = {'acc_id': acc_id};
  return postWithContent(Constants.POST_WORK_CATEGORY_LIST_URL, body, context);
}

Future<dynamic> workCategoryDocRequiredListApi(
  String acc_id,
  String workcategory_id,
  String service_request_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'workcategory_id': workcategory_id,
    'service_request_id': service_request_id,
  };
  return postWithContent(
      Constants.POST_WORK_CATEGORY_DOC_REQUIRED_LIST_URL, body, context);
}

Future<dynamic> notificationListApi(
  String acc_id,
  String user_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
  };
  return postWithContent(Constants.POST_NOTIFICATION_LIST_URL, body, context);
}

Future<dynamic> selectedCustomerApi(
  String acc_id,
  String user_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
  };

  return postWithContent(
      Constants.POST_SELECTED_CUSTOMER_DETAILS_URL, body, context);
}

Future<dynamic> notificationDetailsApi(
  String acc_id,
  String user_id,
  String msg_id,
  String notification_type,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'msg_id': msg_id,
    'notification_type': notification_type,
  };

  return postWithContent(
      Constants.POST_NOTIFICATION_DETAILS_URL, body, context);
}

Future<dynamic> workCategoryUploadDocRequiredListApi(
  String acc_id,
  String user_id,
  String service_request_id,
  String document_primary_key_id,
  String file_to_be_uploaded,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'service_request_id': service_request_id,
    'document_primary_key_id': document_primary_key_id,
    'file_to_be_uploaded': file_to_be_uploaded,
  };
  return postWithContent(
      Constants.POST_SERVICE_REQ_UPLOAD_DOCUMENT_URL, body, context);
}

Future<dynamic> workUploadRequiredApi(
  String acc_id,
  String user_id,
  String document_primary_key_id,
  String file_to_be_uploaded,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'document_primary_key_id': document_primary_key_id,
    'file_to_be_uploaded': file_to_be_uploaded,
  };
  return postWithContent(
      Constants.POST_UPLOAD_REQUIRED_FILE_URL, body, context);
}

Future<dynamic> workDeleteRequiredApi(
  String acc_id,
  String user_id,
  String document_primary_key_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'document_primary_key_id': document_primary_key_id,
  };
  return postWithContent(
      Constants.POST_DELETE_REQUIRED_FILE_URL, body, context);
}

Future<dynamic> workCategoryDeleteDocRequiredListApi(
  String acc_id,
  String user_id,
  String service_request_id,
  String document_primary_key_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'service_request_id': service_request_id,
    'document_primary_key_id': document_primary_key_id,
  };

  return postWithContent(
      Constants.POST_SERVICE_REQ_DELETE_DOCUMENT_URL, body, context);
}

Future<dynamic> serviceRequestFromSubmissionApi(
  String acc_id,
  String user_id,
  String workcategory_id,
  String work_description,
  String preferred_communication_mode,
  String preferred_date_time_to_call,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'workcategory_id': workcategory_id,
    'work_description': work_description,
    'preferred_communication_mode': preferred_communication_mode,
    'preferred_date_time_to_call': preferred_date_time_to_call,
  };

  return postWithContent(
      Constants.POST_SERVICE_REQUEST_FROM_SUBMISSION_URL, body, context);
}

Future<dynamic> getCustomerTaskDetailsApi(
  String acc_id,
  String user_id,
  String task_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'task_id': task_id,
  };
  return postWithContent(
      Constants.POST_CUSTOMER_TASK_DETAILS_URL, body, context);
}

Future<dynamic> getMobileAppAccountInfoApi(
  String acc_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
  };

  return postWithContent(
      Constants.POST_MOBILE_APP_ACCOUNT_INFO_URL, body, context);
}

Future<dynamic> checkAppVersion(
    String acc_id,
    String version_no,
    BuildContext context,
    ) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'version_no': version_no,
  };

  return postWithContent(
      Constants.VERSION_CHECK, body, context);
}

Future<dynamic> billreceiptApi(
  String acc_id,
  String user_id,
  String billing_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'billing_id': billing_id,
  };
  return postWithContent(Constants.POST_BILL_RECEIPT_URL, body, context);
}

Future<dynamic> post(String url, var body, BuildContext context) async {
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http.post(Uri.parse(url), body: body, headers: {
    "Accept": "application/json",
    "Cache-Control": "false",
    "eO2-Secret-Code": Constants.ACCESS_TOKEN
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}
Future<dynamic> postWithContent3(
    String url, var headers,  var body, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  
  return await http.post(Uri.parse(url), body: json.encode(body), headers: headers).then((http.Response responseVar) {
    final int statusCode = responseVar.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(responseVar.body);
    if(responseVar.body.isNotEmpty) {
      json.decode(responseVar.body);
    }
    else{
    }
    return responseJson;
  });
}

Future<dynamic> postWithContent2(
    String url, var headers,  var body, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  
  return await http.post(Uri.parse(url), body: json.encode(body), headers: headers).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> postWithContent1(
    String url, var headers,  var body, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  
  return await http.post(Uri.parse(url), body: json.encode(body), headers: headers).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> postWithContent(
    String url, var body, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  
  return await http.post(Uri.parse(url), body: json.encode(body), headers: {
    "Id": "erpca",
    "Pass": "e736258681ac6d7126d298cc93a732db1dad2996",
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }

    if(response.body.isNotEmpty) {
      responseJson = json.decode(response.body);
    }
    return responseJson;
  });
}

Future<dynamic> postWithContentToken(
    String url, var body, String token, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  
  return await http.post(Uri.parse(url), body: json.encode(body), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> getWithContent(
    String url, String token, var body, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url');
  
  return await http.get(Uri.parse(url), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}
