import 'dart:convert';
import 'dart:io' as Io;
import 'dart:ui';


import 'package:erpcacustomer/activity/DocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/CustomerDocumentScreen3Activity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:erpcacustomer/controller/DocumentController.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
//import 'package:flutter_share/flutter_share.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_picker/image_picker.dart';


import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:url_launcher/url_launcher.dart';

import '../activity/OtherDocumentDetailsSecondActivity.dart';
import '../controller/CreateFolderFileController.dart';
import '../model/CustomerOtherDocumentScreen2Model.dart';
import '../model/customer_create_folder_model.dart';
import '../model/customer_file_upload_model.dart';

class OtherDocumentDetailsSecondFragment extends StatefulWidget {
  String? acc_id;
  String? user_id;
  String? folder_id;
  String? folder_name;
  OtherDocumentDetailsSecondFragment({Key? key,required this.acc_id,required this.user_id, required this.folder_id, required this.folder_name})
      : super(key: key);

  @override
  OtherDocumentDetailsSecondFragmentState createState() {
    return new OtherDocumentDetailsSecondFragmentState();
  }
}

class OtherDocumentDetailsSecondFragmentState
    extends State<OtherDocumentDetailsSecondFragment> {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var hasCardObs = false.obs;
  var showLoaderObs = false.obs;
  var folderCreatingObs = false.obs;
  var fileUploadingObs = false.obs;
  var isLoadingObs = false.obs;
  var documentsRefreshTrigger = 0.obs; // ✅ ADDED: Trigger for document refresh

  // Original variables (keeping for compatibility)
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  TextEditingController folderNameController = TextEditingController();
  TextEditingController fileNameController = TextEditingController();
  FilePickerResult? result;
  XFile? image;
  final ImagePicker _picker = ImagePicker();
  bool showloader = false;

  Future<CustomerCreateFolderModel> customerCreateFolder(String folderName, BuildContext context) async {
    print("Values For Folder ${widget.acc_id.toString()} ${widget.user_id.toString()} ${widget.folder_id.toString()} ${widget.folder_name}");
    return await customerCreateFolderApi(widget.acc_id.toString(), widget.user_id.toString(),widget.folder_id.toString(), folderName, context);
  }

  Future<CustomerFileUploadModel> customerFileUpload(String fileName, String fileToBeUploaded, BuildContext context) async {
    print("Values For Folder ${widget.acc_id.toString()} ${widget.user_id.toString()} ${widget.folder_id.toString()} ${widget.folder_name}");
    return await customerFileUploadApi(widget.acc_id.toString(), widget.user_id.toString(), fileName, widget.folder_id.toString(), fileToBeUploaded, context);
  }

  // ✅ OPTIMIZED: Method to refresh data when needed (GetX reactive)
  void _refreshDocuments() {
    // ✅ REPLACED setState with GetX reactive update
    _documentsFuture = customerDocumentScreen2Call(
        widget.acc_id.toString(),
        widget.user_id.toString(),
        widget.folder_id.toString(),
        context);
    documentsRefreshTrigger.value++; // ✅ Trigger UI refresh reactively
    print("🔄 Documents refreshed using GetX reactive state management");
  }

  // ✅ OPTIMIZED: Future variable to prevent continuous API calls
  late Future<CustomerOtherDocumentScreen2Model> _documentsFuture;

  @override
  void initState() {
    super.initState();
    // ✅ OPTIMIZED: Initialize GetX reactive variables
    _hasCard = false;
    hasCardObs.value = false;
    showLoaderObs.value = false;
    folderCreatingObs.value = false;
    fileUploadingObs.value = false;
    isLoadingObs.value = false;
    // ✅ OPTIMIZED: Create Future once to prevent continuous refreshing
    _documentsFuture = customerDocumentScreen2Call(
        widget.acc_id.toString(),
        widget.user_id.toString(),
        widget.folder_id.toString(),
        context);
  }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Stack(
        children: [
          _buildBackground(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Material(
      child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
        //Above card
        // ✅ OPTIMIZED: GetX reactive wrapper for automatic UI updates
        Obx(() {
          // ✅ Watch for refresh trigger changes
          documentsRefreshTrigger.value;

          return FutureBuilder<CustomerOtherDocumentScreen2Model>(
            // ✅ OPTIMIZED: Use stored Future to prevent continuous API calls
            future: _documentsFuture,
            builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
              return Scaffold(
                floatingActionButton: new FloatingActionButton(
                  heroTag: "plus",
                  backgroundColor:
                  Color(new CommonColor().erpca_blue_color),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        // return object of type Dialog
                        return AlertDialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          content: Container(
                            alignment: Alignment.center,
                            height: 90,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Column(
                              children: [
                                /* InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                    folderCreateBottomSheet();
                                    //Navigator.push(context, MaterialPageRoute(builder: (context) => CreateNewUser([])));
                                  },
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Row(
                                        children: [
                                          /*Container(
                                            margin: EdgeInsets.only(right: 5),
                                            child: SvgPicture.asset(
                                              'assets/images/customer_tab_icons/person_add.svg',
                                              height: 18,
                                            ),
                                          ),*/
                                          Text(
                                              'Create New Folder',
                                             style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.w500,
                                                fontSize: 15,
                                              ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ), */
                                SizedBox(
  width: 250, // Set desired width
  child: Material(
    color: Colors.grey[200], // Background color of the button
    elevation: 2, // Shadow effect
    borderRadius: BorderRadius.circular(8),
    child: InkWell(
      onTap: () {
        Navigator.pop(context);
        folderCreateBottomSheet();
      },
      borderRadius: BorderRadius.circular(8), // Ripple corner radius
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.create_new_folder, size: 18, color: Colors.black),
            const SizedBox(width: 8),
            Text(
              'Create New Folder',
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w500,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    ),
  ),
),
Spacer(),
SizedBox(
  width: 250, // Optional: set desired button width
  child: Material(
    color: Colors.grey[200], // Light background color
    elevation: 2, // Adds shadow
    borderRadius: BorderRadius.circular(8),
    child: InkWell(
      onTap: () {
        Navigator.pop(context);
        fileUploadBottomSheet();
      },
      borderRadius: BorderRadius.circular(8), // Ripple follows rounded edges
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.upload_file, size: 20, color: Colors.black),
            const SizedBox(width: 8),
            Text(
              'Upload New File',
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w500,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    ),
  ),
),

                                /* InkWell(
                                 onTap: (){
                                   Navigator.pop(context);
                                   fileUploadBottomSheet();
                                 },
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Row(
                                        children: [
                                          /*Container(
                                            margin: EdgeInsets.only(right: 5),
                                            child: SvgPicture.asset(
                                              'assets/images/customer_tab_icons/contacts.svg',
                                              height: 20,
                                            ),
                                          ),*/
                                          Text(
                                            'Upload New File',
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ), */
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  }, // Switch tabs
                  child: new Icon(Icons.add,color: Colors.white,),
                ),
                body: Padding(
                      padding: const EdgeInsets.only(top: 20.0),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            ListView.separated(
                              physics: ClampingScrollPhysics(),
                              shrinkWrap: true,
                              separatorBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(left: 13.0, right: 13),
                                  child: Divider(),
                                );
                              },
                              padding: EdgeInsets.zero,
                              itemCount: (snapshot.data!.subFolders!.isNotEmpty?snapshot.data!.subFolders!.length:0) +
                                  (  snapshot.data!.fileList!.isNotEmpty   ? snapshot.data!.fileList!.length:0) ,
                              itemBuilder: (BuildContext context, int index) {

                                return (
                                    index<snapshot.data!.subFolders!.length && snapshot.data!.subFolders!.isNotEmpty)?
                                _gstBottomValue(snapshot.data!.subFolders![index]):
                                snapshot.data!.fileList!.isNotEmpty?_gstBottomValue1(snapshot.data!.fileList![index - snapshot.data!.subFolders!.length ]):
                                Center(
                                  child: Container(
                                    child: Text('No document(s) available!',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontFamily: 'GlacialIndifference',
                                      ),
                                    ),
                                  ),
                                );
                              },
                            ),
                            // ✅ OPTIMIZED: GetX reactive loader widget
                            Obx(() => showLoaderObs.value == true ? Center(
                                child: Container(
                                    child: new MyUtils().kLoadingWidget(context))) : Container()),
                          ],
                        ),
                      ),
                    ),
              );
            }
            if(snapshot.connectionState == ConnectionState.waiting){
              print("else connection waiting");
              return Center(
                  child: Container(

                      child: new MyUtils().kLoadingWidget(context)));
            }
            else {
              print("else connection");
              return Scaffold(
                floatingActionButton: new FloatingActionButton(
                  heroTag: "plus",
                  backgroundColor:
                  Color(new CommonColor().erpca_blue_color),
                  onPressed: () {
                    showDialog(
                      context: context,
                      builder: (BuildContext context) {
                        return AlertDialog(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                          content: Container(
                            alignment: Alignment.center,
                            height: 90,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Column(
                              children: [
                                /* InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                    folderCreateBottomSheet();
                                    //Navigator.push(context, MaterialPageRoute(builder: (context) => CreateNewUser([])));
                                  },
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Row(
                                        children: [
                                          /*Container(
                                            margin: EdgeInsets.only(right: 5),
                                            child: SvgPicture.asset(
                                              'assets/images/customer_tab_icons/person_add.svg',
                                              height: 18,
                                            ),
                                          ),*/
                                          Text(
                                            'Create New Folder',
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                                InkWell(
                                  onTap: (){
                                    Navigator.pop(context);
                                    fileUploadBottomSheet();
                                  },
                                  child: Container(
                                    child: Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Row(
                                        children: [
                                          /*Container(
                                            margin: EdgeInsets.only(right: 5),
                                            child: SvgPicture.asset(
                                              'assets/images/customer_tab_icons/contacts.svg',
                                              height: 20,
                                            ),
                                          ),*/
                                          Text(
                                            'Upload New File',
                                            style: TextStyle(
                                              color: Colors.black,
                                              fontWeight: FontWeight.w500,
                                              fontSize: 15,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ), */
                                 SizedBox(
  width: 250, // Set desired width
  child: Material(
    color: Colors.grey[200], // Background color of the button
    elevation: 2, // Shadow effect
    borderRadius: BorderRadius.circular(8),
    child: InkWell(
      onTap: () {
        Navigator.pop(context);
        folderCreateBottomSheet();
      },
      borderRadius: BorderRadius.circular(8), // Ripple corner radius
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.create_new_folder, size: 18, color: Colors.black),
            const SizedBox(width: 8),
            Text(
              'Create New Folder',
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w500,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    ),
  ),
),
Spacer(),
SizedBox(
  width: 250, // Optional: set desired button width
  child: Material(
    color: Colors.grey[200], // Light background color
    elevation: 2, // Adds shadow
    borderRadius: BorderRadius.circular(8),
    child: InkWell(
      onTap: () {
        Navigator.pop(context);
        fileUploadBottomSheet();
      },
      borderRadius: BorderRadius.circular(8), // Ripple follows rounded edges
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.upload_file, size: 20, color: Colors.black),
            const SizedBox(width: 8),
            Text(
              'Upload New File',
              style: TextStyle(
                color: Colors.black,
                fontWeight: FontWeight.w500,
                fontSize: 15,
              ),
            ),
          ],
        ),
      ),
    ),
  ),
),
                              ],
                            ),
                          ),
                        );
                      },
                    );
                  }, // Switch tabs
                  child: new Icon(Icons.add,color: Colors.white,),
                ),
                body: Center(
                  child: Container(
                    child: Text('No document(s) available!',
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: 'GlacialIndifference',
                      ),
                    ),
                  ),
                ),
              );
            }
          },
        ); // End of FutureBuilder
        }), // ✅ End of Obx wrapper
        // Positioned to take only AppBar size
      ]),
    );
  }

  postCustomerDocumentScreen2Call(
      String acc_id,
      String user_id,
      String folder_id,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await customerOtherDocumentScreen2Api(acc_id, user_id, folder_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        CustomerOtherDocumentScreen2Model currentParsedResponse =
        CustomerOtherDocumentScreen2Model.fromJson(responseJson);
        return currentParsedResponse;
      } else {
      /*  MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());*/
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<CustomerOtherDocumentScreen2Model> customerDocumentScreen2Call(
      String acc_id,
      String user_id,
      String folder_id,
      BuildContext context) async {
    return await postCustomerDocumentScreen2Call(acc_id, user_id, folder_id, context);
  }

  Widget _gstBottomValue(SubFolders data) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        InkWell(
          onTap: (){
            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => OtherDocumentDetailsSecondActivity(
                    accId: Constants.ACC_ID,
                    userId: widget.user_id,
                    folderId: data.id,
                    folderName: data.folderName,
                  )
              ),
            );
          },
          child: Container(
            child: Padding(
              padding: const EdgeInsets.only(left: 13.0, right: 13),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.folder,
                        color: Colors.amber,
                        size: 50,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              data.folderName.toString(),
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),
                         /*   Text(
                              data.documentAvailable + " Document(s)",
                              style: new CSSStyle()
                                  .poppinsLightGreyRegular14(context),
                            ),*/
                          ],
                        ),
                      ),
                    ],
                  ),
                  /* Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                  ),*/
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
  Widget _gstBottomValue1(FileList data) {
    return Column(
     // mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 13.0, right: 13),
          child: Row(
            // mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.insert_drive_file,
                      color: Color(new CommonColor().green_light_Color),
                      size: 25,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 25.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            data.filename.toString(),
                            style: new CSSStyle()
                                .poppinsLightBlackRegular16(context),
                          ),
                          /*   Text(
                        data.documentAvailable + " Document(s)",
                        style: new CSSStyle()
                            .poppinsLightGreyRegular14(context),
                      ),*/
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                alignment: Alignment.centerRight,
                margin: const EdgeInsets.only(right: 8.0),
                child: Row(
                  // /mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    InkWell(
                      onTap: () async{
                      MyUtils().getDownloadUrl1(data.filepath.toString());
                      },
                      child: Container(
                        child:   Icon(
                          Icons.download_rounded,
                         color: Color(new CommonColor().green_light_Color),
                          size: 25,
                        ),
                      ),
                    ),
                    SizedBox(width: 10,),
                 /*   InkWell(
                      onTap: () async{
                        final tasks = await FlutterDownloader.loadTasks();
                        if (tasks.length > 0) {
                          int urlMatchCount = -1;
                          for (int i = 0; i < tasks.length; i++) {
                            if (data.filePath == tasks[i].url) {
                              urlMatchCount = i;
                              break;
                            }
                          }
                          if (urlMatchCount != -1) {
                            if (data.filePath ==
                                tasks[urlMatchCount].url) {

                              if (tasks[urlMatchCount].status ==
                                  DownloadTaskStatus.undefined) {
                                new MyUtils().getDownloadUrl1(
                                    context, data.filePath, false, data.fileName);
                              } else if (tasks[urlMatchCount].status ==
                                  DownloadTaskStatus.complete) {
                                new MyUtils().getDownloadUrl1(
                                    context, data.filePath, false, data.fileName);
                                var  _localPath = "/sdcard/download/erpcacustomer/";
                                Directory savedDir = Directory(_localPath);
                                int len = savedDir.toString().length - 1;
                                var savePath = '${savedDir.toString().substring(0,len)}/${data.fileName}';
                                bool hasExisted = await savedDir.exists();

                             *//*   FlutterDownloader.open(
                                    taskId: tasks[urlMatchCount].taskId);*//*
                              Share.shareFiles([savePath]);
                              }
                            } else {
                              new MyUtils().getDownloadUrl1(
                                  context, data.filePath, false, data.fileName);
                            }
                          } else {
                            new MyUtils().getDownloadUrl1(
                                context, data.filePath, false, data.fileName);
                          }
                        } else {
                          new MyUtils().getDownloadUrl1(
                              context, data.filePath, false, data.fileName);
                        }


                       },
                      child: Container(
                        margin: const EdgeInsets.only(left: 8.0),
                        child: Icon(
                          Icons.share_outlined,
                          color: Colors.black,
                          size: 25,
                        ),
                      ),
                    ),*/
                  ],
                ),
              ),
              /* Icon(
                Icons.arrow_forward_ios,
                color: Colors.grey,
              ),*/
            ],
          ),
        ),
      ],
    );
  }

  folderCreateBottomSheet() {
    return showModalBottomSheet(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState2) {
            return SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Container(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          child: Row(
                            children: [
                              Container(
                                child: const Icon(
                                  Icons.arrow_back_ios_new_outlined,
                                  color: Colors.blue,
                                  size: 20,
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 20),
                                child: Text(
                                  'Create Folder',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: Container(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 10),
                          child: TextFormField(
                            controller: folderNameController,
                            autofocus: false,
                            decoration: InputDecoration(
                              hintText: 'Folder Name',
                              hintStyle:TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w200,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 25),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            InkWell(
                              onTap: () async {
                                if(folderNameController.text.isNotEmpty){
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  folderCreatingObs.value = true;
                                  showLoaderObs.value = true;
                                  showloader = true; // Keep for compatibility
                                  // setState(() { showloader = true; }); // Removed
                                  Navigator.pop(context);
                                  try {
                                    CustomerCreateFolderModel response = await customerCreateFolder(folderNameController.text.toString(), context);

                                    if(response.success == 1){
                                      // ✅ OPTIMIZED: Success state management
                                      folderCreatingObs.value = false;
                                      showLoaderObs.value = false;
                                      showloader = false; // Keep for compatibility
                                      // setState(() { showloader = false; }); // Removed
                                      folderNameController.text = "";
                                      // ✅ OPTIMIZED: Refresh data after folder creation
                                      _refreshDocuments();
                                    }else{
                                      // ✅ OPTIMIZED: Error state management
                                      folderCreatingObs.value = false;
                                      showLoaderObs.value = false;
                                      showloader = false; // Keep for compatibility
                                      // setState(() { showloader = false; }); // Removed
                                      MyUtils.showOkDialog(context, "Error", response.message.toString());
                                    }
                                  } catch (e) {
                                    // ✅ OPTIMIZED: Exception handling
                                    folderCreatingObs.value = false;
                                    showLoaderObs.value = false;
                                    showloader = false; // Keep for compatibility
                                    MyUtils.showOkDialog(context, "Error", "Failed to create folder");
                                  }
                                }
                                else{
                                  MyUtils.showOkDialog(context, "Error", "Please enter the folder name");
                                }

                                //navigationService.pushNamed(breakTimer);
                              },
                              child: Container(
                                child: Text(
                                  'Submit',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.pop(context);
                              },
                              child: Container(
                                margin: const EdgeInsets.only(left: 25),
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      ),
    );
  }

  fileUploadBottomSheet() {
    return showModalBottomSheet(
      context: context,
      builder: (context) {
        return StatefulBuilder(
          builder: (context, setState2) {
            return SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Container(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          child: Row(
                            children: [
                              Container(
                                child: const Icon(
                                  Icons.arrow_back_ios_new_outlined,
                                  color: Colors.blue,
                                  size: 20,
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 20),
                                child: Text(
                                  'Upload File',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: Container(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 10),
                          child: TextFormField(
                            controller: fileNameController,
                            autofocus: false,
                            decoration: InputDecoration(
                              hintText: 'File Name',
                              hintStyle:TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w200,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 10, bottom: 13, right: 20, left: 20),
                            child: InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                showUploadOptions();
                                print("Result File $result");
                                // ✅ OPTIMIZED: GetX reactive state management (no state change needed here)
                                // setState(() {}); // Removed
                              },
                              child: Container(
                                //margin: EdgeInsets.only(top: 20),
                                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 8),
                                decoration: BoxDecoration(
                                    color: Colors.white12,
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                      color: Colors.blue,
                                    )),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      child: Icon(
                                        Icons.file_upload_outlined,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    Container(
                                      child:   Text(
                                        'Upload Document',
                                        style: TextStyle(
                                          color: Colors.blue,
                                        ),

                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      ),
    );
  }

  showUploadOptions() async {
    await showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        builder: (context) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                margin: EdgeInsets.only(bottom: 10),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () async {
                        Navigator.pop(context);
                        file_camers();

                      },
                      child: Container(
                        child:   Text(
                          'Upload from camera',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                    Divider(),
                    InkWell(
                      onTap: () async {
                        Navigator.pop(context);
                        file_browse();
                        var status = await Permission.storage.status;
                        print('file permission status $status');
                        /*if (status == PermissionStatus.denied) {
                          return MyUtils.showOkDialog(context, "Error", "Please Enable Permission");
                        } else {
                          file_browse();
                        }*/
                      },
                      child: Container(
                        child:  Text(
                          'Choose from files',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  void file_browse() async {
    if (fileNameController.text.isEmpty) {
      MyUtils.showOkDialog(context, 'Error', "Please Enter Document Title");
    } else {
      result = await FilePicker.platform.pickFiles(allowMultiple: true);
      if (result == null) {
        MyUtils.showOkDialog(context, 'Error', "No file Selected");
      } else {
        result?.files.forEach((element) async {
          //print("Elemensts ${element.name}");
          var file_name = element.path;

          final bytes = Io.File(file_name.toString()).readAsBytesSync();

          String img64 = base64Encode(bytes);
          // ✅ OPTIMIZED: GetX reactive state management
          fileUploadingObs.value = true;
          showLoaderObs.value = true;
          showloader = true; // Keep for compatibility
          // setState(() { showloader = true; }); // Removed
          try {
            CustomerFileUploadModel? response = await customerFileUpload(fileNameController.text, img64, context);
            if(response.success == 1){
              print("File uploaded successfully");
              // ✅ OPTIMIZED: Success state management
              fileUploadingObs.value = false;
              showLoaderObs.value = false;
              showloader = false; // Keep for compatibility
              // setState(() { showloader = false; }); // Removed
              fileNameController.text = "";
              // ✅ OPTIMIZED: Refresh data after file upload
              _refreshDocuments();
            }else{
              print("File upload failed");
              // ✅ OPTIMIZED: Error state management
              fileUploadingObs.value = false;
              showLoaderObs.value = false;
              showloader = false; // Keep for compatibility
              // setState(() { showloader = false; }); // Removed
              MyUtils.showOkDialog(context, "Error", response.message.toString());
            }
          } catch (e) {
            // ✅ OPTIMIZED: Exception handling
            fileUploadingObs.value = false;
            showLoaderObs.value = false;
            showloader = false; // Keep for compatibility
            MyUtils.showOkDialog(context, "Error", "Failed to upload file");
          }

          /*fileNameController.text = "";
          setState(() {

          });*/

          print('File Uploaded');
        });
      }
    }
  }
  void file_camers() async{
    if (fileNameController.text.isEmpty) {
      MyUtils.showOkDialog(context, 'Error', "Please Enter Document Title");
    } else {
      final XFile? image = await _picker.pickImage(source: ImageSource.camera);
      if (image == null) {
        MyUtils.showOkDialog(context, 'Error', "No file Selected");
      } else {
        var file_name = image!.path;

        final bytes = Io.File(file_name.toString()).readAsBytesSync();

        String img64 = base64Encode(bytes);

        // ✅ OPTIMIZED: GetX reactive state management
        fileUploadingObs.value = true;
        showLoaderObs.value = true;
        showloader = true; // Keep for compatibility
        // setState(() { showloader = true; }); // Removed

        try {
          CustomerFileUploadModel? response = await customerFileUpload(fileNameController.text, img64, context);

          if(response.success == 1){
            print("File uploaded successfully");
            // ✅ OPTIMIZED: Success state management
            fileUploadingObs.value = false;
            showLoaderObs.value = false;
            showloader = false; // Keep for compatibility
            // setState(() { showloader = false; }); // Removed
            fileNameController.text = "";
            // ✅ OPTIMIZED: Refresh data after file upload
            _refreshDocuments();
          }else{
            print("File upload failed");
            // ✅ OPTIMIZED: Error state management
            fileUploadingObs.value = false;
            showLoaderObs.value = false;
            showloader = false; // Keep for compatibility
            // setState(() { showloader = false; }); // Removed
            MyUtils.showOkDialog(context, "Error", response.message.toString());
          }
        } catch (e) {
          // ✅ OPTIMIZED: Exception handling
          fileUploadingObs.value = false;
          showLoaderObs.value = false;
          showloader = false; // Keep for compatibility
          MyUtils.showOkDialog(context, "Error", "Failed to upload file");
        }

        print('File Uploaded');
      }
    }
    // ✅ OPTIMIZED: setState removed - using GetX reactive state management
    print("Result File $result");
  }
}
