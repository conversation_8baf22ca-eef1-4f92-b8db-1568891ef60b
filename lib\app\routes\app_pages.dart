// GetX App Pages
// Defines all page routes and their corresponding widgets with bindings

import 'package:get/get.dart';
import '../../activity/SplashActivity.dart';
import '../../activity/NewServicesActivity.dart';
import '../../activity/LoginActivity.dart';
import '../../activity/LoginWithMobileNumberActivity.dart';
import '../../activity/SelectUserActivity.dart';
import '../../activity/HomePageActivity.dart';
import '../../activity/SubmitRequestActivity.dart';
import '../../activity/NotificationActivity.dart';
import '../../activity/CheckMPinActivity.dart';
import '../bindings/initial_binding.dart';
import '../bindings/auth_binding.dart';
import '../bindings/home_binding.dart';
import 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    // Initial/Splash Routes
    GetPage(
      name: Routes.INITIAL,
      page: () => SplashActivity(),
      binding: InitialBinding(),
      transition: Transition.fade,
    ),
    GetPage(
      name: Routes.SPLASH,
      page: () => SplashActivity(),
      binding: InitialBinding(),
      transition: Transition.fade,
    ),
    GetPage(
      name: Routes.NEW_SERVICES,
      page: () => NewServicesActivity(),
      transition: Transition.fade,
    ),

    // Authentication Routes
    GetPage(
      name: Routes.LOGIN,
      page: () => LoginActivity(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.LOGIN_MOBILE,
      page: () => LoginWithMobileNumberActivity(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.SELECT_USER,
      page: () => SelectUserActivity(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.CHECK_MPIN,
      page: () => CheckMPinActivity(),
      binding: AuthBinding(),
      transition: Transition.rightToLeft,
    ),

    // Main App Routes
    GetPage(
      name: Routes.HOME,
      page: () => HomePageActivity(),
      binding: HomeBinding(),
      transition: Transition.fadeIn,
    ),

    // Feature Routes
    GetPage(
      name: Routes.SUBMIT_REQUEST,
      page: () => SubmitRequestActivity(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.NOTIFICATIONS,
      page: () => NotificationActivity(),
      transition: Transition.rightToLeft,
    ),
  ];
}
