import 'dart:io';

import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:get/get.dart';

import 'activity/NewServicesActivity.dart';
import 'app/routes/app_pages.dart';
import 'app/routes/app_routes.dart';
import 'app/bindings/initial_binding.dart';

final RouteObserver<PageRoute> routeObserver = RouteObserver<PageRoute>();
GlobalKey<NavigatorState> navigatorKey =
    GlobalKey(debugLabel: "Main Navigator");
FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    new FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await FlutterDownloader.initialize(debug: true);
  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: Colors.white,statusBarIconBrightness: Brightness.dark,
  ));
  runApp(
    OverlaySupport.global(
      child: GetMaterialApp(
        debugShowCheckedModeBanner: false,
        title: "EC Demo",
        theme: ThemeData(
          useMaterial3: false,
          primaryColor: Color(CommonColor().lightBlue),
        ),
        initialRoute: Routes.SPLASH,
        getPages: AppPages.routes,
        initialBinding: InitialBinding(),
        navigatorObservers: [routeObserver],
        /* routes: <String, WidgetBuilder>{
          '/SplashActivity': (BuildContext context) => new SplashActivity(),
          '/SignInActivity': (BuildContext context) => new SignInActivity(),
          '/AddInvoiceActivity': (BuildContext context) =>
          new AddInvoiceActivity(),
          '/ExpenseActivity': (BuildContext context) => new ExpenseActivity(),
          '/DirectoryActivity': (BuildContext context) => new DirectoryActivity(),
          '/MyProfileActivity': (BuildContext context) => new MyProfileActivity(),
          '/HomePageActivity': (BuildContext context) => new HomePageActivity(),
          '/AttendanceActivity': (BuildContext context) =>
          new AttendanceActivity(),
          '/CustomerActivity': (BuildContext context) => new CustomerActivity(),
          '/LeaveActivity': (BuildContext context) => new LeaveActivity(),
          '/CalenderActivity': (BuildContext context) => new CalenderActivity(),
          '/InvoiceActivity': (BuildContext context) => new InvoiceActivity(),
          '/ThankYouActivity': (BuildContext context) => new ThankYouActivity(),
          '/ProductListActivity': (BuildContext context) =>
          new ProductListActivity(),
          '/HomePageSaleActivity': (BuildContext context) =>
          new HomePageSaleActivity(),
          '/HomePageAccountantActivity': (BuildContext context) =>
          new HomePageAccountantActivity(),
          '/OrdersListActivity': (BuildContext context) =>
          new OrdersListActivity(),
          '/ProductDashboardActivity': (BuildContext context) =>
          new ProductDashboardActivity(),
          '/ReportDetailsActivity': (BuildContext context) =>
          new ReportDetailsActivity(),
          '/ForgotPasswordActivity': (BuildContext context) =>
          new ForgotPasswordActivity(),
          '/KRAActivity': (BuildContext context) => new KRAActivity(),
          '/AddTaskActivity': (BuildContext context) => new AddTaskActivity(),
          '/TASKActivity': (BuildContext context) => new TASKActivity(),
          '/WebViewExample': (BuildContext context) => new WebViewExample(),
          '/WebViewAddTicketActivity': (BuildContext context) =>
          new WebViewAddTicketActivity(),
          '/CustomerImagePageActivity': (BuildContext context) =>
          new CustomerImagePageActivity(),
          '/ProductPageActivity': (BuildContext context) =>
          new ProductPageActivity(),
          '/AddTaskCalenderActivity': (BuildContext context) =>
          new AddTaskCalenderActivity(),
          '/TicketActivity': (BuildContext context) => new TicketActivity(),
        },*/
      ),
    ),
  );

  // Platform messages are asynchronous, so we initialize in an async method.
}



Future<dynamic> onSelectNotification(String payload) async {
  navigatorKey.currentState!
      .push(MaterialPageRoute(builder: (_) => NewServicesActivity()));
}
