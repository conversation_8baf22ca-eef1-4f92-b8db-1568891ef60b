class CustomerLoginPassModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  CustomerLoginPassModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CustomerLoginPassModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _userId;
  String? _clientName;
  String? _clientBusinessName;
  String? _photoThumb;
  String? _userEmail;
  String? _contactPhone1;
  String? _groupName;
  String? _mobileNo;

  Data(
      {String? userId,
        String? clientName,
        String? clientBusinessName,
        String? photoThumb,
        String? userEmail,
        String? contactPhone1,
        String? groupName,
        String? mobileNo}) {
    if (userId != null) {
      this._userId = userId;
    }
    if (clientName != null) {
      this._clientName = clientName;
    }
    if (clientBusinessName != null) {
      this._clientBusinessName = clientBusinessName;
    }
    if (photoThumb != null) {
      this._photoThumb = photoThumb;
    }
    if (userEmail != null) {
      this._userEmail = userEmail;
    }
    if (contactPhone1 != null) {
      this._contactPhone1 = contactPhone1;
    }
    if (groupName != null) {
      this._groupName = groupName;
    }
    if (mobileNo != null) {
      this._mobileNo = mobileNo;
    }
  }

  String? get userId => _userId;
  set userId(String? userId) => _userId = userId;
  String? get clientName => _clientName;
  set clientName(String? clientName) => _clientName = clientName;
  String? get clientBusinessName => _clientBusinessName;
  set clientBusinessName(String? clientBusinessName) =>
      _clientBusinessName = clientBusinessName;
  String? get photoThumb => _photoThumb;
  set photoThumb(String? photoThumb) => _photoThumb = photoThumb;
  String? get userEmail => _userEmail;
  set userEmail(String? userEmail) => _userEmail = userEmail;
  String? get contactPhone1 => _contactPhone1;
  set contactPhone1(String? contactPhone1) => _contactPhone1 = contactPhone1;
  String? get groupName => _groupName;
  set groupName(String? groupName) => _groupName = groupName;
  String? get mobileNo => _mobileNo;
  set mobileNo(String? mobileNo) => _mobileNo = mobileNo;

  Data.fromJson(Map<String, dynamic> json) {
    _userId = json['user_id'];
    _clientName = json['client_name'];
    _clientBusinessName = json['client_business_name'];
    _photoThumb = json['photo_thumb'];
    _userEmail = json['user_email'];
    _contactPhone1 = json['contact_phone_1'];
    _groupName = json['group_name'];
    _mobileNo = json['mobile_no'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this._userId;
    data['client_name'] = this._clientName;
    data['client_business_name'] = this._clientBusinessName;
    data['photo_thumb'] = this._photoThumb;
    data['user_email'] = this._userEmail;
    data['contact_phone_1'] = this._contactPhone1;
    data['group_name'] = this._groupName;
    data['mobile_no'] = this._mobileNo;
    return data;
  }
}
