class MobileInfoModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  MobileInfoModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  MobileInfoModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _accId;
  String? _organizationName;
  String? _address;
  String? _stateName;
  String? _cityName;
  String? _pincode;
  String? _contactPerson;
  String? _contactEmail;
  String? _contactNumber;
  String? _logoThumb;
  String? _appVersion;
  String? _accountShortCode;
  String? _serviceRequetEmailSupport;
  String? _serviceRequestWhatsappSupport;
  String? _serviceRequetPhoneSupport;

  Data(
      {String? accId,
        String? organizationName,
        String? address,
        String? stateName,
        String? cityName,
        String? pincode,
        String? contactPerson,
        String? contactEmail,
        String? contactNumber,
        String? logoThumb,
        String? appVersion,
        String? accountShortCode,
        String? serviceRequetEmailSupport,
        String? serviceRequestWhatsappSupport,
        String? serviceRequetPhoneSupport}) {
    if (accId != null) {
      this._accId = accId;
    }
    if (organizationName != null) {
      this._organizationName = organizationName;
    }
    if (address != null) {
      this._address = address;
    }
    if (stateName != null) {
      this._stateName = stateName;
    }
    if (cityName != null) {
      this._cityName = cityName;
    }
    if (pincode != null) {
      this._pincode = pincode;
    }
    if (contactPerson != null) {
      this._contactPerson = contactPerson;
    }
    if (contactEmail != null) {
      this._contactEmail = contactEmail;
    }
    if (contactNumber != null) {
      this._contactNumber = contactNumber;
    }
    if (logoThumb != null) {
      this._logoThumb = logoThumb;
    }
    if (appVersion != null) {
      this._appVersion = appVersion;
    }
    if (accountShortCode != null) {
      this._accountShortCode = accountShortCode;
    }
    if (serviceRequetEmailSupport != null) {
      this._serviceRequetEmailSupport = serviceRequetEmailSupport;
    }
    if (serviceRequestWhatsappSupport != null) {
      this._serviceRequestWhatsappSupport = serviceRequestWhatsappSupport;
    }
    if (serviceRequetPhoneSupport != null) {
      this._serviceRequetPhoneSupport = serviceRequetPhoneSupport;
    }
  }

  String? get accId => _accId;
  set accId(String? accId) => _accId = accId;
  String? get organizationName => _organizationName;
  set organizationName(String? organizationName) =>
      _organizationName = organizationName;
  String? get address => _address;
  set address(String? address) => _address = address;
  String? get stateName => _stateName;
  set stateName(String? stateName) => _stateName = stateName;
  String? get cityName => _cityName;
  set cityName(String? cityName) => _cityName = cityName;
  String? get pincode => _pincode;
  set pincode(String? pincode) => _pincode = pincode;
  String? get contactPerson => _contactPerson;
  set contactPerson(String? contactPerson) => _contactPerson = contactPerson;
  String? get contactEmail => _contactEmail;
  set contactEmail(String? contactEmail) => _contactEmail = contactEmail;
  String? get contactNumber => _contactNumber;
  set contactNumber(String? contactNumber) => _contactNumber = contactNumber;
  String? get logoThumb => _logoThumb;
  set logoThumb(String? logoThumb) => _logoThumb = logoThumb;
  String? get appVersion => _appVersion;
  set appVersion(String? appVersion) => _appVersion = appVersion;
  String? get accountShortCode => _accountShortCode;
  set accountShortCode(String? accountShortCode) =>
      _accountShortCode = accountShortCode;
  String? get serviceRequetEmailSupport => _serviceRequetEmailSupport;
  set serviceRequetEmailSupport(String? serviceRequetEmailSupport) =>
      _serviceRequetEmailSupport = serviceRequetEmailSupport;
  String? get serviceRequestWhatsappSupport => _serviceRequestWhatsappSupport;
  set serviceRequestWhatsappSupport(String? serviceRequestWhatsappSupport) =>
      _serviceRequestWhatsappSupport = serviceRequestWhatsappSupport;
  String? get serviceRequetPhoneSupport => _serviceRequetPhoneSupport;
  set serviceRequetPhoneSupport(String? serviceRequetPhoneSupport) =>
      _serviceRequetPhoneSupport = serviceRequetPhoneSupport;

  Data.fromJson(Map<String, dynamic> json) {
    _accId = json['acc_id'];
    _organizationName = json['organization_name'];
    _address = json['address'];
    _stateName = json['state_name'];
    _cityName = json['city_name'];
    _pincode = json['pincode'];
    _contactPerson = json['contact_person'];
    _contactEmail = json['contact_email'];
    _contactNumber = json['contact_number'];
    _logoThumb = json['logo_thumb'];
    _appVersion = json['app_version'];
    _accountShortCode = json['account_short_code'];
    _serviceRequetEmailSupport = json['service_requet_email_support'];
    _serviceRequestWhatsappSupport = json['service_request_whatsapp_support'];
    _serviceRequetPhoneSupport = json['service_requet_phone_support'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['acc_id'] = this._accId;
    data['organization_name'] = this._organizationName;
    data['address'] = this._address;
    data['state_name'] = this._stateName;
    data['city_name'] = this._cityName;
    data['pincode'] = this._pincode;
    data['contact_person'] = this._contactPerson;
    data['contact_email'] = this._contactEmail;
    data['contact_number'] = this._contactNumber;
    data['logo_thumb'] = this._logoThumb;
    data['app_version'] = this._appVersion;
    data['account_short_code'] = this._accountShortCode;
    data['service_requet_email_support'] = this._serviceRequetEmailSupport;
    data['service_request_whatsapp_support'] =
        this._serviceRequestWhatsappSupport;
    data['service_requet_phone_support'] = this._serviceRequetPhoneSupport;
    return data;
  }
}
