class ServiceRequestListModel {
  int? _status;
  String? _message;
  int? _success;
  String? _errorMsg;
  List<Data>? _data;
  String? _errorDev;

  ServiceRequestListModel(
      {int? status,
        String? message,
        int? success,
        String? errorMsg,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (errorMsg != null) {
      this._errorMsg = errorMsg;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get errorMsg => _errorMsg;
  set errorMsg(String? errorMsg) => _errorMsg = errorMsg;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  ServiceRequestListModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _errorMsg = json['error_msg'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['error_msg'] = this._errorMsg;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _workcategoryId;
  String? _serviceRequestNo;
  String? _serviceRequested;
  String? _workDescription;
  String? _serviceRequestDate;
  String? _documentsShared;

  Data(
      {String? workcategoryId,
        String? serviceRequestNo,
        String? serviceRequested,
        String? workDescription,
        String? serviceRequestDate,
        String? documentsShared}) {
    if (workcategoryId != null) {
      this._workcategoryId = workcategoryId;
    }
    if (serviceRequestNo != null) {
      this._serviceRequestNo = serviceRequestNo;
    }
    if (serviceRequested != null) {
      this._serviceRequested = serviceRequested;
    }
    if (workDescription != null) {
      this._workDescription = workDescription;
    }
    if (serviceRequestDate != null) {
      this._serviceRequestDate = serviceRequestDate;
    }
    if (documentsShared != null) {
      this._documentsShared = documentsShared;
    }
  }

  String? get workcategoryId => _workcategoryId;
  set workcategoryId(String? workcategoryId) =>
      _workcategoryId = workcategoryId;
  String? get serviceRequestNo => _serviceRequestNo;
  set serviceRequestNo(String? serviceRequestNo) =>
      _serviceRequestNo = serviceRequestNo;
  String? get serviceRequested => _serviceRequested;
  set serviceRequested(String? serviceRequested) =>
      _serviceRequested = serviceRequested;
  String? get workDescription => _workDescription;
  set workDescription(String? workDescription) =>
      _workDescription = workDescription;
  String? get serviceRequestDate => _serviceRequestDate;
  set serviceRequestDate(String? serviceRequestDate) =>
      _serviceRequestDate = serviceRequestDate;
  String? get documentsShared => _documentsShared;
  set documentsShared(String? documentsShared) =>
      _documentsShared = documentsShared;

  Data.fromJson(Map<String, dynamic> json) {
    _workcategoryId = json['workcategory_id'];
    _serviceRequestNo = json['service_request_no'];
    _serviceRequested = json['service_requested'];
    _workDescription = json['work_description'];
    _serviceRequestDate = json['service_request_date'];
    _documentsShared = json['documents_shared'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['workcategory_id'] = this._workcategoryId;
    data['service_request_no'] = this._serviceRequestNo;
    data['service_requested'] = this._serviceRequested;
    data['work_description'] = this._workDescription;
    data['service_request_date'] = this._serviceRequestDate;
    data['documents_shared'] = this._documentsShared;
    return data;
  }
}
