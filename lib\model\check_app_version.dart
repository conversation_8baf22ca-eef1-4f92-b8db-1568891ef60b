class CheckAppVersionModel {
  int? _status;
  String? _message;
  int? _success;
  String? _errorMsg;
  String? _errorDev;

  CheckAppVersionModel(
      {int? status,
        String? message,
        int? success,
        String? errorMsg,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (errorMsg != null) {
      this._errorMsg = errorMsg;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get errorMsg => _errorMsg;
  set errorMsg(String? errorMsg) => _errorMsg = errorMsg;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CheckAppVersionModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _errorMsg = json['error_msg'];
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['error_msg'] = this._errorMsg;
    data['error_dev'] = this._errorDev;
    return data;
  }
}
