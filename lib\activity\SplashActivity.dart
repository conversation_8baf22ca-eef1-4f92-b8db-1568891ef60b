import 'dart:async';
import 'dart:io' as io;

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/CheckMPinActivity.dart';
import 'package:erpcacustomer/activity/ChooseMobileNumberActivity.dart';
import 'package:erpcacustomer/activity/LoginWithMobileNumberActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/common/requireAppUpdate.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/MobileInfoModel.dart';
import 'package:erpcacustomer/model/check_app_version.dart';
import 'package:erpcacustomer/model/push_notification.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:version/version.dart';
import 'package:get/get.dart';
import '../routes/app_routes.dart';
import '../utils/navigation_helper.dart';

class SplashActivity extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => SplashActivityState();
}

class SplashActivityState extends State<SplashActivity> {
  // GetX reactive variables - ONLY ADDED, NO FUNCTIONALITY CHANGE
  var fcmIdObs = ''.obs;
  var totalNotificationsObs = 0.obs;
  var appStatusObs = 0.obs;
  var isLoadingObs = true.obs;

  // Original variables - COMPLETELY PRESERVED
  String fcmId = "";
  PushNotification? _notificationInfo;
  int _totalNotifications = 0;
  String _debugLabelString = "";
  String _emailAddress= '';
  String _smsNumber = '';
  String _externalUserId = '';
  bool _enableConsentButton = false;

  // CHANGE THIS parameter to true if you want to test GDPR privacy consent
  bool _requireConsent = false;

  startTimeout([int? milliseconds]) {
    var duration = const Duration(seconds: 3);
    return new Timer(duration, handleTimeout);
  }
  ConnectivityResult? previousresult = ConnectivityResult.wifi;
  StreamSubscription? connectivitySubscription;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _totalNotifications = 0;
    PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      String version = packageInfo.version;
      Version latestVersion = Version.parse("$version");
      print('latest version $latestVersion');
      WidgetsBinding.instance.addPostFrameCallback((_) async {
        connectivitySubscription = Connectivity()
            .onConnectivityChanged
            .listen((ConnectivityResult nowresult) {
          previousresult = nowresult;
          print('previous result ${previousresult}');
          if (nowresult == ConnectivityResult.none) {
            // print('Not Connected');
            //TODO Flutter Toaster for None
            final timer =
            Timer(const Duration(seconds: 5), () async {
              print('Timer finished');
              MyUtils.showOkDialog(context, 'Error', 'No Internet Connection');
              Fluttertoast.showToast(
                msg: "Network Connection Error",
                backgroundColor: Colors.black,
                textColor: Colors.white,
                fontSize: 16.0,
              );
            });
          }
          // when mobile and wifi network connected
          else if (previousresult != ConnectivityResult.none) {
            // print('Connected');
            if (nowresult == ConnectivityResult.mobile) {

            } else if (nowresult == ConnectivityResult.wifi) {

            }
          }

        });
      });
      postCheckMobileAppVersion(Constants.ACC_ID, latestVersion.toString(), context);

    });

    //  registerNotification();
    //  checkForInitialMessage();
    // For handling notification when the app is in background
    // but not terminated
  }

  // For handling notification when the app is in terminated state

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return _createSplash();
  }

  _createSplash() {
    return Scaffold(
      body: FutureBuilder<MobileInfoModel>(
        future: postGetMobileAppAccountInfoCall(
            Constants.ACC_ID, Constants.USER_ID, context),
        builder: (context, snapshot) {
          return snapshot.hasData
              ? Stack(
            children: <Widget>[
              Image.asset(
                'assets/images/splash.png',
                width: double.infinity,
                fit: BoxFit.fill,
              ),
              /*   Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.cyanAccent,
                gradient: LinearGradient(colors: [
                  Colors.white,
                  Colors.white,
                ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
              ),
            ),
            Align(
              child: Center(
                child: Text(
                  "asdasdsade",
                  style: new CSSStyle().poppinsLightBlueBold40(context),
                ),
              ),
            ),*/
              Positioned.fill(
                top: 100,
                left: 0,
                right: 0,
                child: Column(
                  children: [
                    Container(
                      height: 100,
                      width: 100,
                      child: Center(
                        child: Image.network('${snapshot.data!.data![0].logoThumb}'),
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 0),
                        child: Container(
                            alignment: Alignment.bottomCenter,
                            child: Text(
                              snapshot.data!.data![0].organizationName.toString(),
                              style: new CSSStyle()
                                  .poppinsErpcaBlueRegular18(context),
                            )),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          )
              : Stack(
            children: <Widget>[
              Image.asset(
                'assets/images/splash.png',
                width: double.infinity,
                fit: BoxFit.fill,
              ),
              /*   Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                color: Colors.cyanAccent,
                gradient: LinearGradient(colors: [
                  Colors.white,
                  Colors.white,
                ], begin: Alignment.topCenter, end: Alignment.bottomCenter),
              ),
            ),
            Align(
              child: Center(
                child: Text(
                  "asdasdsade",
                  style: new CSSStyle().poppinsLightBlueBold40(context),
                ),
              ),
            ),*/
            ],
          );
        },
      ),
    );
  }

  void handleTimeout() async {
    String role = "";
    int appStatus = await PreferenceManagerUtil().getStatus();
    print('appstatus $appStatus');
    if (io.Platform.isAndroid) {
      if (appStatus == 0) {
        // Navigate to app update screen using GetX
        Get.to(() => RequiredAppUpdate());
      } else {
        Future<String> token = new PreferenceManagerUtil().getMPin();
        token.then((val) {
          role = val;
          if (role == "") {
            // Navigate to login using GetX routing
            Get.offAllNamed(Routes.LOGIN_MOBILE);
          } else {
            // Navigate to MPIN check using GetX routing
            Get.offAllNamed(Routes.CHECK_MPIN);
          }
        });
        //Navigator.push(context, MaterialPageRoute(builder: (context)=>RequiredAppUpdate()));
      }
    }
    else {
      Future<String> token = new PreferenceManagerUtil().getMPin();
      token.then((val) {
        role = val;
        if (role == "") {
          // Navigate to login using GetX routing
          Get.offAllNamed(Routes.LOGIN_MOBILE);
        } else {
          // Navigate to MPIN check using GetX routing
          Get.offAllNamed(Routes.CHECK_MPIN);
        }
      });
    }
    /*PackageInfo.fromPlatform().then((PackageInfo packageInfo) {
      String version = packageInfo.version;
      Version latestVersion = Version.parse("$version");
      print('latest version $latestVersion ${Version.parse("$appVersion")}');

    });*/
  }

  getMobileAppAccountInfoCall(
      String acc_id, String user_id, BuildContext context) async {
    /*var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {

    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }*/
    var responseJson = await getMobileAppAccountInfoApi(acc_id, context);
    int flag = responseJson["success"];

    if (flag == 1) {
      MobileInfoModel currentParsedResponse =
      MobileInfoModel.fromJson(responseJson);

      currentParsedResponse.data![0].contactEmail;
      currentParsedResponse.data![0].contactNumber;
      new PreferenceManagerUtil()
          .setContPerEmail(currentParsedResponse.data![0].contactEmail.toString());
      new PreferenceManagerUtil()
          .setContactPhone(currentParsedResponse.data![0].contactNumber.toString());

      startTimeout(10000);
      return currentParsedResponse;
    } else {
      MyUtils.showOkDialog(
          context, "Error", responseJson['message'].toString());
      var connectivityResult =
      await (Connectivity().checkConnectivity()); // User defined class
      if (connectivityResult == ConnectivityResult.mobile ||
          connectivityResult == ConnectivityResult.wifi) {
        // Restart splash screen using GetX
        Get.offAll(() => SplashActivity());
      }
    }
  }

  Future<MobileInfoModel> postGetMobileAppAccountInfoCall(
      String acc_id, String user_id, BuildContext context) async {
    return await getMobileAppAccountInfoCall(acc_id, user_id, context);
  }



  checkMobileAppVersion(
      String acc_id, String version, BuildContext context) async {
    var connectivityResult =
    await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await checkAppVersion(acc_id, version, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        CheckAppVersionModel currentParsedResponse =
        CheckAppVersionModel.fromJson(responseJson);
        await PreferenceManagerUtil()
            .setStatus(flag);

        startTimeout(10000);
        return currentParsedResponse;
      }
      else{
        await PreferenceManagerUtil()
            .setStatus(0);
      }/*else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }*/
    } /*else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }*/
  }

  Future<CheckAppVersionModel> postCheckMobileAppVersion(
      String acc_id, String version, BuildContext context) async {
    return await checkMobileAppVersion(acc_id, version, context);
  }
}
