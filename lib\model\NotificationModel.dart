class NotificationModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  NotificationModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  NotificationModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _msgId;
  String? _notificationType;
  String? _subject;
  String? _sentBy;
  String? _messageSentOn;
  String? _sentOn;

  Data(
      {String? msgId,
        String? notificationType,
        String? subject,
        String? sentBy,
        String? messageSentOn,
        String? sentOn}) {
    if (msgId != null) {
      this._msgId = msgId;
    }
    if (notificationType != null) {
      this._notificationType = notificationType;
    }
    if (subject != null) {
      this._subject = subject;
    }
    if (sentBy != null) {
      this._sentBy = sentBy;
    }
    if (messageSentOn != null) {
      this._messageSentOn = messageSentOn;
    }
    if (sentOn != null) {
      this._sentOn = sentOn;
    }
  }

  String? get msgId => _msgId;
  set msgId(String? msgId) => _msgId = msgId;
  String? get notificationType => _notificationType;
  set notificationType(String? notificationType) =>
      _notificationType = notificationType;
  String? get subject => _subject;
  set subject(String? subject) => _subject = subject;
  String? get sentBy => _sentBy;
  set sentBy(String? sentBy) => _sentBy = sentBy;
  String? get messageSentOn => _messageSentOn;
  set messageSentOn(String? messageSentOn) => _messageSentOn = messageSentOn;
  String? get sentOn => _sentOn;
  set sentOn(String? sentOn) => _sentOn = sentOn;

  Data.fromJson(Map<String, dynamic> json) {
    _msgId = json['msg_id'];
    _notificationType = json['notification_type'];
    _subject = json['subject'];
    _sentBy = json['sent_by'];
    _messageSentOn = json['message_sent_on'];
    _sentOn = json['sent_on'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['msg_id'] = this._msgId;
    data['notification_type'] = this._notificationType;
    data['subject'] = this._subject;
    data['sent_by'] = this._sentBy;
    data['message_sent_on'] = this._messageSentOn;
    data['sent_on'] = this._sentOn;
    return data;
  }
}