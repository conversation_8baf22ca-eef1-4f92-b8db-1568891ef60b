import 'dart:convert';

import 'package:erpcacustomer/activity/DocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/DocumentDetailsSecondActivity.dart';
import 'package:erpcacustomer/activity/CoreDocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/OtherDocumentDetailsSecondActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:erpcacustomer/model/CustomerDocumentScreen1Model.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:erpcacustomer/controller/DocumentController.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';

import '../controller/CreateFolderFileController.dart';
import '../model/customer_create_folder_model.dart';
import '../model/customer_file_upload_model.dart';
import 'dart:io' as Io;

class DocumentsFragment extends StatefulWidget {
  DocumentsFragment({Key? key}) : super(key: key);

  @override
  DocumentsFragmentState createState() {
    return new DocumentsFragmentState();
  }
}

class DocumentsFragmentState extends State<DocumentsFragment> {
  // ✅ OPTIMIZED: GetX reactive variables for comprehensive state management
  var hasCardObs = false.obs;
  var showLoaderObs = false.obs;
  var selectedFileObs = ''.obs;
  var folderCreatingObs = false.obs;
  var fileUploadingObs = false.obs;
  var isLoadingObs = false.obs;

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  TextEditingController folderNameController = TextEditingController();
  TextEditingController fileNameController = TextEditingController();
  FilePickerResult? result;
  XFile? image;
  final ImagePicker _picker = ImagePicker();

  bool showloader = false;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }

  Future<CustomerCreateFolderModel> customerCreateFolder(
      String folderName, BuildContext context) async {
    print(
        "Values For Folder ${Constants.ACC_ID.toString()} ${Constants.USER_ID.toString()}");
    return await customerCreateFolderApi(Constants.ACC_ID.toString(),
        Constants.USER_ID.toString(), "0", folderName, context);
  }

  Future<CustomerFileUploadModel> customerFileUpload(
      String fileName, String fileToBeUploaded, BuildContext context) async {
    return await customerFileUploadApi(Constants.ACC_ID.toString(),
        Constants.USER_ID.toString(), fileName, "0", fileToBeUploaded, context);
  }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Stack(
        children: [
          _buildBackground(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Material(
      child: Stack(
        children: [
          FutureBuilder<CustomerDocumentScreen1Model>(
            future: customerDocumentScreen1Call(
                Constants.ACC_ID, Constants.USER_ID, context),
            builder: (context, snapshot) {
              print(
                  "Snapshot document ${Constants.ACC_ID} ${Constants.USER_ID}");
              print("Snapshot document ${snapshot.data}");
              return snapshot.hasData
                  ? Scaffold(
                      floatingActionButton: new FloatingActionButton(
                        heroTag: "plus",
                        backgroundColor:
                            Color(new CommonColor().erpca_blue_color),
                        onPressed: () {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              // return object of type Dialog
                              return AlertDialog(
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10),
                                ),
                                content: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(20),
                                  ),
                                  child: Column(
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          Navigator.pop(context);
                                          folderCreateBottomSheet();
                                        },
                                        borderRadius: BorderRadius.circular(
                                            8), // optional for ripple rounding
                                        child: Material(
                                          color: Colors.grey[
                                              200], // light background for button effect
                                          elevation: 2, // slight shadow
                                          borderRadius:
                                              BorderRadius.circular(8),
                                          child: Container(
                                            width: 300,
                                            padding: const EdgeInsets.all(12),
                                            child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(Icons.create_new_folder,
                                                    size: 20,
                                                    color: Colors.black87),
                                                const SizedBox(width: 8),
                                                Text(
                                                  'Create New Folder',
                                                  style: TextStyle(
                                                    color: Colors.black87,
                                                    fontWeight: FontWeight.w600,
                                                    fontSize: 15,
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),

                                      /* Material(
                                  child: InkWell(
                                    onTap: (){
                                      Navigator.pop(context);
                                      fileUploadBottomSheet();
                                    },
                                    child: Container(
                                      child: Padding(
                                        padding: const EdgeInsets.all(8.0),
                                        child: Row(
                                          children: [
                                            */ /*Container(
                                              margin: EdgeInsets.only(right: 5),
                                              child: SvgPicture.asset(
                                                'assets/images/customer_tab_icons/contacts.svg',
                                                height: 20,
                                              ),
                                            ),*/ /*
                                            Text(
                                              'Upload New File',
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontWeight: FontWeight.w500,
                                                fontSize: 15,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),*/
                                    ],
                                  ),
                                ),
                              );
                            },
                          );
                        }, // Switch tabs
                        child: new Icon(
                          Icons.add,
                          color: Colors.white,
                        ),
                      ),
                      body: Padding(
                          padding: const EdgeInsets.only(top: 20.0),
                          child: (snapshot.data!.data!.length > 0 ||
                                  snapshot.data!.othersFolders!.length > 0)
                              ? SingleChildScrollView(
                                  child: Column(
                                    children: [
                                      ListView.separated(
                                        physics: ClampingScrollPhysics(),
                                        shrinkWrap: true,
                                        separatorBuilder: (context, index) {
                                          return Padding(
                                            padding: const EdgeInsets.only(
                                                left: 13.0, right: 13),
                                            child: Divider(),
                                          );
                                        },
                                        padding: EdgeInsets.zero,
                                        itemCount: int.parse(snapshot
                                                .data!.data!.length
                                                .toString()) +
                                            int.parse(snapshot
                                                .data!.othersFolders!.length
                                                .toString()) +
                                            1,
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          return index <=
                                                  snapshot.data!.data!.length
                                              ? _gstBottomValue(index - 1,
                                                  snapshot.data!.data!)
                                              : _gstBottomValue1(
                                                  index -
                                                      snapshot
                                                          .data!.data!.length -
                                                      1,
                                                  snapshot
                                                      .data!.othersFolders!);
                                        },
                                      ),
                                      Obx(() => showLoaderObs.value == true
                                        ? Center(
                                            child: Container(
                                                child: new MyUtils()
                                                    .kLoadingWidget(context)))
                                        : Container()),
                                    ],
                                  ),
                                )
                              : Center(
                                  child:
                                      Text(snapshot.data!.message.toString()))),
                    )
                  : Center(
                      child: Container(
                          child: new MyUtils().kLoadingWidget(context)));
            },
          ),
        ],
      ),
    );
  }

  postCustomerDocumentScreen1Call(
      String acc_id, String user_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await customerDocumentScreen1Api(acc_id, user_id, context);
      int flag = responseJson["success"];
      print("Response json docs ${responseJson}");

      if (flag == 1) {
        CustomerDocumentScreen1Model currentParsedResponse =
            CustomerDocumentScreen1Model.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      //  MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<CustomerDocumentScreen1Model> customerDocumentScreen1Call(
      String acc_id, String user_id, BuildContext context) async {
    return await postCustomerDocumentScreen1Call(acc_id, user_id, context);
  }

  Widget _gstBottomValue(int index, List<Data> data) {
    if (index == -1) {
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => CoreDocumentDetailsActivity()),
          );
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 13.0, right: 13, top: 1, bottom: 10),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.folder,
                        color: Colors.amber,
                        size: 50,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Core Document(s)",
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),
                            Text(
                              "Eg. Pan card, NDA, MOU, LOE, etc.",
                              style: new CSSStyle()
                                  .poppinsLightGreyRegular14(context),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  /*Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                  ),*/
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      Data allData = data[index];
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => DocumentDetailsSecondActivity(
                      asst_year: allData.asstYear,
                      asst_year_Display: allData.displayAsstYear,
                    )),
          );
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                left: 13.0,
                right: 13,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.folder,
                        color: Colors.amber,
                        size: 50,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              allData.displayAsstYear.toString(),
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),
                            Text(
                              allData.availableDocuments.toString() +
                                  " Document(s)",
                              style: new CSSStyle()
                                  .poppinsLightGreyRegular14(context),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  /*Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                  ),*/
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  Widget _gstBottomValue1(int index, List<OthersFolders> data) {
    if (index == -1) {
      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => CoreDocumentDetailsActivity()),
          );
        },
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 13.0, right: 13, top: 1, bottom: 10),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.folder,
                        color: Colors.amber,
                        size: 50,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Core Document(s)",
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),
                            Text(
                              "Eg. Pan card, NDA, MOU, LOE, etc.",
                              style: new CSSStyle()
                                  .poppinsLightGreyRegular14(context),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  /*  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                  ),*/
                ],
              ),
            ),
          ],
        ),
      );
    } else {
      OthersFolders allData = data[index];

      return InkWell(
        onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => OtherDocumentDetailsSecondActivity(
                      accId: Constants.ACC_ID,
                      userId: Constants.USER_ID,
                      folderId: allData.id,
                      folderName: allData.folderName,
                    )),
          );
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                left: 13.0,
                right: 13,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.folder,
                        color: Colors.amber,
                        size: 50,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              allData.folderName.toString(),
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),
                            /*  Text(
                              allData.availableDocuments + " Document(s)",
                              style: new CSSStyle()
                                  .poppinsLightGreyRegular14(context),
                            ),*/
                          ],
                        ),
                      ),
                    ],
                  ),
                  /*Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.grey,
                  ),*/
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  folderCreateBottomSheet() {
    return showModalBottomSheet(
      context: context,
      builder: (context) {
        // ✅ OPTIMIZED: Removed StatefulBuilder to prevent continuous refreshing
        return SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Container(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          child: Row(
                            children: [
                              Container(
                                child: const Icon(
                                  Icons.arrow_back_ios_new_outlined,
                                  color: Colors.blue,
                                  size: 20,
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 20),
                                child: Text(
                                  'Create Folder',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: Container(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 10),
                          child: TextFormField(
                            controller: folderNameController,
                            autofocus: false,
                            decoration: InputDecoration(
                              hintText: 'Folder Name',
                              hintStyle: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w200,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Container(
                        margin: const EdgeInsets.only(top: 25),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            InkWell(
                              onTap: () async {
                                if (folderNameController.text.isNotEmpty) {
                                  // ✅ OPTIMIZED: GetX reactive state management with enhanced loader
                                  folderCreatingObs.value = true;
                                  showLoaderObs.value = true;
                                  showloader = true; // Keep for compatibility
                                  Navigator.pop(context);

                                  try {
                                    CustomerCreateFolderModel response =
                                        await customerCreateFolder(
                                            folderNameController.text.toString(),
                                            context);

                                    if (response.success == 1) {
                                      // ✅ OPTIMIZED: Success state management
                                      folderCreatingObs.value = false;
                                      showLoaderObs.value = false;
                                      showloader = false; // Keep for compatibility
                                      folderNameController.text = "";
                                      MyUtils.showOkDialog(context, "Success",
                                          "Folder created successfully");
                                    } else {
                                      // ✅ OPTIMIZED: Error state management
                                      folderCreatingObs.value = false;
                                      showLoaderObs.value = false;
                                      showloader = false; // Keep for compatibility
                                      MyUtils.showOkDialog(context, "Error",
                                          response.message.toString());
                                    }
                                  } catch (e) {
                                    // ✅ OPTIMIZED: Exception handling
                                    folderCreatingObs.value = false;
                                    showLoaderObs.value = false;
                                    showloader = false; // Keep for compatibility
                                    MyUtils.showOkDialog(context, "Error",
                                        "Failed to create folder");
                                  }
                                } else {
                                  MyUtils.showOkDialog(context, "Error",
                                      "Please enter the folder name");
                                }

                                //navigationService.pushNamed(breakTimer);
                              },
                              child: Container(
                                child: Text(
                                  'Submit',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                //Navigator.pop(context);
                                Navigator.pop(context);
                              },
                              child: Container(
                                margin: const EdgeInsets.only(left: 25),
                                child: Text(
                                  'Cancel',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      ),
    );
  }

  fileUploadBottomSheet() {
    return showModalBottomSheet(
      context: context,
      builder: (context) {
        // ✅ OPTIMIZED: Removed StatefulBuilder to prevent continuous refreshing
        return SingleChildScrollView(
              scrollDirection: Axis.vertical,
              child: Container(
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          child: Row(
                            children: [
                              Container(
                                child: const Icon(
                                  Icons.arrow_back_ios_new_outlined,
                                  color: Colors.blue,
                                  size: 20,
                                ),
                              ),
                              Container(
                                margin: const EdgeInsets.only(left: 20),
                                child: Text(
                                  'Upload File',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 15,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            bottom: MediaQuery.of(context).viewInsets.bottom),
                        child: Container(
                          padding: const EdgeInsets.only(
                              left: 10, right: 10, top: 10),
                          child: TextFormField(
                            controller: fileNameController,
                            autofocus: false,
                            decoration: InputDecoration(
                              hintText: 'File Name',
                              hintStyle: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w200,
                              ),
                            ),
                          ),
                        ),
                      ),
                      Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                top: 10, bottom: 13, right: 20, left: 20),
                            child: InkWell(
                              onTap: () {
                                Navigator.pop(context);
                                showUploadOptions();
                                print("Result File $result");
                                // GetX reactive update - NO setState needed!
                              },
                              child: Container(
                                //margin: EdgeInsets.only(top: 20),
                                padding: EdgeInsets.symmetric(
                                    horizontal: 15, vertical: 8),
                                decoration: BoxDecoration(
                                    color: Colors.white12,
                                    borderRadius: BorderRadius.circular(5),
                                    border: Border.all(
                                      color: Colors.blue,
                                    )),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Container(
                                      child: Icon(
                                        Icons.file_upload_outlined,
                                        color: Colors.blue,
                                      ),
                                    ),
                                    Container(
                                      child: Text(
                                        'Upload Document',
                                        style: TextStyle(
                                          color: Colors.blue,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
      },
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20), topRight: Radius.circular(20)),
      ),
    );
  }

  showUploadOptions() async {
    await showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        builder: (context) {
          return SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Container(
                margin: EdgeInsets.only(bottom: 10),
                child: Column(
                  children: [
                    InkWell(
                      onTap: () async {
                        Navigator.pop(context);
                        if (fileNameController.text.isEmpty) {
                          MyUtils.showOkDialog(
                              context, 'Error', "Please Enter Document Title");
                        } else {
                          // ✅ OPTIMIZED: GetX reactive state management with loader
                          fileUploadingObs.value = true;
                          showLoaderObs.value = true;

                          try {
                            final XFile? image = await _picker.pickImage(
                                source: ImageSource.camera);
                            if (image == null) {
                              // ✅ OPTIMIZED: Reset loader on cancellation
                              fileUploadingObs.value = false;
                              showLoaderObs.value = false;
                              MyUtils.showOkDialog(
                                  context, 'Error', "No file Selected");
                            } else {
                              var file_name = image.path;
                              final bytes =
                                  Io.File(file_name.toString()).readAsBytesSync();
                              String img64 = base64Encode(bytes);

                              CustomerFileUploadModel? response =
                                  await customerFileUpload(
                                      fileNameController.text, img64, context);

                              // ✅ OPTIMIZED: Reset loader after upload
                              fileUploadingObs.value = false;
                              showLoaderObs.value = false;

                              if (response.success == 1) {
                                print("File uploaded successfully");
                                fileNameController.text = "";
                                MyUtils.showOkDialog(context, "Success",
                                    "File uploaded successfully");
                              } else {
                                print("File upload failed");
                                MyUtils.showOkDialog(context, "Error",
                                    response.message.toString());
                              }
                            }
                          } catch (e) {
                            // ✅ OPTIMIZED: Exception handling
                            fileUploadingObs.value = false;
                            showLoaderObs.value = false;
                            MyUtils.showOkDialog(context, "Error",
                                "Failed to upload file");
                          }
                        }
                        /*setState(() {});*/
                        print("Result File $result");
                      },
                      child: Container(
                        child: Text(
                          'Upload from camera',
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                    Divider(),
                    InkWell(
                      onTap: () async {
                        Navigator.pop(context);
                        file_browse();
                        var status = await Permission.storage.status;
                        print('file permission status $status');
                        /*if (status == PermissionStatus.denied) {
                          return MyUtils.showOkDialog(context, "Error", "Please Enable Permission");
                        } else {
                          file_browse();
                        }*/
                      },
                      child: Container(
                        child: Text(
                          'Choose from files',
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w500),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  void file_browse() async {
    if (fileNameController.text.isEmpty) {
      MyUtils.showOkDialog(context, 'Error', "Please Enter Document Title");
    } else {
      // ✅ OPTIMIZED: Set loader state BEFORE file picking
      fileUploadingObs.value = true;
      showLoaderObs.value = true;

      try {
        result = await FilePicker.platform.pickFiles(allowMultiple: true);
        if (result == null) {
          // ✅ OPTIMIZED: Reset loader on cancellation
          fileUploadingObs.value = false;
          showLoaderObs.value = false;
          MyUtils.showOkDialog(context, 'Error', "No file Selected");
        } else {
          // ✅ OPTIMIZED: Use for loop instead of forEach to avoid race conditions
          for (var element in result!.files) {
            try {
              var file_name = element.path;
              final bytes = Io.File(file_name.toString()).readAsBytesSync();
              String img64 = base64Encode(bytes);

              CustomerFileUploadModel response = await customerFileUpload(
                  fileNameController.text, img64, context);

              if (response.success == 1) {
                print("File uploaded successfully: ${element.name}");
              } else {
                print("File upload failed: ${element.name}");
                MyUtils.showOkDialog(context, "Error", response.message.toString());
                break; // Stop uploading on error
              }
            } catch (e) {
              print("Error uploading file: ${element.name}");
              MyUtils.showOkDialog(context, "Error", "Failed to upload ${element.name}");
              break; // Stop uploading on error
            }
          }

          // ✅ OPTIMIZED: Reset loader and clear filename after all uploads
          fileUploadingObs.value = false;
          showLoaderObs.value = false;
          fileNameController.text = "";
          MyUtils.showOkDialog(context, "Success", "File(s) uploaded successfully");
        }
      } catch (e) {
        // ✅ OPTIMIZED: Exception handling
        fileUploadingObs.value = false;
        showLoaderObs.value = false;
        MyUtils.showOkDialog(context, "Error", "Failed to pick files");
      }
    }
  }
}
