{"buildCommand": "prepareForIndexing", "configuredTargets": [{"guid": "c2a7b90ce94c18601910ebe2edd7952788a783a885d8b0b3beb2e9f90bde3f49"}], "continueBuildingAfterErrors": true, "enableIndexBuildArena": false, "hideShellScriptEnvironment": false, "parameters": {"action": "build", "activeArchitecture": "arm64", "activeRunDestination": {"disableOnlyActiveArch": false, "platform": "iphoneos", "sdk": "iphoneos14.5", "sdkVariant": "iphoneos", "supportedArchitectures": ["arm64v8", "arm64", "armv8"], "targetArchitecture": "arm64"}, "arenaInfo": {"buildIntermediatesPath": "", "buildProductsPath": "", "derivedDataPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData", "indexDataStoreFolderPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-fqzodtspbxgeuiasyculmqoviasd/Index/DataStore", "indexEnableDataStore": true, "indexPCHPath": "/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-fqzodtspbxgeuiasyculmqoviasd/Index/PrecompiledHeaders", "pchPath": ""}, "configurationName": "Debug", "overrides": {"synthesized": {"table": {"ASSETCATALOG_FILTER_FOR_DEVICE_MODEL": "iPhone8,1", "ASSETCATALOG_FILTER_FOR_DEVICE_OS_VERSION": "14.7.1", "BUILD_ACTIVE_RESOURCES_ONLY": "YES", "ENABLE_PREVIEWS": "NO", "TARGET_DEVICE_IDENTIFIER": "00682015872f06d88f33a7645675e0bfce79b804", "TARGET_DEVICE_MODEL": "iPhone8,1", "TARGET_DEVICE_OS_VERSION": "14.7.1", "TARGET_DEVICE_PLATFORM_NAME": "iphoneos"}}}}, "schemeCommand": "launch", "shouldCollectMetrics": false, "showNonLoggedProgress": true, "useDryRun": false, "useImplicitDependencies": true, "useLegacyBuildLocations": false, "useParallelTargets": true}