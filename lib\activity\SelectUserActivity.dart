import 'dart:convert';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/CommonText.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/NotificationBadge.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/common/User.dart';
import 'package:erpcacustomer/controller/LoginController.dart';
import 'package:erpcacustomer/model/ValidateCustomerMobileModel.dart';
import 'package:erpcacustomer/model/push_notification.dart';

//import 'package:firebase_core/firebase_core.dart';
//import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:get/get.dart';
import '../utils/navigation_helper.dart';

import 'NewServicesActivity.dart';

class SelectUserActivity extends StatefulWidget {
  String? mobile_Otp;

  SelectUserActivity({Key? key, this.mobile_Otp}) : super(key: key);

  @override
  SelectUserActivityState createState() => SelectUserActivityState();
}

class SelectUserActivityState extends State<SelectUserActivity> {
  // ✅ OPTIMIZED: GetX reactive variables for persistent user selection
  // These variables automatically save/restore user selection state
  var isLoadingObs = false.obs;
  var selectedUserObs = Rxn<Data>();        // Selected user object
  var selectedUser1Obs = ''.obs;            // Selected user business name
  var idObs = 1.obs;                        // Selected user ID
  var checkedValueObs = false.obs;

  // Original variables (keeping for compatibility)
  bool _saving = false;
  String myToken = '';
  List _selectedIndex = [];
  var checkedValue = false;
  final GlobalKey<NavigatorState> navigatorKey =
  GlobalKey(debugLabel: "Main Navigator");
  TextEditingController controller1 = new TextEditingController();
  TextEditingController controllerConfirm1 = new TextEditingController();
  TextEditingController controller2 = new TextEditingController();
  TextEditingController controllerConfirm2 = new TextEditingController();
  TextEditingController controller3 = new TextEditingController();
  TextEditingController controllerConfirm3 = new TextEditingController();
  TextEditingController controller4 = new TextEditingController();
  TextEditingController controllerConfirm4 = new TextEditingController();
  TextEditingController controller5 = new TextEditingController();
  TextEditingController controllerConfirm5 = new TextEditingController();
  TextEditingController currController = new TextEditingController();
  TextEditingController currConfirmController = new TextEditingController();
  List<User> userData = [];
  var _form2Key = GlobalKey<FormState>();
  String userName = "";
  String password = "";
  //FirebaseMessaging firebaseMessaging = new FirebaseMessaging();
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
  new FlutterLocalNotificationsPlugin();
  Map<String, String> headerHttp = new Map();
  bool hasPinError = false;
  bool hasConfirmPinError = false;
  Data? selectedUser;
  String? selectedUser1;
  int id = 1;
  String fcmId = "";
  PushNotification? _notificationInfo;
  int? _totalNotifications;


  // TextEditingController userNameController = TextEditingController();
  // TextEditingController passwordController = TextEditingController();
  //FirebaseApp firebaseApp;
  var _formKey = GlobalKey<FormState>();
  bool _isLoggedIn = false;
  String googleuserName = "";
  TextEditingController mPincontroller = TextEditingController();
  TextEditingController mConfirmPincontroller = TextEditingController();
  String mPin = "";
  String confirmMPin = "";
  String mainUser = "";
  final _scrollController = ScrollController(initialScrollOffset: 0);

  @override
  void dispose() {
    super.dispose();
    controller1.dispose();
    controllerConfirm1.dispose();
    controller2.dispose();
    controllerConfirm2.dispose();
    controller3.dispose();
    controllerConfirm3.dispose();
    controller4.dispose();
    controllerConfirm4.dispose();
    controller5.dispose();
    controllerConfirm5.dispose();
    // controller6.dispose();
  }



  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    // ✅ OPTIMIZED: Initialize GetX reactive variables
    _initializeSelectedUser();
    // createRadioListUsers();
    //new PreferenceManagerUtil().setBuisnessName("");
    _totalNotifications = 0;
    /*_messaging.configure(
        onMessage: (Map<String, dynamic> message) async {

      _showNotification(1234, "GET title FROM message OBJECT", "GET description FROM message OBJECT", "GET PAYLOAD FROM message OBJECT");
      return;
    }
    );*/
    // For handling notification when the app is in background
    // but not terminated
    Future<String> token = new PreferenceManagerUtil().getMainUser();
    token.then((value) {
      mainUser = value;
    });
    currController = controller1;
    currConfirmController = controllerConfirm1;
  }

  // ✅ ADDED: Method to initialize selected user from preferences
  void _initializeSelectedUser() async {
    try {
      // ✅ Load selected user business name
      String selectedBusinessName = await PreferenceManagerUtil().getBuisnessName();
      if (selectedBusinessName.isNotEmpty) {
        selectedUser1 = selectedBusinessName;
        selectedUser1Obs.value = selectedBusinessName;
        print('✅ Selected user business name loaded: $selectedBusinessName');
      }

      // ✅ Load selected user ID
      String selectedUserId = await PreferenceManagerUtil().getUserId();
      if (selectedUserId.isNotEmpty) {
        id = int.parse(selectedUserId);
        idObs.value = int.parse(selectedUserId);
        print('✅ Selected user ID loaded: $selectedUserId');
      }

      print('✅ User selection initialized from preferences');
    } catch (e) {
      print('❌ Error initializing selected user: $e');
    }
  }

  // ✅ ADDED: Method to clear user selection
  void clearUserSelection() {
    selectedUser1Obs.value = '';
    selectedUserObs.value = null;
    idObs.value = 1;

    selectedUser1 = '';
    selectedUser = null;
    id = 1;

    // Clear from preferences
    PreferenceManagerUtil().setBuisnessName('');
    PreferenceManagerUtil().setUserId('');

    print('✅ User selection cleared');
  }


  void inputTextToField(String str) {
    //Edit first textField
    if (currController == controller1) {
      controller1.text = str;
      currController = controller2;
    }

    //Edit second textField
    else if (currController == controller2) {
      controller2.text = str;
      currController = controller3;
    }

    //Edit third textField
    else if (currController == controller3) {
      controller3.text = str;
      currController = controller4;
    }

    //Edit fourth textField
    else if (currController == controller4) {
      controller4.text = str;
      currController = controller5;
    }

    //Edit fifth textField
/*    else if (currController == controller5) {
      controller5.text = str;
      currController = controller6;
    }

    //Edit sixth textField
    else if (currController == controller6) {
      controller6.text = str;
      currController = controller6;
    }*/
  }

  void inputTextToFieldConfirm(String str) {
    //Edit first textField
    if (currController == controllerConfirm1) {
      controllerConfirm1.text = str;
      currConfirmController = controllerConfirm2;
    }

    //Edit second textField
    else if (currConfirmController == controllerConfirm2) {
      controllerConfirm2.text = str;
      currConfirmController = controllerConfirm3;
    }

    //Edit third textField
    else if (currConfirmController == controllerConfirm3) {
      controllerConfirm3.text = str;
      currConfirmController = controllerConfirm4;
    }

    //Edit fourth textField
    else if (currConfirmController == controllerConfirm4) {
      controllerConfirm4.text = str;
      currConfirmController = controllerConfirm5;
    }

    //Edit fifth textField
/*    else if (currController == controller5) {
      controller5.text = str;
      currController = controller6;
    }

    //Edit sixth textField
    else if (currController == controller6) {
      controller6.text = str;
      currController = controller6;
    }*/
  }

  void deleteText() {
    if (currController.text.length == 0) {
    } else {
      currController.text = "";
      currController = controller4;
      return;
    }

    if (currController == controller1) {
      controller1.text = "";
    } else if (currController == controller2) {
      controller1.text = "";
      currController = controller1;
    } else if (currController == controller3) {
      controller2.text = "";
      currController = controller2;
    } else if (currController == controller4) {
      controller3.text = "";
      currController = controller3;
    } else if (currController == controller5) {
      controller4.text = "";
      currController = controller4;
    }
    /* else if (currController == controller6) {
      controller5.text = "";
      currController = controller5;
    }*/
  }

  void deleteConfirmText() {
    if (currConfirmController.text.length == 0) {
    } else {
      currConfirmController.text = "";
      currConfirmController = controllerConfirm4;
      return;
    }

    if (currConfirmController == controllerConfirm1) {
      controllerConfirm1.text = "";
    } else if (currConfirmController == controllerConfirm2) {
      controllerConfirm1.text = "";
      currConfirmController = controllerConfirm1;
    } else if (currConfirmController == controllerConfirm3) {
      controllerConfirm2.text = "";
      currConfirmController = controllerConfirm2;
    } else if (currConfirmController == controllerConfirm4) {
      controllerConfirm3.text = "";
      currConfirmController = controllerConfirm3;
    } else if (currConfirmController == controllerConfirm5) {
      controllerConfirm4.text = "";
      currConfirmController = controllerConfirm4;
    }
    /* else if (currController == controller6) {
      controller5.text = "";
      currController = controller5;
    }*/
  }

  String? _validateUserName(String? user_name) {
    if (user_name!.isEmpty) {
      return CommonText().user_name;
    } else {
      userName = user_name;
      return null; // Valid input
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> widgetList = [
      Padding(
        padding: EdgeInsets.only(left: 0.0, right: 2.0),
        child: new Container(
          color: Colors.transparent,
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
            height: 1,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(width: 1.0, color: Colors.white),
              ),
            ),
            child: new TextField(
              inputFormatters: [
                LengthLimitingTextInputFormatter(1),
              ],
              enabled: false,
              controller: controller1,
              obscureText: true,
              autofocus: false,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 24.0, color: Colors.white),
            )),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(width: 1.0, color: Colors.white),
            ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            controller: controller2,
            obscureText: true,
            autofocus: false,
            enabled: false,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 24.0, color: Colors.white),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(width: 1.0, color: Colors.white),
            ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            keyboardType: TextInputType.number,
            controller: controller3,
            obscureText: true,
            textAlign: TextAlign.center,
            autofocus: false,
            enabled: false,
            style: TextStyle(fontSize: 24.0, color: Colors.white),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(width: 1.0, color: Colors.white),
            ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            textAlign: TextAlign.center,
            controller: controller4,
            obscureText: true,
            autofocus: false,
            enabled: false,
            style: TextStyle(fontSize: 24.0, color: Colors.white),
          ),
        ),
      ),
    ];

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Color(new CommonColor().red_Color),
      appBar: AppBar(
        elevation: 0,
        leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.black,
            ),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          "",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().white_Color),
      ),
      body: FutureBuilder<ValidateCustomerMobileModel>(
        future: verifyLogin(Constants.ACC_ID, '', "0", context),
        builder: (context, snapshot) {
          print('snapshot data ${snapshot.data}');
          if(snapshot.hasError){
            print('snapsshot error ${snapshot.error}');
          }
          if (snapshot.hasData) {
            return Stack(
              children: [
                Image.asset(
                  'assets/images/select_user_background.png',
                  width: double.infinity,
                  fit: BoxFit.fill,
                ),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: <Widget>[
                    Image.asset('assets/images/user.png',
                        width: 100.0, height: 100.0),
                    Padding(
                      padding: const EdgeInsets.only(top: 15.0),
                      child: Text(
                        "Welcome!",
                        style: new CSSStyle().poppinsBlackRegular15(context),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        mainUser,
                        style: new CSSStyle().poppinsBlackRegular20(context),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Padding(
                      padding:
                      const EdgeInsets.only(left: 15, right: 15, top: 15),
                      child: Text(
                        "Below are the list of your business(s) registered with us.",
                        textAlign: TextAlign.center,
                        style:
                        new CSSStyle().poppinsLightBlackRegular16(context),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        height: 250,
                        child: Scrollbar(
                          controller:
                          _scrollController, // <---- Here, the controller

                          thumbVisibility: true,
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            child: Container(
                              // margin: EdgeInsets.only(top: 40,),
                              child: Column(
                                children: [
                                  Column(
                                    children: getData(snapshot.data!.data!),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    ClipOval(
                      child: Material(
                        color: Color(
                            CommonColor().erpca_blue_color), // button color
                        child: InkWell(
                          splashColor: Color(
                              CommonColor().erpca_blue_color), // inkwell color
                          child: SizedBox(
                              width: 56,
                              height: 56,
                              child: Icon(Icons.arrow_forward,
                                  color: Color(CommonColor().white_Color))),
                          onTap: () {
                            Future<String> token =
                            new PreferenceManagerUtil().getBuisnessName();
                            token.then((value) {
                              if (selectedUser1!= "") {
                                // Use NavigationHelper for clean navigation
                                NavigationHelper.toHome();
                              } else {
                                MyUtils.showOkDialog(
                                    context, "Sorry", "Please select company");
                              }
                            });
                          },
                        ),
                      ),
                    ),
                    Padding(
                      padding:
                      const EdgeInsets.only(left: 25, right: 25, top: 15),
                      child: Text(
                        "*We detect your services and profile data based on your \nmobile number registered with us.",
                        textAlign: TextAlign.center,
                        style: new CSSStyle().poppinsBlackRegular10(context),
                      ),
                    ),
                  ],
                ),
              ],
            );
          } else {
            return Center(
                child: Container(
                    color: Colors.white,
                    child: new MyUtils().kLoadingWidget(context)));
          }
        },
      ),
    );
  }

  setSelectedUser(Data user) {
    // ✅ OPTIMIZED: GetX reactive state management
    selectedUserObs.value = user;
    idObs.value = int.parse(user.userId.toString());

    // Keep original variables for compatibility
    selectedUser = user;
    id = int.parse(user.userId.toString());

    // Store user preferences (no setState needed)
    new PreferenceManagerUtil()
        .setProposerDisplayName(user.clientName.toString());
    new PreferenceManagerUtil()
        .setBuisnessName(user.clientBusinessName.toString());
    new PreferenceManagerUtil().setProfilePic(user.photoThumb.toString());
    new PreferenceManagerUtil().setUserId(user.userId.toString());
    Constants.USER_ID = user.userId.toString();

    // ✅ No setState needed - GetX handles reactive updates automatically
    // setState(() { ... }); // Removed
  }

  List<Widget>? createRadioListUsers() {
    String role = "";

    Future<String> token = new PreferenceManagerUtil().getListOfCompany();

    token.then((val) {
      Map<String, dynamic> map = jsonDecode(val);
      User user = User(map['clientBusinessName'], map['clientName'],
          map['customerGroup'], map['userId']);

      userData.add(user);
    });

    return null; // This function appears to be async setup, returning null for now
  }

  List<Widget> getData(List<Data> users) {
    print('get data callled');
    //print('selected user ${selectedUser!.clientBusinessName.toString()}');

    // ✅ FIXED: Find and set selected user object based on saved business name
    if (selectedUser == null && selectedUser1Obs.value.isNotEmpty) {
      for (Data user in users) {
        if (user.clientBusinessName.toString() == selectedUser1Obs.value) {
          selectedUser = user;
          selectedUserObs.value = user;
          print('✅ Found and set selected user: ${user.clientBusinessName}');
          break;
        }
      }
    }

    // ✅ DEBUG: Log current selection state
    print('🔍 Current selection state:');
    print('   selectedUser1Obs.value: ${selectedUser1Obs.value}');
    print('   selectedUser: ${selectedUser?.clientBusinessName ?? "null"}');
    print('   idObs.value: ${idObs.value}');

    List<Widget> widgets = [];
    bool selectedData = false;

    for (int j = 0; j < users.length; j++) {
      Data user = users[j];
      print('selected userr ${user}');
      if (selectedUser != null && user.userId == selectedUser!.userId) {
        selectedData = true;
        print('selected User${selectedUser}');

      } else {
        print('selected data ${selectedData}');
        selectedData = false;
      }

      _selectedIndex.add(0);
      widgets.add(
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [

            Obx(() => RadioListTile(
              // / dense: true,
              value: user.clientBusinessName.toString(),
              groupValue: selectedUser1Obs.value,  // ✅ OPTIMIZED: GetX reactive variable
              // fillColor: MaterialStateColor.resolveWith((states) =>  Colors.blue),

              title: Text(
                user.clientBusinessName.toString(),
                style: new CSSStyle().poppinsBlackRegular14(context),
              ),
              onChanged: (value) {
                //  print('current user $value');
                //  print('current user${currentUser!.clientBusinessName.toString()}');
                // ✅ OPTIMIZED: GetX reactive state management
                print('users[j] ${user.clientBusinessName.toString()}');
                selectedUser1Obs.value = value.toString();
                idObs.value = int.parse(user.userId.toString());
                selectedUserObs.value = user; // ✅ ADDED: Set selected user object

                // Keep original variables for compatibility
                selectedUser1 = value.toString();
                id = int.parse(user.userId.toString());
                selectedUser = user; // ✅ ADDED: Set selected user object

                // Store user preferences (no setState needed)
                new PreferenceManagerUtil()
                    .setProposerDisplayName(user.clientName.toString());
                new PreferenceManagerUtil()
                    .setBuisnessName(user.clientBusinessName.toString());
                new PreferenceManagerUtil().setProfilePic(user.photoThumb.toString());
                new PreferenceManagerUtil().setUserId(user.userId.toString());
                Constants.USER_ID = user.userId.toString();

                // ✅ No setState needed - GetX handles reactive updates automatically
                // setState(() { ... }); // Removed
              },
              toggleable: true,
              selected: selectedData,
              activeColor: Colors.blue,
            )),  // ✅ FIXED: Close Obx widget properly
            /*  Container(
              margin: EdgeInsets.only(top: 30),
              child: Align(
                alignment: Alignment.bottomLeft,
                child: Row(
                  children: [
                    if (_selectedIndex[0] == 1)
                      Container(
                        margin: EdgeInsets.only(left: 7, top: 9),
                        child: Icon(
                          Icons.check_outlined,
                          size: 28,
                          color: Colors.blue,
                        ),
                      ),
                    InkWell(
                      onTap: (){
                        for()
                      },
                      child: Text(
                        user.clientBusinessName.toString(),
                        style: new CSSStyle().poppinsBlackRegular14(context),
                      ),
                    ),
                  ],
                ),
              ),
                  ),*/
          ],
        ),
      );
    }
    return widgets;
  }


  /*Future<ValidateCustomerMobileModel>? verifyLogin(String acc_id, String fcm_id,
      String validate, BuildContext context) async {
    String mobile_no = "";
    Future<String> token = new PreferenceManagerUtil().getMobile();
    await token.then((value) {
      mobile_no = value;
      print('mobile number $mobile_no');
    });
    Future.delayed(Duration(seconds: 1), () async {
      var connectivityResult =
      await (Connectivity().checkConnectivity()); // User defined class
      if (connectivityResult == ConnectivityResult.mobile ||
          connectivityResult == ConnectivityResult.wifi) {
        Map<String, dynamic> responseJson =
        await callLoginApi1(acc_id, mobile_no, fcm_id, validate, context);
        print('response json ${responseJson}');
        int status = responseJson['success'];

        if (status != 1) {
          MyUtils.showOkDialog(
              context, "Error", responseJson['message'].toString());
          throw 'api error';
        } else {
          print('inside else');
          ValidateCustomerMobileModel parsedResponse =
          ValidateCustomerMobileModel.fromJson(responseJson);
          print('parsed response ${parsedResponse.data}');
          return await parsedResponse;
        }
      } else {
        MyUtils.showOkDialog(context, "No Internet", "Check your connection");
        throw 'no internet error';
        // MyUtils.showToast("check your connection");
      }
    });
  }*/


  Future<ValidateCustomerMobileModel> verifyLogin(String acc_id, String fcm_id,
      String validate, BuildContext context) async {
    String mobile_no = "";
    Future<String> token = new PreferenceManagerUtil().getMobile();
    await token.then((value) {
      mobile_no = value;
      print('mobile number $mobile_no');
    });
    return await callLoginApi(acc_id, mobile_no, fcm_id, validate, context);



  }
  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
