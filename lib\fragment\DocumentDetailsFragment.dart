import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/controller/DocumentController.dart';
import 'package:erpcacustomer/model/DocumentListModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/controller/TaskController.dart';
import 'package:flutter_downloader/flutter_downloader.dart';

class DocumentDetailsFragment extends StatefulWidget {
  String? taskId = "";

  DocumentDetailsFragment({Key? key, this.taskId}) : super(key: key);

  @override
  DocumentDetailsFragmentState createState() {
    return new DocumentDetailsFragmentState();
  }
}

class DocumentDetailsFragmentState extends State<DocumentDetailsFragment> {
  // GetX reactive variables
  var hasCardObs = false.obs;
  var isLoadingObs = false.obs;

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    _media = MediaQuery.of(context).size;

    children.add(_buildBackground());
    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
          primaryColor: PrimaryColor, fontFamily: 'GlacialIndifference'),
      home: Stack(
        children: children,
      ),
    );
  }

  Widget _buildBackground() {
    return Stack(children: <Widget>[
      //Above card
      NotificationListener<OverscrollIndicatorNotification>(
        onNotification: (overscroll) {
          overscroll.disallowIndicator();
          return true;
        },
        child: FutureBuilder<DocumentListModel>(
          future: postDocRequiredCall(
              Constants.ACC_ID, Constants.USER_ID, widget.taskId!, context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? ListView.separated(
                    physics: ClampingScrollPhysics(),
                    shrinkWrap: true,
                    separatorBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 85.0),
                        child: Divider(),
                      );
                    },
                    padding: EdgeInsets.zero,
                    itemCount: snapshot.data!.data!.length,
                    itemBuilder: (BuildContext context, int index) {
                      return _gstBottomValue(snapshot.data!.data![index]);
                    },
                  )
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
      ),
      // Positioned to take only AppBar size
    ]);
  }

   DocRequiredCall(String acc_id, String user_id,
      String taskId, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await postDocRequiredApi(acc_id, user_id, taskId, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        DocumentListModel currentParsedResponse =
            DocumentListModel.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<DocumentListModel> postDocRequiredCall(String acc_id, String user_id,
      String taskId, BuildContext context) async {
    return await DocRequiredCall(acc_id, user_id, taskId, context);
  }

  Widget _gstBottomValue(Data data) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        color: Color(new CommonColor().white_Color),
        elevation: 3,
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 15, right: 13),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      data.docTime.toString(),
                      style: new CSSStyle().poppinsBlackRegular20(context),
                    ),
                    Container(
                        decoration: new BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.rectangle,
                          borderRadius: BorderRadius.circular(30),
                        ),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 15.0, right: 15, top: 5, bottom: 5),
                          child: Text(
                            "N/A",
                            style:
                                new CSSStyle().poppinsWhiteRegular12(context),
                          ),
                        ))
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 15.0, top: 15),
                child: Text(
                  data.uploadedOn.toString(),
                  style: new CSSStyle().poppinsBlackRegular12(context),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 15),
                child: Row(
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      color: Colors.red,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text(
                        "Aadhar Card for the GST-3B filing",
                        style: new CSSStyle().poppinsBlackRegular12(context),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    left: 13.0, top: 15, right: 13, bottom: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                        child: Text(
                          "Download",
                          style: new CSSStyle().poppinsBlueRegular12(context),
                        ),
                        onTap: () async {
                          MyUtils().getDownloadUrl1(data.filePath!);


                        }),
                    Text(
                      "",
                      style: new CSSStyle().poppinsRedRegular12(context),
                    ),
                  /*  GestureDetector(
                      onTap: () async {
                        final tasks = await FlutterDownloader.loadTasks();

                        if (tasks.length > 0) {
                          int urlMatchCount = -1;
                          for (int i = 0; i < tasks.length; i++) {
                            if (data.filePath == tasks[i].url) {
                              urlMatchCount = i;
                              tasks[i].savedDir;
                              tasks[i].filename;

                              break;
                            }
                          }
                          MyUtils().shareFile(
                              "/sdcard/download/erpcacustomer" +
                                  "/" +
                                  tasks[urlMatchCount].filename,
                              data.docTime.toString());
                          if (urlMatchCount != -1) {
                            if (data.filePath == tasks[urlMatchCount].url) {
                              if (tasks[urlMatchCount].status ==
                                  DownloadTaskStatus.undefined) {
                                new MyUtils().getDownloadUrl(
                                    context, data.filePath, false);
                              } else if (tasks[urlMatchCount].status ==
                                  DownloadTaskStatus.complete) {
                                FlutterDownloader.open(
                                    taskId: tasks[urlMatchCount].taskId);
                              }
                            } else {
                              new MyUtils().getDownloadUrl(
                                  context, data.filePath, false);
                            }
                          } else {
                            new MyUtils()
                                .getDownloadUrl(context, data.filePath, false);
                          }
                        } else {
                          new MyUtils()
                              .getDownloadUrl(context, data.filePath, false);
                        }
                      },
                      child: Text(
                        "Share",
                        style: new CSSStyle().poppinsGreenRegular12(context),
                      ),
                    ),*/
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
