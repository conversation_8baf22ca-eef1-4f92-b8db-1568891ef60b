import 'dart:convert';
import 'dart:isolate';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/ReceiptDetailsActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PaymentAcknowlegement.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/InvoiceListModel.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:get/get.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:table_calendar/table_calendar.dart';

import '../model/CheckPaymentGateWayModel.dart' as paymentGatewayData;
import '../model/GetNetPaymentAmount.dart';
import '../model/RazorPayPaymentActionModel.dart';
import '../model/StoreOrderModel.dart' as storeModelData;

class InvoicesFragment extends StatefulWidget {
  String? type;

  InvoicesFragment({Key? key, required this.type}) : super(key: key);

  @override
  InvoicesFragmentState createState() {
    return new InvoicesFragmentState();
  }
}

class InvoicesFragmentState extends State<InvoicesFragment> {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var buttonVisibilityObs = false.obs;
  var paymentGateWayTypeObs = ''.obs;
  var razorPayApiKeyObs = ''.obs;
  var razorPayOIdObs = ''.obs;
  var netAmountObs = 0.obs;
  var netAmountFromApiObs = ''.obs;
  var showLoaderObs = false.obs;
  var selectedButtonObs = [1, 0, 0].obs;  // ✅ NEW: Reactive button selection

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  bool buttonVisibility = false;
  //String billId = '';
  String paymentGateWayType = '';
  String razorPayApiKey = '';
  String razorPayOId = '';
  int netAmount = 0;
  Razorpay? _razorpay;
  Future? _futurePaymentData;
  String netAmountFromApi = '';
  bool showLoader = false;
  var _media;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _razorpay = Razorpay();
    _razorpay!.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay!.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay!.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
    FlutterDownloader.registerCallback(downloadCallback);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _razorpay!.clear();
  }

  void openCheckout(String name, String key, String orderId, int amount) async {
    var options = {
      'key': key,
      'amount': amount * 100,
      'order_id': orderId,
      'name': name,
      'description': 'Payment',
      'external': {
        'wallets': ['paytm']
      },

      //'theme': {'backdrop_color':'00008B'},
    };

    try {
      _razorpay!.open(options);
    } catch (e) {
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) async {
    // Do something when payment succeeds
    razorPayActionApi(response.paymentId, 'success',
        'Online payment using Razorpay', context);
    // GetX reactive update - NO setState needed!
    final res = await Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => PaymentAcknowledgement(
                true, response.paymentId.toString(), netAmount.toString())));
    if (res.toString().contains('true')) {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedButtonObs.assignAll([0, 0, 0]);
      selectedButtonObs[0] = 1;
      selectedButton = List.from(selectedButtonObs); // Keep for compatibility
    }
  }

  _handlePaymentError(PaymentFailureResponse response) async {
    var res = json.decode(response.message.toString());
    razorPayActionApi(
        'paymentId', 'error', '${res["error"]["description"]}', context);
    // GetX reactive update - NO setState needed!

    var resp = await Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => PaymentAcknowledgement(false, '', '')));
    if (resp.toString().contains('false')) {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedButtonObs.assignAll([0, 0, 0]);
      selectedButtonObs[0] = 1;
      selectedButton = List.from(selectedButtonObs); // Keep for compatibility
    }
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Do something when an external wallet is selected
  }

  static void downloadCallback(
      String id, int status, int progress) {
    if (true) {
    }
    final SendPort? send =
        IsolateNameServer.lookupPortByName('downloader_send_port');
    send!.send([id, status, progress]);
  }

  List selectedButton = [1, 0, 0];

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;
    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Stack(
        children: [
          _buildBackground(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    List buttonsText = ['Unpaid', 'Paid', 'All'];

    var width = MediaQuery.of(context).size.width;
    int value = 0;
    return Material(
      child: FutureBuilder<InvoiceListModel>(
        future: invoiceListCall(
            Constants.ACC_ID, Constants.USER_ID, widget.type!, context),
        builder: (context, snapShot) {
          if (snapShot.connectionState == ConnectionState.done &&
              snapShot.hasData) {
            if (snapShot.data!.data!.length > 0) {
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 10.0),
                    // padding: const EdgeInsets.all(8.0),
                    child: Container(
                      //alignment: Alignment.topLeft,
                      //padding: const EdgeInsets.only(left: 85.0),
                      height: 0.1 * width,
                      margin: EdgeInsets.only(
                          top: 0.03 * width, bottom: 0.05 * width),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(0.02 * width),
                        border: Border.all(
                          color: Colors.black12,
                        ),
                      ),
                      child: ListView.builder(
                          padding: EdgeInsets.zero,
                          scrollDirection: Axis.horizontal,
                          shrinkWrap: true,
                          itemCount: 3,
                          itemBuilder: (context, index) {
                            return InkWell(
                              onTap: () {
                                // ✅ OPTIMIZED: GetX reactive state management
                                selectedButtonObs.assignAll([0, 0, 0]);
                                selectedButtonObs[index] = 1;
                                selectedButton = List.from(selectedButtonObs); // Keep for compatibility
                              },
                              child: Obx(() => Container(
                                /*  margin: EdgeInsets.only(left: 0.03 * width,
                      right: 0.03 * width
                  ),*/
                                // height: 0.0 * width,
                                width: 0.2 * width,
                                alignment: Alignment.center,
                                decoration: BoxDecoration(
                                  color: selectedButtonObs[index] == 1
                                      ? Colors.green
                                      : Colors.white,
                                  /* borderRadius: BorderRadius.only(
                      topRight: Radius.circular(0.02 * width),
                      bottomRight: Radius.circular(0.02 * width),
                    ),*/
                                  borderRadius:
                                      BorderRadius.circular(0.02 * width),
                                ),
                                child: Text(
                                  buttonsText[index],
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                      fontSize: 0.04 * width,
                                      color: selectedButtonObs[index] == 1
                                          ? Colors.white
                                          : Colors.black,
                                      fontFamily:
                                          'FontsFree-Net-SFProText-Regular-1',
                                      fontWeight: FontWeight.w400),
                                ),
                              )),
                            );
                          }),
                    ),
                  ),
                  Expanded(
                    child: FutureBuilder<paymentGatewayData.CheckPaymentGatewayModel>(
                      future: checkPaymentButtonVisibility(Constants.ACC_ID),
                      builder: (context, snap) {
                             return ListView.separated(
                                physics: ClampingScrollPhysics(),
                                shrinkWrap: true,
                                separatorBuilder: (context, index) {
                                  return Padding(
                                    padding: const EdgeInsets.only(left: 85.0),
                                  );
                                },
                                padding: EdgeInsets.zero,
                                itemCount: snapShot.data!.data!.length,
                                itemBuilder: (BuildContext context, int index) {
                                  return _gstBottomValue(
                                      snapShot.data!.data![index],
                                      snap.hasData?((snapShot.data!.data![index].billStatus!
                                                      .toLowerCase() ==
                                                  "unpaid" &&
                                              snap.data!.success == 1 &&
                                              snap.data!.paymentGatewayType ==
                                                  'razorpay')
                                          ? true
                                          : false):false,
                                      snap.hasData?snap.data!.data![0].apiKey:'',
                                      snap.hasData? snap.data!.data![0].secretKey:'');
                                },
                              );
                      },
                    ),
                  ),
                ],
              );
            }
          }
          if (snapShot.connectionState == ConnectionState.waiting) {
            return Center(
                child: Container(
                    color: Colors.white,
                    child: new MyUtils().kLoadingWidget(context)));
          } else {
            return Center(
              child: Container(
                child: Text(
                  'No invoice(s) found!',
                  style: TextStyle(
                    fontSize: 18,
                    fontFamily: 'GlacialIndifference',
                  ),
                ),
              ),
            );
          }
        },
      ),
    );
  }

  postInvoiceListCall(String acc_id, String user_id,
      String bill_type, BuildContext context) async {

    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await invoiceListApi(acc_id, user_id, bill_type, context);
      int flag = responseJson["success"];
      List data = responseJson["data"];
      var billingId = data[0]['bill_id'];
      if (flag == 1) {
        InvoiceListModel currentParsedResponse =
            InvoiceListModel.fromJson(responseJson);
        //billId = billingId;
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<InvoiceListModel> invoiceListCall(String acc_id, String user_id,
      String bill_type, BuildContext context) async {
    return await postInvoiceListCall(acc_id, user_id, bill_type, context);
  }

   postCheckPaymentButtonVisibility(
      String acc_id) async {
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await checkPaymentApi(acc_id, context);
      int flag = responseJson["success"];
      /* List data = responseJson["data"];
      var razorPayData = data[0]['api_key'];
      String gatewayType = responseJson['payment_gateway_type'];

      */

      if (flag == 1) {
        paymentGatewayData.CheckPaymentGatewayModel currentParsedResponse =
        paymentGatewayData.CheckPaymentGatewayModel.fromJson(responseJson);
        /*  setState(() {
          buttonVisibility = true;
          razorPayApiKey = razorPayData;
          paymentGateWayType = gatewayType;
        });*/
        return currentParsedResponse;
      } else {}
    }
  }

  Future<paymentGatewayData.CheckPaymentGatewayModel> checkPaymentButtonVisibility(
      String acc_id) async {
    return await postCheckPaymentButtonVisibility(acc_id);
  }

   postGetNetAmount(
      String acc_id, String billingId, context) async {
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await getAmount(acc_id, billingId, context);
      int flag = responseJson["success"];
      List data = responseJson["data"];
      var netAmnt = data[0]['net_amount'];
      if (flag == 1) {
        GetNetPaymentAmountModel currentParsedResponse =
            GetNetPaymentAmountModel.fromJson(responseJson);
        // GetX reactive update - NO setState needed!
        netAmount = netAmnt;
        netAmountObs.value = netAmnt;  // GetX automatically updates UI

        return currentParsedResponse;
      } else {}
    }
  }

  Future<GetNetPaymentAmountModel> getNetAmount(
      String acc_id, String billingId, context) async {
    return await postGetNetAmount(acc_id, billingId, context);
  }

  generateOrderId(rId, rSecretKey) async {
    String d = '$rId:$rSecretKey';
    String encodedString = base64.encode(d.codeUnits);
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Basic $encodedString'
    };
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
     // var responseJson = await ordersApi(headers, 1 * 100, 'INR', context);
       var responseJson = await ordersApi(headers,netAmount * 100,'INR',context);
      var razorPayOrderId = responseJson['id'];
      // ✅ OPTIMIZED: GetX reactive state management
      razorPayOIdObs.value = razorPayOrderId;
      razorPayOId = razorPayOrderId; // Keep for compatibility
      // setState(() { razorPayOId = razorPayOrderId; }); // Removed

    }
  }

  storeId(orderId, billingId) async {
    var headers = {
      'id': 'erpca',
      'pass': 'e736258681ac6d7126d298cc93a732db1dad2996',
      'Content-Type': 'application/json',
     /* 'Cookie':
          'AWSALB=AmGoL650bwZ7U4DoFugNbftrb7KxppG+2M9sFfjDCjVXn3CidNuBx/TFRWHrpWDs3ZUH8u7AZfKsoH6OohufdBF/+xjNPI0X+4SAgOAP9O1mg2HtBO56+QJ8UXN7; AWSALBCORS=AmGoL650bwZ7U4DoFugNbftrb7KxppG+2M9sFfjDCjVXn3CidNuBx/TFRWHrpWDs3ZUH8u7AZfKsoH6OohufdBF/+xjNPI0X+4SAgOAP9O1mg2HtBO56+QJ8UXN7'*/
    };
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await storeAction(headers, Constants.ACC_ID,
          billingId.toString(), Constants.USER_ID, orderId, context);
      storeModelData.StoreOrderModel currentParsedResponse =
      storeModelData.StoreOrderModel.fromJson(responseJson);
      return currentParsedResponse;
    }
  }

  razorPayActionApi(paymentId, status, remark, context) async {
    var headers = {
      'id': 'erpca',
      'pass': 'e736258681ac6d7126d298cc93a732db1dad2996',
      'Content-Type': 'application/json',
     /* 'Cookie':
          'AWSALB=AmGoL650bwZ7U4DoFugNbftrb7KxppG+2M9sFfjDCjVXn3CidNuBx/TFRWHrpWDs3ZUH8u7AZfKsoH6OohufdBF/+xjNPI0X+4SAgOAP9O1mg2HtBO56+QJ8UXN7; AWSALBCORS=AmGoL650bwZ7U4DoFugNbftrb7KxppG+2M9sFfjDCjVXn3CidNuBx/TFRWHrpWDs3ZUH8u7AZfKsoH6OohufdBF/+xjNPI0X+4SAgOAP9O1mg2HtBO56+QJ8UXN7'*/
    };
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await razorPayAction(headers, Constants.ACC_ID,
          paymentId, razorPayOId, status, remark, context);
      RazorPayPaymentAction currentParsedResponse =
          RazorPayPaymentAction.fromJson(responseJson);
      return currentParsedResponse;
    }
  }

  Widget _gstBottomValue(Data data, showButton, razorPayId, razorPaySecretKey) {
    return Obx(() => (selectedButtonObs[0] == 1)
        ? _showUnpaidBills(data, showButton, razorPayId, razorPaySecretKey)
        : (selectedButtonObs[1] == 1)
            ? _showPaidBills(data, showButton, razorPayId, razorPaySecretKey)
            : Padding(
                padding: const EdgeInsets.all(8.0),
                child: Card(
                  color: Color(new CommonColor().white_Color),
                  elevation: 2,
                  shadowColor: Colors.white,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 13.0, right: 11, top: 15),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${data.billNo}',
                              style:
                                  new CSSStyle().poppinsBlackRegular20(context),
                            ),
                            (data.billStatus != "")
                                ? Padding(
                                    padding: const EdgeInsets.only(
                                      left: 0,
                                      bottom: 1,
                                    ),
                                    child: Card(
                                        elevation: 0,
                                        child: (data.billStatus!.toLowerCase() ==
                                                "unpaid")
                                            ? Container(
                                                // width: 60,
                                                decoration: new BoxDecoration(
                                                    // shape: BoxShape.rectangle,
                                                    // borderRadius: BorderRadius.circular(3),
                                                    ),
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 0,
                                                          right: 0,
                                                          top: 5,
                                                          bottom: 5),
                                                  child: Text(
                                                    data.billStatus.toString(),
                                                    textAlign: TextAlign.center,
                                                    style: TextStyle(
                                                      color: Color(
                                                          new CommonColor()
                                                              .red_light_Color),
                                                    ),
                                                  ),
                                                ))
                                            : Container(
                                                width: 60,
                                                decoration: new BoxDecoration(
                                                  color: Colors.green,
                                                  shape: BoxShape.rectangle,
                                                  borderRadius:
                                                      BorderRadius.circular(3),
                                                ),
                                                child: Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          left: 5.0,
                                                          right: 5,
                                                          top: 5,
                                                          bottom: 5),
                                                  child: Text(
                                                    data.billStatus.toString(),
                                                    textAlign: TextAlign.center,
                                                    style: new CSSStyle()
                                                        .poppinsWhiteRegular12(
                                                            context),
                                                  ),
                                                ))),
                                  )
                                : Container(),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 13.0, right: 13, top: 15),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Date",
                                  style: new CSSStyle()
                                      .poppinsBlackBold13W400(context),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 5.0),
                                  child: Text(
                                    data.billDate.toString(),
                                    style: new CSSStyle()
                                        .poppinsGreyRegular12(context),
                                  ),
                                ),
                              ],
                            ),
                            Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(
                                  "Amount",
                                  textAlign: TextAlign.right,
                                  style: new CSSStyle()
                                      .poppinsBlackBold13W400(context),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(top: 5.0),
                                  child: Text(
                                    "₹ " + data.netAmount.toString() + "/-",
                                    style: new CSSStyle()
                                        .poppinsGreyRegular12(context),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                      /*  Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 15),
              child: Text(
                data.invoiceType,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/
                      /* Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 5),
              child: Text(
                data.billDescription,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/

                      data.billDescription != "" ? Divider() : Container(),
                      data.billDescription != ""
                          ? Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 13.0, right: 13),
                                  child: Text(
                                    "Service Description",
                                    style: new CSSStyle()
                                        .poppinsBlackBold13W400(context),
                                  ),
                                ),
                                ListView.builder(
                                  physics: ClampingScrollPhysics(),
                                  shrinkWrap: true,
                                  padding: EdgeInsets.zero,
                                  itemCount:
                                      data.billDescription!.split("~").length,
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    return _gstDescriptionValue(
                                        data.billDescription!.split("~")[index]);
                                  },
                                ),
                              ],
                            )
                          : Container(),
                      Padding(
                        padding: const EdgeInsets.only(
                          top: 15,
                        ),
                        child: Divider(),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 13.0, right: 13.0, top: 10, bottom: 15),
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            (data.billLink != "")
                                ? GestureDetector(
                                    onTap: () async {
                                      MyUtils().getDownloadUrl1(data.billLink!);
                                    },
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.download_sharp,
                                          size: 15,
                                          color: Color(new CommonColor()
                                              .green_light_Color),
                                        ),
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(left: 2.0),
                                          child: Text(
                                            "Invoice",
                                            style: new CSSStyle()
                                                .poppinsBlueRegular12(context),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : Text(
                                    "     ",
                                    style: new CSSStyle()
                                        .poppinsBlueRegular12(context),
                                  ),
                            if (data.billStatus == 'Paid')
                              GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            ReceiptDetailsActivity(
                                              billing_id: data.billId,
                                              invoiceNo: data.billNo,
                                            )),
                                  );
                                },
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.wysiwyg,
                                      size: 15,
                                      color: Colors.teal,
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(left: 5),
                                      child: Text(
                                        "Receipt",
                                        style: new CSSStyle()
                                            .poppinsGreenRegular12(context),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            if (showButton)
                              _gstPayNowButton(
                                  data.billId,
                                  data.billingCompanyName,
                                  razorPayId,
                                  razorPaySecretKey),

                            /* (data.billStatus.toLowerCase() == "unpaid")
                      ? Expanded(
                          flex: 1,
                          child: GestureDetector(
                            onTap: () {
                              MyUtils.showOkDialog(

                                  context, "Dispute", "Coming Soon");
                            },
                            child: Text(
                              "Dispute",
                              style:
                                  new CSSStyle().poppinsRedRegular12(context),
                            ),
                          ),
                        )
                      : Expanded(
                          flex: 1,
                          child: Text(
                            "",
                            style: new CSSStyle().poppinsRedRegular12(context),
                          ),
                        ),
                  (data.billStatus.toLowerCase() == "unpaid")
                      ? Expanded(
                          flex: 1,
                          child: GestureDetector(
                            onTap: () {
                              MyUtils.showOkDialog(
                                  context, "Online Payment", "Coming Soon");
                            },
                            child: Text(
                              "Pay Now",
                              style:
                                  new CSSStyle().poppinsGreenRegular12(context),
                            ),
                          ),
                        )
                      : Expanded(
                          flex: 1,
                          child: Text(
                            "",
                            style:
                                new CSSStyle().poppinsGreenRegular12(context),
                          ),
                        ),*/
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ));
  }

  Widget _gstDescriptionValue(String data) {
    return Padding(
      padding: const EdgeInsets.only(left: 13.0, right: 13, top: 5.0),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check,
            color: Colors.green,
            size: 15,
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8.0),
            child: Container(
              width: 280,
              child: Text(
                data,
                style: new CSSStyle().poppinsGreyRegular12(context),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _gstPayNowButton(billId, billingCompanyName, razorId, razorSecret) {
    var height = MediaQuery.of(context).size.height;
    var width = MediaQuery.of(context).size.width;
    return InkWell(
      onTap: () async {
        // GetX reactive update - NO setState needed!
        showLoader = true;
        showLoaderObs.value = true;  // GetX automatically updates UI
        if (showLoader)
          showModalBottomSheet(
              isDismissible: false,
              enableDrag: false,
              shape: const RoundedRectangleBorder(
                // <-- SEE HERE
                borderRadius: BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
              ),
              context: context,
              builder: (context) {
                return Container(
                  height: 0.3 * height,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Container(
                          // height: 0.7 * width,
                          // width: 0.6 * width,
                          // /color: Colors.red,
                          child: Container(
                              child:
                                  new MyUtils().kLoadingWidgetLarge(context))),
                      SizedBox(
                        height: 0.1 * width,
                      ),
                      Container(
                        child: Text(
                          'Processing ...',
                          style: TextStyle(
                            fontSize: 0.06 * width,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              });

        await getNetAmount(Constants.ACC_ID, billId, context);
        // Navigator.push(context, MaterialPageRoute(builder: (context)=>PaymentAcknowledgement(false)));
        await generateOrderId(razorId, razorSecret);
        await storeId(razorPayOId, billId);
        // GetX reactive update - NO setState needed!
        showLoader = false;
        showLoaderObs.value = false;  // GetX automatically updates UI
        Navigator.pop(context);
        // Navigator.push(context, MaterialPageRoute(builder: (context)=>PaymentAcknowledgement(true,'response.paymentId.toString()',netAmount.toString(),widget.type)));
        openCheckout(billingCompanyName, razorId, razorPayOId, netAmount);
        // openCheckout(billingCompanyName, razorId, razorPayOId, 1);
      },
      child: Container(
        alignment: Alignment.center,
        height: width * 0.1,
        width: width * 0.2,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(0.02 * width),
          color: Colors.blueAccent,
          // color: Color(new CommonColor().green_light_Color),
        ),
        child: Text(
          "Pay Now",
          textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: 0.036 * width,
              color: Colors.white,
              fontFamily: 'FontsFree-Net-SFProText-Regular-1'),
        ),
      ),
    );
  }

  Widget _showUnpaidBills(
      Data data, showButton, razorPayId, razorPaySecretKey) {
    return data.billStatus == 'Unpaid'
        ? Padding(
            padding: const EdgeInsets.all(8.0),
            child: Card(
              color: Color(new CommonColor().white_Color),
              elevation: 2,
              shadowColor: Colors.white,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 13.0, right: 11, top: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${data.billNo}',
                          style: new CSSStyle().poppinsBlackRegular20(context),
                        ),
                        (data.billStatus != "")
                            ? Padding(
                                padding: const EdgeInsets.only(
                                  left: 0,
                                  bottom: 1,
                                ),
                                child: Card(
                                    elevation: 0,
                                    child: (data.billStatus.toString().toLowerCase() ==
                                            "unpaid")
                                        ? Container(
                                            // width: 60,
                                            decoration: new BoxDecoration(
                                                // shape: BoxShape.rectangle,
                                                // borderRadius: BorderRadius.circular(3),
                                                ),
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 0,
                                                  right: 0,
                                                  top: 5,
                                                  bottom: 5),
                                              child: Text(
                                                data.billStatus.toString(),
                                                textAlign: TextAlign.center,
                                                style: TextStyle(
                                                  color: Color(new CommonColor()
                                                      .red_light_Color),
                                                ),
                                              ),
                                            ))
                                        : Container(
                                            width: 60,
                                            decoration: new BoxDecoration(
                                              color: Colors.green,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(3),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 5.0,
                                                  right: 5,
                                                  top: 5,
                                                  bottom: 5),
                                              child: Text(
                                                data.billStatus.toString(),
                                                textAlign: TextAlign.center,
                                                style: new CSSStyle()
                                                    .poppinsWhiteRegular12(
                                                        context),
                                              ),
                                            ))),
                              )
                            : Container(),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 13.0, right: 13, top: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Date",
                              style: new CSSStyle()
                                  .poppinsBlackBold13W400(context),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                data.billDate.toString(),
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                          ],
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Amount",
                              textAlign: TextAlign.right,
                              style: new CSSStyle()
                                  .poppinsBlackBold13W400(context),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                "₹ " + data.netAmount.toString() + "/-",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  /*  Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 15),
              child: Text(
                data.invoiceType,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/
                  /* Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 5),
              child: Text(
                data.billDescription,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/

                  data.billDescription != "" ? Divider() : Container(),
                  data.billDescription != ""
                      ? Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 13.0, right: 13),
                              child: Text(
                                "Service Description",
                                style: new CSSStyle()
                                    .poppinsBlackBold13W400(context),
                              ),
                            ),
                            ListView.builder(
                              physics: ClampingScrollPhysics(),
                              shrinkWrap: true,
                              padding: EdgeInsets.zero,
                              itemCount: data.billDescription!.split("~").length,
                              itemBuilder: (BuildContext context, int index) {
                                return _gstDescriptionValue(
                                    data.billDescription!.split("~")[index]);
                              },
                            ),
                          ],
                        )
                      : Container(),
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 15,
                    ),
                    child: Divider(),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 13.0, right: 13.0, top: 10, bottom: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        (data.billLink != "")
                            ? GestureDetector(
                                onTap: () async {
                                  MyUtils().getDownloadUrl1(data.billLink!);
                                },
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.download_sharp,
                                      size: 15,
                                      color: Color(
                                          new CommonColor().green_light_Color),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 2.0),
                                      child: Text(
                                        "Invoice",
                                        style: new CSSStyle()
                                            .poppinsBlueRegular12(context),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Text(
                                "     ",
                                style: new CSSStyle()
                                    .poppinsBlueRegular12(context),
                              ),
                        /* GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (context) => ReceiptDetailsActivity(
                              billing_id: data.billId,
                              invoiceNo: data.billNo,
                            )),
                      );
                    },
                    child: Row(
                      children: [
                        Icon(
                          Icons.wysiwyg,
                          size: 15,
                          color: Colors.teal,
                        ),
                        Text(
                          "Receipt",
                          style: new CSSStyle().poppinsGreenRegular12(context),
                        ),
                      ],
                    ),
                  ),*/
                        if (showButton)
                          _gstPayNowButton(data.billId, data.billingCompanyName,
                              razorPayId, razorPaySecretKey),

                        /* (data.billStatus.toLowerCase() == "unpaid")
                      ? Expanded(
                          flex: 1,
                          child: GestureDetector(
                            onTap: () {
                              MyUtils.showOkDialog(

                                  context, "Dispute", "Coming Soon");
                            },
                            child: Text(
                              "Dispute",
                              style:
                                  new CSSStyle().poppinsRedRegular12(context),
                            ),
                          ),
                        )
                      : Expanded(
                          flex: 1,
                          child: Text(
                            "",
                            style: new CSSStyle().poppinsRedRegular12(context),
                          ),
                        ),
                  (data.billStatus.toLowerCase() == "unpaid")
                      ? Expanded(
                          flex: 1,
                          child: GestureDetector(
                            onTap: () {
                              MyUtils.showOkDialog(
                                  context, "Online Payment", "Coming Soon");
                            },
                            child: Text(
                              "Pay Now",
                              style:
                                  new CSSStyle().poppinsGreenRegular12(context),
                            ),
                          ),
                        )
                      : Expanded(
                          flex: 1,
                          child: Text(
                            "",
                            style:
                                new CSSStyle().poppinsGreenRegular12(context),
                          ),
                        ),*/
                      ],
                    ),
                  )
                ],
              ),
            ),
          )
        : Container();
  }

  Widget _showPaidBills(Data data, showButton, razorPayId, razorPaySecretKey) {
    return data.billStatus == 'Paid'
        ? Padding(
            padding: const EdgeInsets.all(8.0),
            child: Card(
              color: Color(new CommonColor().white_Color),
              elevation: 2,
              shadowColor: Colors.white,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 13.0, right: 11, top: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          '${data.billNo}',
                          style: new CSSStyle().poppinsBlackRegular20(context),
                        ),
                        (data.billStatus != "")
                            ? Padding(
                                padding: const EdgeInsets.only(
                                  left: 0,
                                  bottom: 1,
                                ),
                                child: Card(
                                    elevation: 0,
                                    child: (data.billStatus.toString().toLowerCase() ==
                                            "unpaid")
                                        ? Container(
                                            // width: 60,
                                            decoration: new BoxDecoration(
                                                // shape: BoxShape.rectangle,
                                                // borderRadius: BorderRadius.circular(3),
                                                ),
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 0,
                                                  right: 0,
                                                  top: 5,
                                                  bottom: 5),
                                              child: Text(
                                                data.billStatus.toString(),
                                                textAlign: TextAlign.center,
                                                style: TextStyle(
                                                  color: Color(new CommonColor()
                                                      .red_light_Color),
                                                ),
                                              ),
                                            ))
                                        : Container(
                                            width: 60,
                                            decoration: new BoxDecoration(
                                              color: Colors.green,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(3),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 5.0,
                                                  right: 5,
                                                  top: 5,
                                                  bottom: 5),
                                              child: Text(
                                                data.billStatus.toString(),
                                                textAlign: TextAlign.center,
                                                style: new CSSStyle()
                                                    .poppinsWhiteRegular12(
                                                        context),
                                              ),
                                            ))),
                              )
                            : Container(),
                      ],
                    ),
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 13.0, right: 13, top: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Date",
                              style: new CSSStyle()
                                  .poppinsBlackBold13W400(context),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                data.billDate.toString(),
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                          ],
                        ),
                        Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Text(
                              "Amount",
                              textAlign: TextAlign.right,
                              style: new CSSStyle()
                                  .poppinsBlackBold13W400(context),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                "₹ " + data.netAmount.toString() + "/-",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  /*  Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 15),
              child: Text(
                data.invoiceType,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/
                  /* Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 5),
              child: Text(
                data.billDescription,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/

                  data.billDescription != "" ? Divider() : Container(),
                  data.billDescription != ""
                      ? Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.only(left: 13.0, right: 13),
                              child: Text(
                                "Service Description",
                                style: new CSSStyle()
                                    .poppinsBlackBold13W400(context),
                              ),
                            ),
                            ListView.builder(
                              physics: ClampingScrollPhysics(),
                              shrinkWrap: true,
                              padding: EdgeInsets.zero,
                              itemCount: data.billDescription!.split("~").length,
                              itemBuilder: (BuildContext context, int index) {
                                return _gstDescriptionValue(
                                    data.billDescription!.split("~")[index]);
                              },
                            ),
                          ],
                        )
                      : Container(),
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 15,
                    ),
                    child: Divider(),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 13.0, right: 13.0, top: 10, bottom: 15),
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        (data.billLink != "")
                            ? GestureDetector(
                                onTap: () async {
                                  MyUtils().getDownloadUrl1(data.billLink!);
                                },
                                child: Row(
                                  children: [
                                    Icon(
                                      Icons.download_sharp,
                                      size: 15,
                                      color: Color(
                                          new CommonColor().green_light_Color),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.only(left: 2.0),
                                      child: Text(
                                        "Invoice",
                                        style: new CSSStyle()
                                            .poppinsBlueRegular12(context),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : Text(
                                "     ",
                                style: new CSSStyle()
                                    .poppinsBlueRegular12(context),
                              ),
                        GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => ReceiptDetailsActivity(
                                        billing_id: data.billId,
                                        invoiceNo: data.billNo,
                                      )),
                            );
                          },
                          child: Row(
                            children: [
                              Icon(
                                Icons.wysiwyg,
                                size: 15,
                                color: Colors.teal,
                              ),
                              Container(
                                margin: EdgeInsets.only(left: 5),
                                child: Text(
                                  "Receipt",
                                  style: new CSSStyle()
                                      .poppinsGreenRegular12(context),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (showButton)
                          _gstPayNowButton(data.billId, data.billingCompanyName,
                              razorPayId, razorPaySecretKey),

                        /* (data.billStatus.toLowerCase() == "unpaid")
                      ? Expanded(
                          flex: 1,
                          child: GestureDetector(
                            onTap: () {
                              MyUtils.showOkDialog(

                                  context, "Dispute", "Coming Soon");
                            },
                            child: Text(
                              "Dispute",
                              style:
                                  new CSSStyle().poppinsRedRegular12(context),
                            ),
                          ),
                        )
                      : Expanded(
                          flex: 1,
                          child: Text(
                            "",
                            style: new CSSStyle().poppinsRedRegular12(context),
                          ),
                        ),
                  (data.billStatus.toLowerCase() == "unpaid")
                      ? Expanded(
                          flex: 1,
                          child: GestureDetector(
                            onTap: () {
                              MyUtils.showOkDialog(
                                  context, "Online Payment", "Coming Soon");
                            },
                            child: Text(
                              "Pay Now",
                              style:
                                  new CSSStyle().poppinsGreenRegular12(context),
                            ),
                          ),
                        )
                      : Expanded(
                          flex: 1,
                          child: Text(
                            "",
                            style:
                                new CSSStyle().poppinsGreenRegular12(context),
                          ),
                        ),*/
                      ],
                    ),
                  )
                ],
              ),
            ),
          )
        : Container();
  }
}

class CustomDialogBox extends StatefulWidget {
  final String? title, descriptions, text;
  final Image? img;

  const CustomDialogBox(
      {Key? key, this.title, this.descriptions, this.text, this.img})
      : super(key: key);

  @override
  _CustomDialogBoxState createState() => _CustomDialogBoxState();
}

class _CustomDialogBoxState extends State<CustomDialogBox> {
  bool checkedProgress = false;
  bool checkedStarted = false;
  bool checkedCompleted = false;
  bool checkedBilled = false;

  String radioItem = 'Mango';

  int id = 1;
  String radioButtonItem = 'Due This Week';
  final kToday = DateTime.now();
 // CalendarController _calendarController = CalendarController();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  Widget _buildTableCalendar() {
    return TableCalendar(
      firstDay: DateTime.now(),
      lastDay: DateTime(kToday.year, kToday.month - 3, kToday.day),
      focusedDay: DateTime(kToday.year, kToday.month + 3, kToday.day),
      //calendarController: _calendarController,
      startingDayOfWeek: StartingDayOfWeek.sunday,
      calendarStyle: CalendarStyle(
        /*selectedColor: Colors.deepOrange[400],
        todayColor: Colors.deepOrange[200],
        markersColor: Colors.brown[700],*/
        outsideDaysVisible: false,
      ),
      headerStyle: HeaderStyle(
        formatButtonTextStyle:
            TextStyle().copyWith(color: Colors.white, fontSize: 15.0),
        formatButtonDecoration: BoxDecoration(
          color: Colors.deepOrange[400],
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }

  contentBox(context) {
    return Stack(
      children: <Widget>[
        SingleChildScrollView(
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: Color(new CommonColor().erpca_light_pink_color),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  child: Column(
                    children: [
                      Card(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                "Reset",
                                style: new CSSStyle()
                                    .poppinsGreyRegular15(context),
                              ),
                            ),
                            Text(
                              "Filter",
                              style: new CSSStyle().poppinsBlackBold15(context),
                            ),
                            IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  color: Colors.black,
                                ),
                                onPressed: () {
                                  Navigator.of(context, rootNavigator: true)
                                      .pop("Discard");
                                }),
                          ],
                        ),
                      ),
                      Card(
                        elevation: 4.0,
                        margin:
                            EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
                        child: Container(
                          child: _buildTableCalendar(),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.fromLTRB(8, 8, 8, 8),
                        constraints:
                            BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
                        child: GradientButton(
                          gradient: LinearGradient(
                              colors: [
                                Color(new CommonColor().erpca_blue_color),
                                Color(new CommonColor().erpca_blue_color)
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight),
                          //color: Colors.cyan,
                          elevation: 5.0,
                          shape: new RoundedRectangleBorder(
                              borderRadius: new BorderRadius.circular(10.0)),
                          //splashColor: Colors.blueGrey,
                          //color: Theme.of(context).accentColor,
                          //textColor: Theme.of(context).primaryColorLight,
                          child: Text(
                            'APPLY',
                            style: new CSSStyle().verdanaWhiteLight14(context),
                          ),
                          callback: () {
                            Navigator.of(context, rootNavigator: true)
                                .pop("Discard");
                          },
                          increaseWidthBy: 225.0,
                          increaseHeightBy: 50.0,
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
