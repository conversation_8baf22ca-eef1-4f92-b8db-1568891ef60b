class CheckPaymentGatewayModel {
  int? _status;
  String? _message;
  int? _success;
  String? _paymentGatewayType;
  List<Data>? _data;
  String? _errorDev;

  CheckPaymentGatewayModel(
      {int? status,
        String? message,
        int? success,
        String? paymentGatewayType,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (paymentGatewayType != null) {
      this._paymentGatewayType = paymentGatewayType;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get paymentGatewayType => _paymentGatewayType;
  set paymentGatewayType(String? paymentGatewayType) =>
      _paymentGatewayType = paymentGatewayType;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CheckPaymentGatewayModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _paymentGatewayType = json['payment_gateway_type'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['payment_gateway_type'] = this._paymentGatewayType;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _apiKey;
  String? _secretKey;

  Data({String? apiKey, String? secretKey}) {
    if (apiKey != null) {
      this._apiKey = apiKey;
    }
    if (secretKey != null) {
      this._secretKey = secretKey;
    }
  }

  String? get apiKey => _apiKey;
  set apiKey(String? apiKey) => _apiKey = apiKey;
  String? get secretKey => _secretKey;
  set secretKey(String? secretKey) => _secretKey = secretKey;

  Data.fromJson(Map<String, dynamic> json) {
    _apiKey = json['api_key'];
    _secretKey = json['secret_key'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['api_key'] = this._apiKey;
    data['secret_key'] = this._secretKey;
    return data;
  }
}
