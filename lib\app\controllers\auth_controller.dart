// Authentication Controller
// Manages authentication state and operations using GetX

import 'package:get/get.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';
import '../../routes/app_routes.dart';

class AuthController extends GetxController {
  final AuthService _authService = Get.find<AuthService>();
  final StorageService _storageService = Get.find<StorageService>();

  // Reactive variables
  final isLoading = false.obs;
  final isLoggedIn = false.obs;
  final errorMessage = ''.obs;
  final userName = ''.obs;
  final userPhone = ''.obs;
  final selectedCompany = ''.obs;

  // Login form variables
  final mobileNumber = ''.obs;
  final password = ''.obs;
  final mPin = ''.obs;
  final otp = ''.obs;

  @override
  void onInit() {
    super.onInit();
    checkAuthStatus();
  }

  // Check if user is already authenticated
  Future<void> checkAuthStatus() async {
    try {
      isLoading.value = true;
      
      final token = await _storageService.getToken();
      final mpin = await _storageService.getMPin();
      
      if (token.isNotEmpty && mpin.isNotEmpty) {
        isLoggedIn.value = true;
        await loadUserData();
      } else {
        isLoggedIn.value = false;
      }
    } catch (e) {
      print('Error checking auth status: $e');
      isLoggedIn.value = false;
    } finally {
      isLoading.value = false;
    }
  }

  // Load user data from storage
  Future<void> loadUserData() async {
    try {
      userName.value = await _storageService.getUserName();
      userPhone.value = await _storageService.getContactPhone();
      selectedCompany.value = await _storageService.getSelectedCompany();
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  // Login with mobile number
  Future<void> loginWithMobile(String mobile) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await _authService.loginWithMobile(mobile);
      
      if (result['success']) {
        mobileNumber.value = mobile;
        // Navigate to OTP screen or next step
        Get.toNamed(Routes.SELECT_USER);
      } else {
        errorMessage.value = result['message'] ?? 'Login failed';
      }
    } catch (e) {
      errorMessage.value = 'Network error occurred';
      print('Login error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Login with username and password
  Future<void> loginWithCredentials(String username, String pass) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await _authService.loginWithCredentials(username, pass);
      
      if (result['success']) {
        await _storageService.saveToken(result['token']);
        await _storageService.saveUserName(username);
        isLoggedIn.value = true;
        Get.offAllNamed(Routes.HOME);
      } else {
        errorMessage.value = result['message'] ?? 'Login failed';
      }
    } catch (e) {
      errorMessage.value = 'Network error occurred';
      print('Login error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Verify OTP
  Future<void> verifyOTP(String otpCode) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await _authService.verifyOTP(mobileNumber.value, otpCode);
      
      if (result['success']) {
        otp.value = otpCode;
        Get.toNamed(Routes.SELECT_USER);
      } else {
        errorMessage.value = result['message'] ?? 'Invalid OTP';
      }
    } catch (e) {
      errorMessage.value = 'Network error occurred';
      print('OTP verification error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Set MPIN
  Future<void> setMPin(String pin) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      await _storageService.saveMPin(pin);
      mPin.value = pin;
      isLoggedIn.value = true;
      
      Get.offAllNamed(Routes.HOME);
    } catch (e) {
      errorMessage.value = 'Failed to set MPIN';
      print('MPIN error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Verify MPIN
  Future<bool> verifyMPin(String pin) async {
    try {
      isLoading.value = true;
      
      final savedPin = await _storageService.getMPin();
      
      if (savedPin == pin) {
        isLoggedIn.value = true;
        Get.offAllNamed(Routes.HOME);
        return true;
      } else {
        errorMessage.value = 'Invalid MPIN';
        return false;
      }
    } catch (e) {
      errorMessage.value = 'MPIN verification failed';
      print('MPIN verification error: $e');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  // Select company
  Future<void> selectCompany(String company) async {
    try {
      selectedCompany.value = company;
      await _storageService.saveSelectedCompany(company);
      Get.offAllNamed(Routes.HOME);
    } catch (e) {
      errorMessage.value = 'Failed to select company';
      print('Company selection error: $e');
    }
  }

  // Logout
  Future<void> logout() async {
    try {
      isLoading.value = true;
      
      await _storageService.clearAll();
      
      // Reset all reactive variables
      isLoggedIn.value = false;
      userName.value = '';
      userPhone.value = '';
      selectedCompany.value = '';
      mobileNumber.value = '';
      password.value = '';
      mPin.value = '';
      otp.value = '';
      errorMessage.value = '';
      
      Get.offAllNamed(Routes.SPLASH);
    } catch (e) {
      print('Logout error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Clear error message
  void clearError() {
    errorMessage.value = '';
  }
}
