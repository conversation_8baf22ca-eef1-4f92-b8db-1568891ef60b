import 'dart:io';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/SetMPinActivity.dart';
import 'package:erpcacustomer/activity/SplashActivity.dart';
import 'package:erpcacustomer/activity/username_verification_screen.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/CommonText.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/NotificationBadge.dart';
import 'package:erpcacustomer/common/sizeconfig.dart';
import 'package:erpcacustomer/controller/LoginController.dart';
import 'package:erpcacustomer/model/ValidateCustomerMobileModel.dart';
import 'package:erpcacustomer/model/push_notification.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mobile_number/mobile_number.dart';
import 'package:mobile_number/sim_card.dart';
import 'package:erpcacustomer/activity/SetOtpActivity.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'dart:convert';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:otpless_flutter/otpless_flutter.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'dart:isolate';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import '../routes/app_routes.dart';

import '../model/validate_with_whatsapp_model.dart';

class LoginWithMobileNumberActivity extends StatefulWidget {
  String? mobileNo;

  LoginWithMobileNumberActivity({Key? key, this.mobileNo}) : super(key: key);

  @override
  LoginWithMobileNumberActivityState createState() {
    return new LoginWithMobileNumberActivityState();
  }
}
/*Future<ValidateCustomerMobileModel> createAlbum(String mobileNo, String fcm, int validate, BuildContext context) async {
  print('fcmId in validate $fcm');
  return await callLoginApi(Constants.ACC_ID,mobileNo,fcm,"0",context);
  */ /*if (Platform.isAndroid) {
        DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
        AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        print('Running on ${androidInfo.model}');
        return await validateUserMobileApi(mobileNo, fcm, androidInfo.model.toString(), 1, context);
      } else {
        var iosInfo = await DeviceInfoPlugin().iosInfo;
        String name = iosInfo.name.toString();
        return await validateUserMobileApi(mobileNo, fcm, name, 1, context);
      }*/ /*
}*/

class LoginWithMobileNumberActivityState
    extends State<LoginWithMobileNumberActivity> {

    ValidateWithWhatsAppModel? output;
  final _otplessFlutterPlugin = Otpless();
  bool? _hasCard;
  bool showLoader = false;
  static const PrimaryColor = const Color(0xFF04137B);
  Future<void> startOtpless() async {
    final Map<String, dynamic> otplessConfig = {
      "appId": "QPE7HXAZSAMY0NU6X831",
    };
    //await _otplessFlutterPlugin.hideFabButton();
    _otplessFlutterPlugin.openLoginPage((result) async {
      print("result 123 : ${result.toString()}");
      var message = "";
      if (result['data'] != null) {
        final token = result['data']['token'];
        // Assuming `result['data']['identities']` is a list
final identities = result['data']['identities'] as List;
final mobNumber = identities.first['identityValue'];

        print("Token test : $token");
        print("Mobile Number test11 : $result");
        print("Mobile Number test : $mobNumber");
        mobileNumber = mobNumber.substring(2);
        message = "token: $token";
        showLoader = true;
        if (showLoader){
          showLoaderModal(context);
        }
        return verifyLogin(
            Constants.ACC_ID, mobileNumber, fcmId, "0", context);
      } else {
      
        message = result['errorMessage'];

      }
      // GetX reactive update - NO setState needed!
    }, /*jsonObject: json*/ otplessConfig);
  }

  var _media;
  /* Future<ValidateCustomerMobileModel>? _futureAlbum;*/
  String mobileNumber = '';
  var mobileNumberObs = ''.obs;  // GetX reactive variable
  var isLoading = false.obs;     // GetX reactive variable
  //var showLoaderObs = false.obs; // GetX reactive variable
  bool _saving = false;
  String myToken = '';
  String userName = "";
  String password = "";
  var checkedValue = false;
  bool _obscureText = true;
  bool selected = true;
  String _mobileNumber = '';
  List<SimCard> _simCard = <SimCard>[];
  int _radioValue1 = -1;
  String fcmId = "";
  bool isWhatsappInstalled = false;
  PushNotification? _notificationInfo;
  int? _totalNotifications;

  var _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    if (widget.mobileNo != null && widget.mobileNo!.length > 10) {
      _mobileNumber = widget.mobileNo!.substring(1);
      _mobileNumber = _mobileNumber.substring(1);
      mobileNumberObs.value = _mobileNumber;  // Update GetX reactive variable
    } else if (widget.mobileNo != null) {
      _mobileNumber = widget.mobileNo!;
      mobileNumberObs.value = _mobileNumber;  // Update GetX reactive variable
    }
    _totalNotifications = 0;

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      isWhatsappInstalled = await _otplessFlutterPlugin.isWhatsAppInstalled();
      // setState(() {});
    });
    FlutterDownloader.registerCallback(downloadCallback);
  }

  static void downloadCallback(String id, int status, int progress) {
    if (true) {}
    final SendPort? send =
        IsolateNameServer.lookupPortByName('downloader_send_port');
    send!.send([id, status, progress]);
  }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: PrimaryColor,
        fontFamily: 'GlacialIndifference',
      ),
      home: Scaffold(
        appBar: AppBar(
          elevation: 0,
          /*  leading: IconButton(
              icon: Icon(Icons.arrow_back),
              onPressed: () {
                moveToLastScreen();
              }),*/
          title: Center(
            child: Text(
              "SIM Verification",
              style: new CSSStyle().poppinsLightBlackRegular16(context),
            ),
          ),
          backgroundColor: Color(new CommonColor().white_Color),
        ),
        body: _buildBackground(),
      ),
    );
  }

  _buildCountryPickerDropdown(
      {bool filtered = false,
      bool sortedByIsoCode = false,
      bool hasPriorityList = false,
      bool hasSelectedItemBuilder = false}) {
    double dropdownButtonWidth = MediaQuery.of(context).size.width * 0.5;
    //respect dropdown button icon size
    double dropdownItemWidth = dropdownButtonWidth - 30;
    double dropdownSelectedItemWidth = dropdownButtonWidth - 30;
    return Row(
      children: <Widget>[
        Expanded(
          child: TextField(
            decoration: InputDecoration(
              labelText: "Phone",
              isDense: true,
              contentPadding: EdgeInsets.zero,
            ),
            keyboardType: TextInputType.number,
          ),
        )
      ],
    );
  }

  Widget fillCards() {
    return Container(
      width: 350,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        mainAxisSize: MainAxisSize.max,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.max,
            children: [
              Image(
                width: 100.0,
                height: 100.0,
                image: AssetImage('assets/images/mobile.png'),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 25.0, right: 25),
                child: Obx(() => TextFormField(
                  keyboardType: TextInputType.number,
                  inputFormatters: <TextInputFormatter>[
                    FilteringTextInputFormatter.digitsOnly
                  ],
                  maxLines: 1,
                  maxLength: 10,
                  initialValue: mobileNumberObs.value.isEmpty ? _mobileNumber : mobileNumberObs.value,
                  onChanged: (value) {
                    mobileNumberObs.value = value;
                    _mobileNumber = value;
                  },
                  decoration: InputDecoration(
                    hintText: 'Enter Your Number',
                    hintStyle: TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                    ),
                    suffixIcon: (Platform.isIOS)
                        ? GestureDetector(
                            onTap: () {
                              FocusScope.of(context).unfocus();
                            },
                            child: Icon(Icons.done),
                          )
                        : null,
                  ),
                  // The validator receives the text that the user has entered.
                  validator: _validateMobileNumber,

                  style: new CSSStyle().poppinsLightBlackRegular17(context),
                )),
              ),
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: Text(
                  "We will send you an OTP to mobile number entered above",
                  textAlign: TextAlign.center,
                  style: new CSSStyle().poppinsLighterBlackRegular16(context),
                ),
              ),
              GestureDetector(
                onTap: () async{
                  FocusScope.of(context).unfocus();
                  if (_formKey.currentState!.validate()) {
                    final height = MediaQuery.of(context).size.height;
                    final width = MediaQuery.of(context).size.width;
                    // GetX reactive update - NO setState needed!
                    _saving = true;
                    FocusScope.of(context).unfocus();
                    showLoader = true;
                    isLoading.value = true;  // GetX automatically updates UI
                    //showLoaderObs.value = true;  // GetX reactive state
                    if (showLoader){
          showLoaderModal(context);
        }


                    await verifyLogin(
                        Constants.ACC_ID, _mobileNumber, fcmId, "1", context);
                    // ✅ FIXED: Don't automatically dismiss loader here
                    // Let verifyLogin method handle loader dismissal based on success/error
                    _saving = false;
                    isLoading.value = false;  // GetX automatically updates UI
                    // ✅ REMOVED: showLoader = false; - Let verifyLogin handle this
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.only(top: 20.0),
                  child: Image(
                    width: 60.0,
                    height: 60.0,
                    image: AssetImage('assets/images/frontarrow.png'),
                  ),
                ),
              ),
            ],
          ),
          Center(
            child: Container(
              margin: EdgeInsets.only(top: 20),
              alignment: Alignment.bottomCenter,
              child: Text(
                "Or Login with",
                style: TextStyle(
                  fontSize: 15,
                  letterSpacing: 0.4,
                  color: Colors.black,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
          InkWell(
            onTap: () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => UserNameVerificationScreen()));
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              margin: const EdgeInsets.only(top: 30),
              padding: const EdgeInsets.only(top: 13, bottom: 13),
              /* width: width,*/
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300, width: 1),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                children: [
                  Container(
                      margin: EdgeInsets.only(left: 10),
                      child: Icon(
                        Icons.person_outline_outlined,
                        color: Colors.black,
                      )),
                  Container(
                    margin: EdgeInsets.only(left: 10),
                    child: Text(
                      'Username and password',
                      style:
                          new CSSStyle().poppinsLighterBlackRegular16(context),
                    ),
                  ),
                ],
              ),
            ),
          ),
          InkWell(
            onTap: () {
              FocusScope.of(context).unfocus();
              Future.delayed(Duration.zero, () async {
                await startOtpless();
                //setState(() {});
               /*  if (mobileNumber.length == 10) {
                  verifyLogin(
                      Constants.ACC_ID, mobileNumber, fcmId, "0", context);
                  setState(() {});
                } */
              });
            },
            child: Container(
              alignment: Alignment.bottomCenter,
              margin: const EdgeInsets.only(top: 15),
              padding: const EdgeInsets.only(top: 13, bottom: 13),
              /* width: width,*/
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey.shade300, width: 1),
                borderRadius: BorderRadius.circular(5),
              ),
              child: Row(
                children: [
                  Container(
                    margin: EdgeInsets.only(left: 10),
                    child: Image.asset(
                      'assets/images/whatsapp.png',
                      height: 28,
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 10),
                    child: Text(
                      'WhatsApp',
                      style:
                          new CSSStyle().poppinsLighterBlackRegular16(context),
                    ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  String? _validateMobileNumber(String? user_name) {
    if (user_name == null || user_name.isEmpty) {
      return "Please enter mobile number";
    } else if (user_name.length < 10) {
      return "Please enter proper mobile number";
    } else {
      _mobileNumber = user_name;
      return null;
    }
  }

  void _handleRadioValueChange1(int value) {
    // GetX reactive update - NO setState needed!
    _radioValue1 = value;

    switch (_radioValue1) {
      case 0:
        break;
      case 1:
        break;
      case 2:
        break;
    }
  }

  Future<void> verifyLogin(String acc_id, String mobile_no, String fcm_id,
      String validate, BuildContext context) async {
    {
      print("Mobile Number Whatsapp $mobileNumber");
      Future.delayed(Duration(seconds: 1), () async {
        var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
        if (connectivityResult == ConnectivityResult.mobile ||
            connectivityResult == ConnectivityResult.wifi) {
          var responseJson =
          await callLoginApi1(acc_id, mobile_no, fcm_id, validate, context);
          int status = responseJson['success'];
          print("Number Login $responseJson");
        FocusScope.of(context).unfocus();
          if (status != 1) {
            // ✅ CLOSE LOADER IMMEDIATELY when error popup appears
            if (showLoader == true) {
              showLoader = false;
              Navigator.pop(context); // Close loader modal first
            }

            // Then show error dialog
            showDialog(
              context: context,
              barrierDismissible: false, // Prevent dismissing by tapping outside
              builder: (BuildContext context) {
                return CupertinoAlertDialog(
                  title: Text("Error"),
                  content: Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Text(responseJson['message'].toString()),
                  ),
                  actions: <Widget>[
                    CupertinoDialogAction(
                      onPressed: () {
                        Navigator.pop(context); // Close error dialog
                      },
                      isDefaultAction: true,
                      child: Text("OK"),
                    ),
                  ],
                );
              },
            );
          } else {
            // ✅ OPTIMIZED: Immediate loader dismissal
            showLoader = false;
            Navigator.pop(context);

            // ✅ OPTIMIZED: Parse response once
            ValidateCustomerMobileModel parsedResponse =
                ValidateCustomerMobileModel.fromJson(responseJson);
            int otp = responseJson['otp'];

            // ✅ OPTIMIZED: Async preference storage (non-blocking)
            _saveUserDataAsync(parsedResponse, mobile_no);

            // ✅ OPTIMIZED: Immediate navigation (don't wait for preferences)
            if (parsedResponse.otp == 0) {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => SetMPinActivity()),
              );
            } else {
              Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => SetOtpActivity(
                          mobile_Otp: otp.toString(),
                          mobileNumber: mobile_no,
                        )),
              );
            }

            // ✅ OPTIMIZED: Background API call (non-blocking)
            _signInApiCall();
          }
        } else {
          // ✅ CLOSE LOADER IMMEDIATELY when no internet error popup appears
          _saving = false;
          if (showLoader == true) {
            showLoader = false;
            Navigator.pop(context); // Close loader modal first
          }

          // Then show no internet dialog
          showDialog(
            context: context,
            barrierDismissible: false, // Prevent dismissing by tapping outside
            builder: (BuildContext context) {
              return CupertinoAlertDialog(
                title: Text("No Internet"),
                content: Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text("Check your connection"),
                ),
                actions: <Widget>[
                  CupertinoDialogAction(
                    onPressed: () {
                      Navigator.pop(context); // Close error dialog
                    },
                    isDefaultAction: true,
                    child: Text("OK"),
                  ),
                ],
              );
            },
          );
        }
      });
    }
  }

  // ✅ ADDED: Async method for non-blocking user data storage
  void _saveUserDataAsync(ValidateCustomerMobileModel parsedResponse, String mobile_no) async {
    try {
      // ✅ OPTIMIZED: Process only first company data (most common case)
      if (parsedResponse.data != null && parsedResponse.data!.isNotEmpty) {
        Map<String, dynamic> map = {
          'clientBusinessName': parsedResponse.data![0].clientBusinessName,
          'clientName': parsedResponse.data![0].clientName,
          'customerGroup': parsedResponse.data![0].customerGroup,
          'userId': parsedResponse.data![0].userId,
        };
        String rawJson = jsonEncode(map);

        // ✅ OPTIMIZED: Parallel async operations for faster storage
        await Future.wait([
          PreferenceManagerUtil().setListOfCompany(rawJson),
          PreferenceManagerUtil().setMobile(mobile_no),
          PreferenceManagerUtil().setMainUser(parsedResponse.data![0].clientName.toString()),
        ]);

        print("✅ User data saved successfully");
      }
    } catch (e) {
      print("⚠️ Error saving user data: $e");
      // Don't block navigation even if storage fails
    }
  }

  Widget _buildBackground() {
    return Stack(
      children: [
        Image.asset(
          'assets/images/login_background.png',
          width: double.infinity,
          fit: BoxFit.fill,
        ),
        Obx(() => ModalProgressHUD(
          inAsyncCall: _saving || isLoading.value,
          child: Container(
            height: double.infinity,
            child: SafeArea(
              child: Form(
                key: _formKey,
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.0),
                  ),
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        fillCards(),
                        _brandingUi(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ))
      ],
    );
  }

  _addOrUi() {
    return Container(
      margin: const EdgeInsets.only(top: 15.0, left: 00, right: 20),
      child: Row(children: <Widget>[
        Expanded(
          child: new Container(
              margin: const EdgeInsets.only(left: 20.0, right: 10.0),
              child: Divider(
                color: Colors.grey,
                height: 30,
              )),
        ),
        Text(
          "OR",
          style: new CSSStyle().poppinsGreyRegular15(context),
        ),
        Expanded(
          child: new Container(
              margin: const EdgeInsets.only(left: 10.0, right: 0.0),
              child: Divider(
                color: Colors.grey,
                height: 30,
              )),
        ),
      ]),
    );
  }

  void _showAlertDialog(String title, String message) {
    AlertDialog alertDialog = AlertDialog(
      title: Text(title),
      content: Text(message),
    );
    showDialog(context: context, builder: (_) => alertDialog);
  }

  void _signInApiCall() async {
    /*await databaseHelper.deleteAllUser();
    var noteMapList = await databaseHelper.getUserMapList();
    if (noteMapList.length <= 0) {
      int result = await databaseHelper.insertUser(userModel);
      if (result != 0) {
        handleTimeout();
      } else {
        MyUtils.showOkDialog(context, "Error", responseJson['emsg'].toString());
      }
    }*/

    //new PreferenceManagerUtil().setIsLogin(true);
  }

  String? _validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return CommonText().password;
    } else if (password.length < 6) {
      return CommonText().valid_password;
    } else {
      this.password = password;
      return null;
    }
  }

  String? _validateUserName(String? user_name) {
    if (user_name == null || user_name.isEmpty) {
      return CommonText().user_name;
    } else {
      userName = user_name;
      return null;
    }
  }

/*
  void verifyLogin(String userName, String password, BuildContext context) {
    if (userName.isEmpty) {
      _showAlertDialog("ERROR", "Enter Username");
    } else if (password.isEmpty) {
      _showAlertDialog("ERROR", "Enter Password");
    } else {
      setState(() {
        _saving = true;
      });
      Future.delayed(Duration(seconds: 1), () async {
        //   bool isTrustFall = await TrustFall.isJailBroken;

        var connectivityResult =
            await (Connectivity().checkConnectivity()); // User defined class
        //if (!isTrustFall) {
        if (connectivityResult == ConnectivityResult.mobile ||
            connectivityResult == ConnectivityResult.wifi) {
          var responseJson = await callLoginApi(
              */
/*  userName,
              new MyUtils().generateSignatureExtraCharacter(password),*/ /*

              "4101200300000073-00",
              "156387eef71097d807fca7db18d3922bb6bd0d132bf522c4a88978241112fd518461775985",
              myToken,
              context);
          setState(() {
            _saving = false;
          });
          String isError = responseJson['isError'].toString();

          if (isError == "1") {
            MyUtils.showOkDialog(
                context, "Error", responseJson['message'].toString());
          } else {
            LoginModel parsedResponse = LoginModel.fromJson(responseJson);
            new PreferenceManagerUtil()
                .setCorporateId(parsedResponse.result.corporateId);
            new PreferenceManagerUtil().setProposerDisplayName(
                parsedResponse.result.proposerDisplayName);
            new PreferenceManagerUtil()
                .setTokenId(parsedResponse.result.tokenID);
            new PreferenceManagerUtil()
                .setProposerPriPhone(parsedResponse.result.proposerPriPhone);
            new PreferenceManagerUtil()
                .setPolicyType(parsedResponse.result.policyType);
            new PreferenceManagerUtil()
                .setProposerPriMobile(parsedResponse.result.proposerPriMobile);
            new PreferenceManagerUtil()
                .setProposerPriEmail(parsedResponse.result.proposerPriEmail);
            new PreferenceManagerUtil()
                .setPolicyCode(parsedResponse.result.policyCode);
          */
/*  Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => DashBoardScreen()),
            );*/ /*

          }
        } else {
          setState(() {
            _saving = false;
          });

          MyUtils.showOkDialog(context, "No Internet", "Check your connection");
          // MyUtils.showToast("check your connection");
        }

        */
/*      } else {
          setState(() {
            _saving = false;
          });
          MyUtils.showOkDialog(
              context, "Sorry", "This Device is not secure");

        }*/ /*

      });
    }
  }
*/

  _addPasswordUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: TextFormField(
        keyboardType: TextInputType.text,
        //key: passKey,

        obscureText: _obscureText,
        inputFormatters: [],
        validator: _validatePassword,
        //   controller: passwordController,
        style: new CSSStyle().ptSansBlackRegular15(context),
        //validator: _validatePassword,
        decoration: InputDecoration(
          labelText: 'Password',
          hintText: 'Minimum 6 character',
          labelStyle: new CSSStyle().poppinsGreyRegular15(context),
          errorStyle: TextStyle(color: Colors.red, fontSize: 15),
          suffixIcon: Padding(
            padding: EdgeInsets.all(0.0),
            child: IconButton(
              onPressed: _toggle,
              icon: (_obscureText == false)
                  ? Icon(Icons.remove_red_eye)
                  : Icon(Icons.visibility_off),
              iconSize: 25,
              color: Colors.black54,
            ),
          ),
        ),
        cursorColor: Colors.black,
      ),
    );
  }

  _addForgotPasswordUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              InkWell(
                child: Text(
                  "Forgot Password?",
                  style: new CSSStyle().poppinsblueTextRegular12(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _addLoginWithOtpUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 8),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              InkWell(
                child: Text(
                  "Login with OTP",
                  style: new CSSStyle().poppinsblueTextRegular14(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _addRegistrationUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                "New to ERPCA ? ",
                style: new CSSStyle().ptSansBlackRegular16(context),
              ),
              InkWell(
                child: Text(
                  "Activate account",
                  style: new CSSStyle().poppinsblueTextRegular14(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _brandingUi() {
    String assetNameMale = 'images/eoxegen_logo.svg';

    return Padding(
      padding: const EdgeInsets.only(top: 15.0, bottom: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Column(
            children: [
              Text(
                "",
                style: new CSSStyle().poppinsGreyRegular10(context),
              ),
              Text(
                "",
                style: new CSSStyle().poppinsGreyRegular10(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _addRememberMeUi() {
    return Expanded(
      flex: 1,
      child: Container(
        margin: EdgeInsets.fromLTRB(15, 0, 0, 0),
        child: Row(
          children: [
            Checkbox(
                value: this.selected,
                checkColor: Colors.white,
                activeColor: Colors.green,
                onChanged: (val) {
                  // GetX reactive update - NO setState needed!
                  this.selected = !this.selected;
                }),
            Text(
              "Remember me ",
              style: new CSSStyle().poppinsGreyRegular10(context),
            )
          ],
        ),
      ),
    );
  }

  void _toggle() {
    // GetX reactive update - NO setState needed!
    _obscureText = !_obscureText;
    // Add GetX reactive variable if needed: obscureTextObs.value = !_obscureText;
  }
}


void showLoaderModal(BuildContext context) {
  // Ensure height and width are not null
  final double height = MediaQuery.of(context).size.height ?? 0.0;
  final double width = MediaQuery.of(context).size.width ?? 0.0;

  showModalBottomSheet(
    isDismissible: false,
    enableDrag: false,
    shape: const RoundedRectangleBorder(
      borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
    ),
    context: context,
    builder: (context) {
      return Container(
        height: 0.3 * height,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              child: MyUtils().kLoadingWidgetLarge(context),
            ),
            SizedBox(
              height: 0.1 * width,
            ),
            Container(
              child: Text(
                'Processing ...',
                style: TextStyle(fontSize: 0.06 * width),
              ),
            ),
          ],
        ),
      );
    },
  );
}
