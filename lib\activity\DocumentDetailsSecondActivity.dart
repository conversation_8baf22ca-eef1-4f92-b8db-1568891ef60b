import 'dart:io' as io;

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/TaskListFragment.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class DocumentDetailsSecondActivity extends StatefulWidget {
  String? asst_year;
  String? asst_year_Display;

  DocumentDetailsSecondActivity(
      {Key? key, this.asst_year, this.asst_year_Display})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return DocumentDetailsSecondActivityState();
  }
}

class DocumentDetailsSecondActivityState
    extends State<DocumentDetailsSecondActivity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          // Status bar color
          statusBarColor: Colors.white,

          // Status bar brightness (optional)
          statusBarIconBrightness: Brightness.dark, // For Android (dark icons)
          statusBarBrightness:
              io.Platform.isAndroid ? Brightness.light : Brightness.dark,
        ),
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        actions: [
          // IconButton(icon: Icon(Icons.search), onPressed: () {}),
          // IconButton(icon: Icon(Icons.notifications_none), onPressed: () {}),
        ],
        title: Text(
          widget.asst_year_Display.toString() + " Documents",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/

      body: DocumentDetailsSecondFragment(asst_year: widget.asst_year.toString()),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
