class WorkCategoryModel {
  int? _status;
  String? _message;
  int? _success;
  String? _errorMsg;
  List<Data>? _data;
  String? _errorDev;

  WorkCategoryModel(
      {int? status,
        String? message,
        int? success,
        String? errorMsg,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (errorMsg != null) {
      this._errorMsg = errorMsg;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get errorMsg => _errorMsg;
  set errorMsg(String? errorMsg) => _errorMsg = errorMsg;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  WorkCategoryModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _errorMsg = json['error_msg'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['error_msg'] = this._errorMsg;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _workcategoryId;
  String? _departmentId;
  String? _departmentName;
  String? _workcategory;
  String? _fees;

  Data(
      {String? workcategoryId,
        String? departmentId,
        String? departmentName,
        String? workcategory,
        String? fees}) {
    if (workcategoryId != null) {
      this._workcategoryId = workcategoryId;
    }
    if (departmentId != null) {
      this._departmentId = departmentId;
    }
    if (departmentName != null) {
      this._departmentName = departmentName;
    }
    if (workcategory != null) {
      this._workcategory = workcategory;
    }
    if (fees != null) {
      this._fees = fees;
    }
  }

  String? get workcategoryId => _workcategoryId;
  set workcategoryId(String? workcategoryId) =>
      _workcategoryId = workcategoryId;
  String? get departmentId => _departmentId;
  set departmentId(String? departmentId) => _departmentId = departmentId;
  String? get departmentName => _departmentName;
  set departmentName(String? departmentName) =>
      _departmentName = departmentName;
  String? get workcategory => _workcategory;
  set workcategory(String? workcategory) => _workcategory = workcategory;
  String? get fees => _fees;
  set fees(String? fees) => _fees = fees;

  Data.fromJson(Map<String, dynamic> json) {
    _workcategoryId = json['workcategory_id'];
    _departmentId = json['department_id'];
    _departmentName = json['department_name'];
    _workcategory = json['workcategory'];
    _fees = json['fees'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['workcategory_id'] = this._workcategoryId;
    data['department_id'] = this._departmentId;
    data['department_name'] = this._departmentName;
    data['workcategory'] = this._workcategory;
    data['fees'] = this._fees;
    return data;
  }
}
