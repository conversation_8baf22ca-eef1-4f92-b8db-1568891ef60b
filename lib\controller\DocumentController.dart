import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import 'package:get/get.dart';

String otp = '';
String mobile_no = '';
var responseJson;


Future<dynamic> coreDocumentListApi(
  String accId,
  String userId,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': accId,
    'user_id': userId,
  };
  return postWithContent(Constants.POST_CORE_DOCUMENT_LIST_URL, body, context);
}


Future<dynamic> customerDocumentScreen1Api(
  String acc_id,
  String user_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
  };

  return postWithContent(
      Constants.POST_CUSTOMER_DOCUMENT_SCREEN_1_URL, body, context);
}

Future<dynamic> serviceRequestListApi(
  String acc_id,
  String user_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
  };
  return postWithContent(
      Constants.POST_SERVICE_REQUEST_LIST_URL, body, context);
}

Future<dynamic> customerDocumentScreen2Api(
  String acc_id,
  String user_id,
  String asst_year,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'asst_year': asst_year,
  };

  return postWithContent(
      Constants.POST_CUSTOMER_DOCUMENT_SCREEN_2_URL, body, context);
}

Future<dynamic> customerOtherDocumentScreen2Api(
    String acc_id,
    String user_id,
    String folder_id,
    BuildContext context,
    ) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'folder_id': folder_id,
  };

  return postWithContent(
      Constants.POST_CUSTOMER_OTHER_DOCUMENT_SCREEN_2_URL, body, context);
}

Future<dynamic> customerCoreDocumentListApi(
  String acc_id,
  String user_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
  };
  return postWithContent(Constants.POST_CORE_DOCUMENT_LIST_URL, body, context);
}

Future<dynamic> customerDocumentScreen3Api(
  String acc_id,
  String user_id,
  String asst_year,
  String workcategory_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'asst_year': asst_year,
    'workcategory_id': workcategory_id,
  };

  return postWithContent(
      Constants.POST_CUSTOMER_DOCUMENT_SCREEN_3_URL, body, context);
}

Future<dynamic> post(String url, var body, BuildContext context) async {
  return await http.post(Uri.parse(url), body: body, headers: {
    "Accept": "application/json",
    "Cache-Control": "false",
    "eO2-Secret-Code": Constants.ACCESS_TOKEN
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> postWithContent(
    String url, var body, BuildContext context) async {

  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");

  return await http
      .post(Uri.parse(url), body: json.encode(body), headers: {
    "Id": "erpca",
    "Pass": "e736258681ac6d7126d298cc93a732db1dad2996",
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    final String responseBody = response.body;

    // ✅ API Response Logging
    log('[RESPONSE STATUS] : $statusCode');
    log('[RESPONSE BODY] : $responseBody');

    if (statusCode < 200 || statusCode > 500) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> postWithContentToken(
    String url, var body, String token, BuildContext context) async {
  return await http
      .post(Uri.parse(url), body: json.encode(body), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> getWithContent(
    String url, String token, var body, BuildContext context) async {
  return await http.get(Uri.parse(url), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}
