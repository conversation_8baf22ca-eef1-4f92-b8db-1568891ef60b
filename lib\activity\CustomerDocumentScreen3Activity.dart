import 'dart:io' as io;

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/CustomerDocumentScreen3Fragment.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class CustomerDocumentScreen3Activity extends StatefulWidget {
  String? asst_year;
  String? workcategory_id;
  String? docName;

  CustomerDocumentScreen3Activity(
      {Key? key, this.asst_year, this.workcategory_id, this.docName})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return CustomerDocumentScreen3ActivityState();
  }
}

class CustomerDocumentScreen3ActivityState
    extends State<CustomerDocumentScreen3Activity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          // Status bar color
          statusBarColor: Colors.white,

          // Status bar brightness (optional)
          statusBarIconBrightness: Brightness.dark, // For Android (dark icons)
          statusBarBrightness:
              io.Platform.isAndroid ? Brightness.light : Brightness.dark,
        ),
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          widget.docName.toString() + " Documents",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: CustomerDocumentScreen3Fragment(
        asst_year: widget.asst_year.toString(),
        workcategory_id: widget.workcategory_id.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
