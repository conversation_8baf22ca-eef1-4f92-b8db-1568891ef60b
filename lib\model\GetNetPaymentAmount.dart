class GetNetPaymentAmountModel {
  int? _status;
  String? _message;
  int? _success;
  List<AllData>? _allData;
  String? _errorDev;

  GetNetPaymentAmountModel(
      {int? status,
        String? message,
        int? success,
        List<AllData>? allData,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (allData != null) {
      this._allData = allData;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<AllData>? get allData => _allData;
  set allData(List<AllData>? allData) => _allData = allData;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  GetNetPaymentAmountModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['all_data'] != null) {
      _allData = <AllData>[];
      json['all_data'].forEach((v) {
        _allData!.add(new AllData.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._allData != null) {
      data['all_data'] = this._allData!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class AllData {
  int? _netAmount;

  AllData({int? netAmount}) {
    if (netAmount != null) {
      this._netAmount = netAmount;
    }
  }

  int? get netAmount => _netAmount;
  set netAmount(int? netAmount) => _netAmount = netAmount;

  AllData.fromJson(Map<String, dynamic> json) {
    _netAmount = json['net_amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['net_amount'] = this._netAmount;
    return data;
  }
}
