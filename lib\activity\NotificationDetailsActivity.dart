import 'dart:io' as io;

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/NotificationDetailsFragment.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class NotificationDetailsActivity extends StatefulWidget {
  String? msg_id;
  String? msg_Title;
  String? notification_type;

  NotificationDetailsActivity(
      {Key? key, this.msg_Title, this.msg_id, this.notification_type})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return NotificationDetailsActivityState();
  }
}

class NotificationDetailsActivityState
    extends State<NotificationDetailsActivity> {
  // GetX reactive variables
  var msgIdObs = ''.obs;
  var msgTitleObs = ''.obs;
  var notificationTypeObs = ''.obs;

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          // Status bar color
          statusBarColor: Colors.white,
          // Status bar brightness (optional)
          statusBarIconBrightness: Brightness.dark, // For Android (dark icons)
          statusBarBrightness:
              io.Platform.isAndroid ? Brightness.light : Brightness.dark,
        ),
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          widget.msg_Title.toString(),
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: NotificationDetailsFragment(
        msg_id: widget.msg_id.toString(),
        notification_type: widget.notification_type.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
