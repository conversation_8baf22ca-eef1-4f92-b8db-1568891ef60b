import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../common/Constants.dart';
import '../model/customer_create_folder_model.dart';
import '../model/customer_file_upload_model.dart';

Future<dynamic> customerCreateFolderApi(
    String accId,
    String userId,
    String parentId,
    String folderName,

    BuildContext context,
    ) async {

  try {
    Map<String, dynamic> requestData = {
      "acc_id": accId,
      "user_id": userId,
      "parent_id": parentId,
      "folder_name": folderName
    };

    // ✅ API Call Logging
    log('[URL] : ${Constants.CUSTOMER_CREATE_FOLDER_URL}\n[PARAMS] : ${json.encode(requestData)}');
    log("[URL] : ${Constants.CUSTOMER_CREATE_FOLDER_URL}");

    Dio _dio = Dio();
    final response = await _dio.post(
      Constants.CUSTOMER_CREATE_FOLDER_URL,
      data: json.encode(requestData),
      options: Options(
        headers: {
          "Id": "erpca",
          "Pass": "e736258681ac6d7126d298cc93a732db1dad2996",
          "Content-Type": "application/json",
        },
      ),
    );

    // ✅ API Response Logging
    log('[RESPONSE STATUS] : ${response.statusCode}');
    log('[RESPONSE BODY] : ${response.data}');

    int flag = response.data['success'];
    print('flag $flag ');
    CustomerCreateFolderModel currentParsedResponse =
    CustomerCreateFolderModel.fromJson(response.data);
    //await MyUtils.showBottomSheetSuccessDialog(context, "Folder Create Successfully", Colors.black);
    return currentParsedResponse;
  } on DioException catch (err) {  // ✅ FIXED: DioError renamed to DioException in dio 5.x
    final errorMessage = err;
    print('error msg $errorMessage');
    throw errorMessage;
  } catch (e) {
    print('e msg $e');
    throw e.toString();
  }
}

Future<dynamic> customerFileUploadApi(
    String accId,
    String userId,
    String documentTitle,
    String folderId,
    String fileToBeUploaded,

    BuildContext context,
    ) async {

  try {
    Map<String, dynamic> requestData = {
      "acc_id": accId,
      "user_id": userId,
      "document_title": documentTitle,
      "folder_id": folderId,
      "file_to_be_uploaded": fileToBeUploaded
    };

    // ✅ API Call Logging
    log('[URL] : ${Constants.CUSTOMER_FILE_UPLOAD_URL}\n[PARAMS] : ${json.encode(requestData)}');
    log("[URL] : ${Constants.CUSTOMER_FILE_UPLOAD_URL}");

    Dio _dio = Dio();
    final response = await _dio.post(
      Constants.CUSTOMER_FILE_UPLOAD_URL,
      data: json.encode(requestData),
      options: Options(
        headers: {
          "id": "erpca",
          "pass": "e736258681ac6d7126d298cc93a732db1dad2996",
          "Content-Type": "application/json",
        },
      ),
    );

    // ✅ API Response Logging
    log('[RESPONSE STATUS] : ${response.statusCode}');
    log('[RESPONSE BODY] : ${response.data}');

    int flag = response.data['success'];
    print('flag $flag ');
    CustomerFileUploadModel? currentParsedResponse =
    CustomerFileUploadModel?.fromJson(response.data);
    //await MyUtils.showBottomSheetSuccessDialog(context, "Success", Colors.black);
    return currentParsedResponse;
  } on DioException catch (err) {  // ✅ FIXED: DioError renamed to DioException in dio 5.x
    final errorMessage = err;
    print('error msg $errorMessage');
    throw errorMessage;
  } catch (e) {
    print('e msg $e');
    throw e.toString();
  }
}