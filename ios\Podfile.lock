PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - DKImagePickerController/Core (4.3.4):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.4)
  - DKImagePickerController/PhotoGallery (4.3.4):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.4)
  - DKPhotoGallery (0.0.17):
    - DKPhotoGallery/Core (= 0.0.17)
    - DKPhotoGallery/Model (= 0.0.17)
    - DKPhotoGallery/Preview (= 0.0.17)
    - DKPhotoGallery/Resource (= 0.0.17)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.17):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.17):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.17):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_downloader (0.0.1):
    - Flutter
  - flutter_email_sender (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_mailer (0.0.1):
    - Flutter
  - flutter_share (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.6.1)
  - image_picker_ios (0.0.1):
    - Flutter
  - modal_progress_hud_nsn (0.0.1):
    - Flutter
  - otpless_flutter (0.0.4):
    - Flutter
    - OtplessSDK (= 2.0.1)
  - OtplessSDK (2.0.1)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - "permission_handler (5.1.0+2)":
    - Flutter
  - razorpay-pod (1.3.2)
  - razorpay_flutter (1.1.10):
    - Flutter
    - razorpay-pod
  - ReachabilitySwift (5.0.0)
  - SDWebImage (5.15.8):
    - SDWebImage/Core (= 5.15.8)
  - SDWebImage/Core (5.15.8)
  - share (0.0.1):
    - Flutter
  - share_extend (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - SwiftyGif (5.4.4)
  - Toast (4.0.0)
  - TOCropViewController (2.6.1)
  - uni_links (0.0.1):
    - Flutter
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_downloader (from `.symlinks/plugins/flutter_downloader/ios`)
  - flutter_email_sender (from `.symlinks/plugins/flutter_email_sender/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_mailer (from `.symlinks/plugins/flutter_mailer/ios`)
  - flutter_share (from `.symlinks/plugins/flutter_share/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - modal_progress_hud_nsn (from `.symlinks/plugins/modal_progress_hud_nsn/ios`)
  - otpless_flutter (from `.symlinks/plugins/otpless_flutter/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - razorpay_flutter (from `.symlinks/plugins/razorpay_flutter/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - share_extend (from `.symlinks/plugins/share_extend/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - uni_links (from `.symlinks/plugins/uni_links/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - FMDB
    - OtplessSDK
    - razorpay-pod
    - ReachabilitySwift
    - SDWebImage
    - SwiftyGif
    - Toast
    - TOCropViewController

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_downloader:
    :path: ".symlinks/plugins/flutter_downloader/ios"
  flutter_email_sender:
    :path: ".symlinks/plugins/flutter_email_sender/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_mailer:
    :path: ".symlinks/plugins/flutter_mailer/ios"
  flutter_share:
    :path: ".symlinks/plugins/flutter_share/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  modal_progress_hud_nsn:
    :path: ".symlinks/plugins/modal_progress_hud_nsn/ios"
  otpless_flutter:
    :path: ".symlinks/plugins/otpless_flutter/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  razorpay_flutter:
    :path: ".symlinks/plugins/razorpay_flutter/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  share_extend:
    :path: ".symlinks/plugins/share_extend/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  uni_links:
    :path: ".symlinks/plugins/uni_links/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity_plus: bf0076dd84a130856aa636df1c71ccaff908fa1d
  DKImagePickerController: b512c28220a2b8ac7419f21c491fc8534b7601ac
  DKPhotoGallery: fdfad5125a9fdda9cc57df834d49df790dbb4179
  file_picker: ce3938a0df3cc1ef404671531facef740d03f920
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_downloader: b7301ae057deadd4b1650dc7c05375f10ff12c39
  flutter_email_sender: 02d7443217d8c41483223627972bfdc09f74276b
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  flutter_mailer: 2ef5a67087bc8c6c4cefd04a178bf1ae2c94cd83
  flutter_share: 4be0208963c60b537e6255ed2ce1faae61cd9ac2
  fluttertoast: 31b00dabfa7fb7bacd9e7dbee580d7a2ff4bf265
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  image_cropper: 60c2789d1f1a78c873235d4319ca0c34a69f2d98
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  modal_progress_hud_nsn: f6fb744cd060653d66ed8f325360ef3650eb2fde
  otpless_flutter: 8c67abdc7821fb8eba66596f41aba022aceb6c9f
  OtplessSDK: b8bbd2e85bee4ebdadb7f5503dc8d596067cd84b
  package_info_plus: fd030dabf36271f146f1f3beacd48f564b0f17f7
  path_provider_foundation: eaf5b3e458fc0e5fbb9940fb09980e853fe058b8
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0
  razorpay-pod: b129f60de5f0d0952bebfb9480a10909d4fd6b6d
  razorpay_flutter: 84b3bfd206ae9c9c2a9ba585524a1b3d8102b6c1
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  SDWebImage: cb032eba469c54e0000e78bcb0a13cdde0a52798
  share: 0b2c3e82132f5888bccca3351c504d0003b3b410
  share_extend: b6748dc53695587891126a89533b862b92548c7b
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_foundation: e2dae3258e06f44cc55f49d42024fd8dd03c590c
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  SwiftyGif: 93a1cc87bf3a51916001cf8f3d63835fb64c819f
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  TOCropViewController: edfd4f25713d56905ad1e0b9f5be3fbe0f59c863
  uni_links: d97da20c7701486ba192624d99bffaaffcfc298a
  url_launcher_ios: 08a3dfac5fb39e8759aeb0abbd5d9480f30fc8b4
  webview_flutter_wkwebview: b7e70ef1ddded7e69c796c7390ee74180182971f

PODFILE CHECKSUM: 4e8f8b2be68aeea4c0d5beb6ff1e79fface1d048

COCOAPODS: 1.14.3
