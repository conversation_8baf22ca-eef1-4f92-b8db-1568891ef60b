// Splash Controller
// Manages splash screen logic and app initialization using GetX

import 'package:get/get.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../services/connectivity_service.dart';
import '../../routes/app_routes.dart';

class SplashController extends GetxController {
  final ApiService _apiService = Get.find<ApiService>();
  final StorageService _storageService = Get.find<StorageService>();
  final ConnectivityService _connectivityService = Get.find<ConnectivityService>();

  // Reactive variables
  final isLoading = true.obs;
  final loadingMessage = 'Initializing...'.obs;
  final appVersion = ''.obs;
  final isUpdateRequired = false.obs;

  @override
  void onInit() {
    super.onInit();
    initializeApp();
  }

  // Initialize the app
  Future<void> initializeApp() async {
    try {
      // Check connectivity
      await checkConnectivity();
      
      // Check app version
      await checkAppVersion();
      
      // Initialize services
      await initializeServices();
      
      // Check authentication status
      await checkAuthenticationStatus();
      
    } catch (e) {
      print('App initialization error: $e');
      handleInitializationError();
    }
  }

  // Check internet connectivity
  Future<void> checkConnectivity() async {
    loadingMessage.value = 'Checking connectivity...';
    
    final isConnected = await _connectivityService.isConnected();
    if (!isConnected) {
      // Show connectivity error and retry
      Get.snackbar(
        'No Internet',
        'Please check your internet connection',
        snackPosition: SnackPosition.BOTTOM,
      );
      
      // Wait and retry
      await Future.delayed(Duration(seconds: 2));
      await checkConnectivity();
    }
  }

  // Check app version and update requirements
  Future<void> checkAppVersion() async {
    try {
      loadingMessage.value = 'Checking app version...';
      
      final versionInfo = await _apiService.get('/app/version');
      
      if (versionInfo['success']) {
        final currentVersion = await _getCurrentAppVersion();
        final requiredVersion = versionInfo['required_version'];
        final latestVersion = versionInfo['latest_version'];
        
        appVersion.value = currentVersion;
        
        if (_isVersionOutdated(currentVersion, requiredVersion)) {
          isUpdateRequired.value = true;
          navigateToUpdateScreen();
          return;
        }
      }
    } catch (e) {
      print('Version check error: $e');
      // Continue with app initialization even if version check fails
    }
  }

  // Initialize core services
  Future<void> initializeServices() async {
    loadingMessage.value = 'Initializing services...';
    
    // Initialize API service
    await _apiService.initialize();
    
    // Initialize storage service
    await _storageService.initialize();
    
    // Add any other service initialization here
    await Future.delayed(Duration(milliseconds: 500)); // Minimum splash time
  }

  // Check user authentication status
  Future<void> checkAuthenticationStatus() async {
    try {
      loadingMessage.value = 'Checking authentication...';
      
      final token = await _storageService.getToken();
      final mpin = await _storageService.getMPin();
      
      if (token.isNotEmpty && mpin.isNotEmpty) {
        // User is logged in, go to home
        navigateToHome();
      } else if (mpin.isNotEmpty) {
        // User has MPIN set, go to MPIN verification
        navigateToMPinVerification();
      } else {
        // New user, go to login
        navigateToLogin();
      }
      
    } catch (e) {
      print('Authentication check error: $e');
      navigateToLogin();
    }
  }

  // Navigation methods
  void navigateToHome() {
    isLoading.value = false;
    Get.offAllNamed(Routes.HOME);
  }

  void navigateToLogin() {
    isLoading.value = false;
    Get.offAllNamed(Routes.LOGIN_MOBILE);
  }

  void navigateToMPinVerification() {
    isLoading.value = false;
    Get.offAllNamed(Routes.CHECK_MPIN);
  }

  void navigateToUpdateScreen() {
    isLoading.value = false;
    // Navigate to update screen (implement as needed)
    Get.snackbar(
      'Update Required',
      'Please update the app to continue',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 5),
    );
  }

  // Handle initialization errors
  void handleInitializationError() {
    isLoading.value = false;
    loadingMessage.value = 'Initialization failed';
    
    Get.snackbar(
      'Error',
      'Failed to initialize app. Please try again.',
      snackPosition: SnackPosition.BOTTOM,
      onTap: (_) => retryInitialization(),
    );
  }

  // Retry initialization
  Future<void> retryInitialization() async {
    isLoading.value = true;
    loadingMessage.value = 'Retrying...';
    await initializeApp();
  }

  // Helper methods
  Future<String> _getCurrentAppVersion() async {
    // Get current app version from package info
    // This is a placeholder - implement with package_info_plus
    return '1.0.0';
  }

  bool _isVersionOutdated(String current, String required) {
    // Compare version strings
    // This is a simplified version comparison
    final currentParts = current.split('.').map(int.parse).toList();
    final requiredParts = required.split('.').map(int.parse).toList();
    
    for (int i = 0; i < 3; i++) {
      if (currentParts[i] < requiredParts[i]) {
        return true;
      } else if (currentParts[i] > requiredParts[i]) {
        return false;
      }
    }
    return false;
  }

  // Update loading message for external use
  void updateLoadingMessage(String message) {
    loadingMessage.value = message;
  }

  // Check if app is ready
  bool get isAppReady => !isLoading.value;
}
