import 'dart:io' as io;

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../fragment/otherFoldersListFragment.dart';

class OtherDocumentDetailsSecondActivity extends StatefulWidget {
  String? accId;
  String? userId;
  String? folderId;
  String? folderName;

  OtherDocumentDetailsSecondActivity(
      {Key? key, this.accId, this.userId, this.folderId, this.folderName})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return OtherDocumentDetailsSecondActivityState();
  }
}

class OtherDocumentDetailsSecondActivityState
    extends State<OtherDocumentDetailsSecondActivity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          // Status bar color
          statusBarColor: Colors.white,

          // Status bar brightness (optional)
          statusBarIconBrightness: Brightness.dark, // For Android (dark icons)
          statusBarBrightness:
              io.Platform.isAndroid ? Brightness.light : Brightness.dark,
        ),
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        actions: [
          // IconButton(icon: Icon(Icons.search), onPressed: () {}),
          // IconButton(icon: Icon(Icons.notifications_none), onPressed: () {}),
        ],
        title: Text(
          widget.folderName.toString() + " Documents",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/

      body: OtherDocumentDetailsSecondFragment(
        acc_id: widget.accId.toString(),
        user_id: widget.userId.toString(),
        folder_id: widget.folderId.toString(),
        folder_name: widget.folderName.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
