class InvoiceListModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  InvoiceListModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  InvoiceListModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _billId;
  String? _invoiceType;
  String? _billNo;
  String? _billDate;
  String? _netAmount;
  String? _billStatus;
  String? _amountPaid;
  String? _balanceToBePaid;
  String? _billLink;
  String? _billDescription;
  String? _billingCompanyName;

  Data(
      {String? billId,
        String? invoiceType,
        String? billNo,
        String? billDate,
        String? netAmount,
        String? billStatus,
        String? amountPaid,
        String? balanceToBePaid,
        String? billLink,
        String? billDescription,
        String? billingCompanyName}) {
    if (billId != null) {
      this._billId = billId;
    }
    if (invoiceType != null) {
      this._invoiceType = invoiceType;
    }
    if (billNo != null) {
      this._billNo = billNo;
    }
    if (billDate != null) {
      this._billDate = billDate;
    }
    if (netAmount != null) {
      this._netAmount = netAmount;
    }
    if (billStatus != null) {
      this._billStatus = billStatus;
    }
    if (amountPaid != null) {
      this._amountPaid = amountPaid;
    }
    if (balanceToBePaid != null) {
      this._balanceToBePaid = balanceToBePaid;
    }
    if (billLink != null) {
      this._billLink = billLink;
    }
    if (billDescription != null) {
      this._billDescription = billDescription;
    }
    if (billingCompanyName != null) {
      this._billingCompanyName = billingCompanyName;
    }
  }

  String? get billId => _billId;
  set billId(String? billId) => _billId = billId;
  String? get invoiceType => _invoiceType;
  set invoiceType(String? invoiceType) => _invoiceType = invoiceType;
  String? get billNo => _billNo;
  set billNo(String? billNo) => _billNo = billNo;
  String? get billDate => _billDate;
  set billDate(String? billDate) => _billDate = billDate;
  String? get netAmount => _netAmount;
  set netAmount(String? netAmount) => _netAmount = netAmount;
  String? get billStatus => _billStatus;
  set billStatus(String? billStatus) => _billStatus = billStatus;
  String? get amountPaid => _amountPaid;
  set amountPaid(String? amountPaid) => _amountPaid = amountPaid;
  String? get balanceToBePaid => _balanceToBePaid;
  set balanceToBePaid(String? balanceToBePaid) =>
      _balanceToBePaid = balanceToBePaid;
  String? get billLink => _billLink;
  set billLink(String? billLink) => _billLink = billLink;
  String? get billDescription => _billDescription;
  set billDescription(String? billDescription) =>
      _billDescription = billDescription;
  String? get billingCompanyName => _billingCompanyName;
  set billingCompanyName(String? billingCompanyName) =>
      _billingCompanyName = billingCompanyName;

  Data.fromJson(Map<String, dynamic> json) {
    _billId = json['bill_id'];
    _invoiceType = json['invoice_type'];
    _billNo = json['bill_no'];
    _billDate = json['bill_date'];
    _netAmount = json['net_amount'];
    _billStatus = json['bill_status'];
    _amountPaid = json['amount_paid'];
    _balanceToBePaid = json['balance_to_be_paid'];
    _billLink = json['bill_link'];
    _billDescription = json['bill_description'];
    _billingCompanyName = json['billing_company_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['bill_id'] = this._billId;
    data['invoice_type'] = this._invoiceType;
    data['bill_no'] = this._billNo;
    data['bill_date'] = this._billDate;
    data['net_amount'] = this._netAmount;
    data['bill_status'] = this._billStatus;
    data['amount_paid'] = this._amountPaid;
    data['balance_to_be_paid'] = this._balanceToBePaid;
    data['bill_link'] = this._billLink;
    data['bill_description'] = this._billDescription;
    data['billing_company_name'] = this._billingCompanyName;
    return data;
  }
}
