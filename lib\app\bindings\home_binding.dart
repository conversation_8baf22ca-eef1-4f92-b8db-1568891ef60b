// Home Binding
// Manages dependency injection for home-related controllers and services

import 'package:get/get.dart';
import '../controllers/home_controller.dart';
import '../controllers/task_controller.dart';
import '../services/api_service.dart';
import '../services/task_service.dart';
import '../services/document_service.dart';
import '../services/invoice_service.dart';
import '../services/storage_service.dart';

class HomeBinding extends Bindings {
  @override
  void dependencies() {
    // Services
    Get.lazyPut<ApiService>(() => ApiService());
    Get.lazyPut<StorageService>(() => StorageService());
    Get.lazyPut<TaskService>(() => TaskService());
    Get.lazyPut<DocumentService>(() => DocumentService());
    Get.lazyPut<InvoiceService>(() => InvoiceService());
    
    // Controllers
    Get.lazyPut<HomeController>(() => HomeController());
    Get.lazyPut<TaskController>(() => TaskController());
  }
}
