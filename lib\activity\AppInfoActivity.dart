

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsFragment.dart';
import 'package:erpcacustomer/fragment/NotificationFragment.dart';
import 'package:erpcacustomer/fragment/AppInfoFragment.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AppInfoActivity extends StatefulWidget {
  AppInfoActivity({ Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return AppInfoActivityState();
  }
}

class AppInfoActivityState extends State<AppInfoActivity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          "App Info",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: AppInfoFragment(),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
