// Authentication Service
// Handles all authentication-related API calls and business logic

import 'package:get/get.dart';
import 'api_service.dart';
import 'storage_service.dart';

class AuthService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();
  final StorageService _storageService = Get.find<StorageService>();

  // Login with mobile number
  Future<Map<String, dynamic>> loginWithMobile(String mobileNumber) async {
    try {
      final response = await _apiService.post('/auth/login-mobile', body: {
        'mobile_number': mobileNumber,
      });

      if (response['success']) {
        // Store mobile number for later use
        await _storageService.saveContactPhone(mobileNumber);
        
        return {
          'success': true,
          'message': 'OTP sent successfully',
          'data': response['data'],
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Login failed',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Login with username and password
  Future<Map<String, dynamic>> loginWithCredentials(String username, String password) async {
    try {
      final response = await _apiService.post('/auth/login', body: {
        'username': username,
        'password': password,
      });

      if (response['success']) {
        final data = response['data'];
        
        // Store authentication data
        await _storageService.saveToken(data['token']);
        await _storageService.saveUserName(username);
        
        // Update API service with token
        _apiService.setAuthToken(data['token']);
        
        return {
          'success': true,
          'message': 'Login successful',
          'token': data['token'],
          'user': data['user'],
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Invalid credentials',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Verify OTP
  Future<Map<String, dynamic>> verifyOTP(String mobileNumber, String otp) async {
    try {
      final response = await _apiService.post('/auth/verify-otp', body: {
        'mobile_number': mobileNumber,
        'otp': otp,
      });

      if (response['success']) {
        final data = response['data'];
        
        // Store OTP verification data
        await _storageService.saveContactPhone(mobileNumber);
        
        return {
          'success': true,
          'message': 'OTP verified successfully',
          'data': data,
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Invalid OTP',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Get user companies/organizations
  Future<Map<String, dynamic>> getUserCompanies(String mobileNumber) async {
    try {
      final response = await _apiService.get('/auth/companies?mobile=$mobileNumber');

      if (response['success']) {
        return {
          'success': true,
          'companies': response['data']['companies'],
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to load companies',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Select company and complete authentication
  Future<Map<String, dynamic>> selectCompany(String companyId, String mobileNumber) async {
    try {
      final response = await _apiService.post('/auth/select-company', body: {
        'company_id': companyId,
        'mobile_number': mobileNumber,
      });

      if (response['success']) {
        final data = response['data'];
        
        // Store authentication data
        await _storageService.saveToken(data['token']);
        await _storageService.saveSelectedCompany(data['company']['name']);
        await _storageService.saveUserName(data['user']['name']);
        
        // Update API service with token
        _apiService.setAuthToken(data['token']);
        
        return {
          'success': true,
          'message': 'Company selected successfully',
          'token': data['token'],
          'user': data['user'],
          'company': data['company'],
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to select company',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Refresh authentication token
  Future<Map<String, dynamic>> refreshToken() async {
    try {
      final response = await _apiService.post('/auth/refresh');

      if (response['success']) {
        final newToken = response['data']['token'];
        
        // Update stored token
        await _storageService.saveToken(newToken);
        _apiService.setAuthToken(newToken);
        
        return {
          'success': true,
          'token': newToken,
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to refresh token',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Logout
  Future<Map<String, dynamic>> logout() async {
    try {
      // Call logout API
      await _apiService.post('/auth/logout');
      
      // Clear local storage
      await _storageService.clearAll();
      
      // Clear API service token
      _apiService.clearAuthToken();
      
      return {
        'success': true,
        'message': 'Logged out successfully',
      };
    } catch (e) {
      // Even if API call fails, clear local data
      await _storageService.clearAll();
      _apiService.clearAuthToken();
      
      return {
        'success': true,
        'message': 'Logged out successfully',
      };
    }
  }

  // Check authentication status
  Future<bool> isAuthenticated() async {
    try {
      final token = await _storageService.getToken();
      if (token.isEmpty) return false;
      
      // Verify token with server
      final response = await _apiService.get('/auth/verify');
      return response['success'];
    } catch (e) {
      return false;
    }
  }

  // Get current user info
  Future<Map<String, dynamic>> getCurrentUser() async {
    try {
      final response = await _apiService.get('/auth/user');

      if (response['success']) {
        return {
          'success': true,
          'user': response['data']['user'],
        };
      } else {
        return {
          'success': false,
          'message': response['message'] ?? 'Failed to get user info',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Change password
  Future<Map<String, dynamic>> changePassword(String currentPassword, String newPassword) async {
    try {
      final response = await _apiService.post('/auth/change-password', body: {
        'current_password': currentPassword,
        'new_password': newPassword,
      });

      return {
        'success': response['success'],
        'message': response['message'] ?? (response['success'] ? 'Password changed successfully' : 'Failed to change password'),
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }

  // Reset password
  Future<Map<String, dynamic>> resetPassword(String mobileNumber) async {
    try {
      final response = await _apiService.post('/auth/reset-password', body: {
        'mobile_number': mobileNumber,
      });

      return {
        'success': response['success'],
        'message': response['message'] ?? (response['success'] ? 'Reset link sent successfully' : 'Failed to send reset link'),
      };
    } catch (e) {
      return {
        'success': false,
        'message': 'Network error occurred',
      };
    }
  }
}
