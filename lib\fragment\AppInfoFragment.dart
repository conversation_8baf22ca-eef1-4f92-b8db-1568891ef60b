import 'dart:async';
import 'dart:io';
import 'dart:ui';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import 'package:erpcacustomer/activity/LoginActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/MobileInfoModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';


class AppInfoFragment extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return AppInfoFragmentState();
  }
}

class AppInfoFragmentState extends State<AppInfoFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var savingObs = false.obs;
  var selectedIndexObs = 0.obs;
  var selectedUserObs = ''.obs;
  var selectedBuisnessObs = ''.obs;

  // Original variables (keeping for compatibility)
  String actualName = "";
  String actualEmail = "";
  String profilePic = "";
  String departmentName = "";
  String designationName = "";
  String dateOfBirth = "";
  DateTime? dateOfBirthDate;
  String actualContact = "";
  ImagePickerHandler? imagePicker;
  AnimationController? _controller;
  bool _saving = false;
  TabController? _tabController;
  int _selectedIndex = 0;
  File? _image;
  String selectedUser = "";
  String selectedBuisness = "";

  @override
  Widget build(BuildContext context) {
    // ✅ OPTIMIZED: Obx wrapper for GetX reactive updates
    return Obx(() => ModalProgressHUD(
      inAsyncCall: savingObs.value,
      child: Scaffold(
        body: FutureBuilder<MobileInfoModel>(
          future: postGetMobileAppAccountInfoCall(
              Constants.ACC_ID, Constants.USER_ID, context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? Stack(
                    children: [
                      Image.asset(
                        'assets/images/login_background.png',
                        width: double.infinity,
                        fit: BoxFit.fill,
                      ),
                      SingleChildScrollView(
                        child: Column(
                          children: <Widget>[
                            GestureDetector(
                                onTap: () {
                                  // imagePicker.showDialog(context);
                                },
                                child: (snapshot.data!.data![0].logoThumb == "")
                                    ? Padding(
                                        padding:
                                            const EdgeInsets.only(top: 25.0),
                                        child: Center(
                                          child: Container(
                                            child: Icon(
                                              Icons.account_circle,
                                              color: Color(new CommonColor()
                                                  .lightest_grey_Color),
                                              size: 80,
                                            ),
                                          ),
                                        ),
                                      )
                                    : Padding(
                                        padding:
                                            const EdgeInsets.only(top: 15.0),
                                        child: Padding(
                                          padding: EdgeInsets.only(
                                              right: 8, left: 0),
                                          child: Container(
                                            height: 100,
                                            decoration: new BoxDecoration(
                                                image: new DecorationImage(
                                                    image: new NetworkImage(
                                                        snapshot.data!.data![0]
                                                            .logoThumb.toString()))),
                                          ),
                                        ),
                                      )),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 15, top: 30, right: 15),
                              child: Column(
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        top: 5.0, bottom: 20),
                                    child: Center(
                                      child: Text(
                                        snapshot.data!.data![0].organizationName.toString(),
                                        style: new CSSStyle()
                                            .poppinsGreyRegular15(context),
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 13.0, right: 13, top: 5),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            /* Text(
                                          "Name",
                                          style: new CSSStyle()
                                              .poppinsBlackBold13W400(context),
                                        ),*/
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 5.0),
                                              child: Text(
                                                snapshot
                                                    .data!.data![0].contactPerson.toString(),
                                                style: new CSSStyle()
                                                    .poppinsGreyRegular14(
                                                        context),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  Divider(),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 13.0, right: 13, top: 5),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Text(
                                              "Address",
                                              style: new CSSStyle()
                                                  .poppinsBlackBold13W400(
                                                      context),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 5.0),
                                              child: Container(
                                                width: 300,
                                                child: Text(
                                                  snapshot.data!.data![0]
                                                          .address.toString() +
                                                      " " +
                                                      snapshot.data!.data![0]
                                                          .cityName.toString() +
                                                      " " +
                                                      snapshot.data!.data![0]
                                                          .stateName.toString() +
                                                      " " +
                                                      snapshot
                                                          .data!.data![0].pincode.toString(),
                                                  style: new CSSStyle()
                                                      .poppinsGreyRegular12(
                                                          context),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                  Divider(),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 13.0, right: 13, top: 5),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 4,
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Text(
                                                "Mobile No",
                                                style: new CSSStyle()
                                                    .poppinsBlackBold13W400(
                                                        context),
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 5.0),
                                                child: Container(
                                                  width: 300,
                                                  child: Text(
                                                    snapshot.data!.data![0]
                                                        .contactNumber.toString(),
                                                    style: new CSSStyle()
                                                        .poppinsGreyRegular12(
                                                            context),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            children: [
                                              GestureDetector(
                                                onTap: () {
                                                  new MyUtils().callMe(
                                                      snapshot.data!.data![0]
                                                          .contactNumber.toString(),
                                                      context);
                                                },
                                                child: Icon(
                                                  Icons.local_phone_outlined,
                                                  size: 25,
                                                  color: Color(new CommonColor()
                                                      .green_light_Color),
                                                ),
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    left: 5.0),
                                                child: GestureDetector(
                                                  onTap: () {
                                                    new MyUtils()
                                                        .launchWhatsApp(snapshot
                                                            .data!
                                                            .data![0]
                                                            .contactNumber.toString());
                                                  },
                                                  child: Padding(
                                                    padding:
                                                        const EdgeInsets.all(
                                                            2.0),
                                                    child: GestureDetector(
                                                      onTap: () {
                                                        new MyUtils()
                                                            .launchWhatsApp(snapshot
                                                                .data!
                                                                .data![0]
                                                                .contactNumber.toString());
                                                      },
                                                      child: FaIcon(
                                                        FontAwesomeIcons
                                                            .whatsapp,
                                                        size: 20,
                                                        color:
                                                            Colors.green[500],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  Divider(),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 13.0, right: 13, top: 5),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          flex: 9,
                                          child: Column(
                                            mainAxisSize: MainAxisSize.max,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              Text(
                                                "Email",
                                                style: new CSSStyle()
                                                    .poppinsBlackBold13W400(
                                                        context),
                                              ),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 5.0),
                                                child: Container(
                                                  width: 300,
                                                  child: Text(
                                                    snapshot.data!.data![0]
                                                        .contactEmail.toString(),
                                                    style: new CSSStyle()
                                                        .poppinsGreyRegular12(
                                                            context),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Expanded(
                                          flex: 1,
                                          child: Row(
                                            children: [
                                              GestureDetector(
                                                onTap: () {
                                                  new MyUtils().sendMail(
                                                      snapshot.data!.data![0]
                                                          .contactEmail.toString(),
                                                      "",
                                                      "",
                                                      context);
                                                },
                                                child: Icon(
                                                  Icons.attach_email,
                                                  color: Color(new CommonColor()
                                                      .green_light_Color),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                  Divider(),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        left: 13.0, right: 13, top: 5),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            Text(
                                              "App Version",
                                              style: new CSSStyle()
                                                  .poppinsBlackBold13W400(
                                                      context),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 5.0),
                                              child: Container(
                                                width: 300,
                                                child: Text(
                                                  snapshot
                                                      .data!.data![0].appVersion.toString(),
                                                  style: new CSSStyle()
                                                      .poppinsGreyRegular12(
                                                          context),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  )
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
      ),
    )); // Close Obx
  }

  getMobileAppAccountInfoCall(
      String acc_id, String user_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await getMobileAppAccountInfoApi(acc_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        MobileInfoModel currentParsedResponse =
            MobileInfoModel.fromJson(responseJson);

        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

/*   postGetMobileAppAccountInfoCall(
      String acc_id, String user_id, BuildContext context) async {
    return await getMobileAppAccountInfoCall(acc_id, user_id, context);
  } */
 Future<MobileInfoModel> postGetMobileAppAccountInfoCall(
    String acc_id, String user_id, BuildContext context) async {
  return await getMobileAppAccountInfoCall(acc_id, user_id, context);
}


  _signoutMethodCall() async {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LoginActivity()),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    Future<String> token = new PreferenceManagerUtil().getMainUser();
    token.then((value) {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedUser = value;
      selectedUserObs.value = value;
      // setState(() { selectedUser = value; }); // Removed
    });
    Future<String> tokenBuisness =
        new PreferenceManagerUtil().getBuisnessName();
    tokenBuisness.then((value) {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedBuisness = value;
      selectedBuisnessObs.value = value;
      // setState(() { selectedBuisness = value; }); // Removed
    });
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
    _tabController = TabController(length: 3, vsync: this);
    _controller!.addListener(() {
      // ✅ OPTIMIZED: GetX reactive state management
      _selectedIndex = _tabController!.index;
      selectedIndexObs.value = _selectedIndex;
      // setState(() { _selectedIndex = _tabController!.index; }); // Removed
    });
  }

  _addForgotPasswordButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 0),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 10.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_blue_Color),
          Color(new CommonColor().oxygen_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'Reset Password',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginActivity(),
              ));
        },
        increaseWidthBy: 100.0,
        increaseHeightBy: 10.0,
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // GetX reactive update - NO setState needed!
      this._image = _image;
    }
  }

  DateTime convertDateFromString(String strDate) {
    DateTime todayDate = DateTime.parse(strDate);
    ;
    return todayDate;
  }
}
