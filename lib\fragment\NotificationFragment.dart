import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:erpcacustomer/activity/DocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/NotificationDetailsActivity.dart';
import 'package:erpcacustomer/activity/SubmitRequestActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:erpcacustomer/model/NotificationModel.dart';
import 'package:erpcacustomer/model/DocumentRequiredDeleteModel.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'dart:io' as Io;
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:flutter/services.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:background_app_bar/background_app_bar.dart';
import 'dart:ui';
import 'package:erpcacustomer/common/Constants.dart';
/*import 'package:open_file/open_file.dart';*/
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';

class NotificationFragment extends StatefulWidget {
  NotificationFragment({Key? key}) : super(key: key);

  @override
  NotificationFragmentState createState() {
    return new NotificationFragmentState();
  }
}

class NotificationFragmentState extends State<NotificationFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // GetX reactive variables
  var hasCardObs = false.obs;
  var documentPrimaryKeyIdObs = ''.obs;
  var isLoadingPathObs = false.obs;
  var directoryPathObs = ''.obs;

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  ImagePickerHandler? imagePicker;
  File? _image;
  AnimationController? _controller;
  String document_primary_key_id = "";
  bool _loadingPath = false;
  String _directoryPath = '';
  List<PlatformFile>? _paths;
  FileType _pickingType = FileType.any;
  bool _multiPick = false;
  String _extension = '';
  String _fileName = '';
  bool _saving = false;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
  }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Material(
        child: Stack(
          children: [
            _buildBackground(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
  return ModalProgressHUD(
    inAsyncCall: _saving,
    child: FutureBuilder<NotificationModel>(
      future: notificationListCall(Constants.ACC_ID, Constants.USER_ID, context),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: new MyUtils().kLoadingWidget(context),
          );
        }

        if (snapshot.hasError) {
          return Center(
            child: Text("Something went wrong"),
          );
        }

        if (snapshot.hasData) {
          final notificationList = snapshot.data!.data!;
          if (notificationList.isEmpty) {
            // ✅ No notifications
            return Center(
              child: Text(
                "No Notification",
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w500,
                  color: Colors.grey,
                ),
              ),
            );
          }

          // ✅ Has notifications
          return Padding(
            padding: const EdgeInsets.only(top: 5.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 9,
                  child: ListView.separated(
                    physics: ClampingScrollPhysics(),
                    shrinkWrap: true,
                    separatorBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 13.0, right: 13),
                        child: Divider(),
                      );
                    },
                    padding: EdgeInsets.zero,
                    itemCount: notificationList.length,
                    itemBuilder: (BuildContext context, int index) {
                      return _gstBottomValue(index, notificationList);
                    },
                  ),
                ),
              ],
            ),
          );
        }

        return Center(
          child: new MyUtils().kLoadingWidget(context),
        );
      },
    ),
  );
}

  postNotificationListCall(
      String acc_id, String user_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await notificationListApi(acc_id, user_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        NotificationModel currentParsedResponse =
            NotificationModel.fromJson(responseJson);

        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<NotificationModel> notificationListCall(
      String acc_id, String user_id, BuildContext context) async {
    return await postNotificationListCall(acc_id, user_id, context);
  }

  Widget _gstBottomValue(int index, List<Data> data) {
    {
      Data allData = data[index];
      Color color = Colors.redAccent;
      if (allData.notificationType == "1") {
        color = Colors.redAccent;
      } else {
        color = Colors.blueAccent;
      }
      return GestureDetector(
        /* onTap: () {
          Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => NotificationDetailsActivity(
                      msg_id: allData.msgId,
                      msg_Title: allData.subject,
                      notification_type: allData.notificationType,
                    )),
          );

          /*  Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => DocumentDetailsSecondActivity(
                      asst_year: allData.asstYear,
                    )),
          );*/
        }, */
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 13.0, right: 13),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        width: 5,
                        height: 35,
                        color: color,
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              width: 250,
                              child: Text(
                                allData.subject.toString(),
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                            /*  Text(
                              allData.availableDocuments,
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),*/
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  Future<void> _dialogCall(String imagePath, BuildContext context) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return MyDialog(
            imagePath: imagePath,
          );
        });
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    // GetX reactive update - NO setState needed!
    this._image = _image;
    /* workCategoryUploadDocRequiredListCall(Constants.ACC_ID, Constants.USER_ID,
        widget.service_request_id, document_primary_key_id, _image, context);*/
  }
}

class MyDialog extends StatefulWidget {
  String? imagePath;
  MyDialog({Key? key, required this.imagePath}) : super(key: key);
  @override
  _MyDialogState createState() => new _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {
  String? imagePath;
  Image? image;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: new SingleChildScrollView(
        child: new ListBody(
          children: <Widget>[
            Image.network(
              widget.imagePath!,
            ),
          ],
        ),
      ),
    );
  }

/*  Future getImageFromCamera() async {
    var x = await ImagePicker.pickImage(source: ImageSource.camera);
    imagePath = x.path;
    image = Image(image: FileImage(x));
  }*/
}
