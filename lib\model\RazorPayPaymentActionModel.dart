class RazorPayPaymentAction {
  int? _status;
  String? _message;
  int? _success;
  String? _errorDev;

  RazorPayPaymentAction(
      {int? status, String? message, int? success, String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  RazorPayPaymentAction.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['error_dev'] = this._errorDev;
    return data;
  }
}
