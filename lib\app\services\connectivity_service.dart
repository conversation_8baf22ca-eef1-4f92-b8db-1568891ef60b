// Connectivity Service
// Handles network connectivity monitoring

import 'dart:async';
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class ConnectivityService extends GetxService {
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;

  final isNetworkConnected = true.obs;
  final connectionType = ConnectivityResult.none.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    await _initConnectivity();
    _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
  }

  @override
  void onClose() {
    _connectivitySubscription.cancel();
    super.onClose();
  }

  // Initialize connectivity
  Future<void> _initConnectivity() async {
    try {
      final result = await _connectivity.checkConnectivity();
      _updateConnectionStatus(result);
    } catch (e) {
      print('Connectivity initialization error: $e');
    }
  }

  // Update connection status
  void _updateConnectionStatus(ConnectivityResult result) {
    connectionType.value = result;
    isNetworkConnected.value = result != ConnectivityResult.none;

    if (!isNetworkConnected.value) {
      _showNoConnectionSnackbar();
    }
  }

  // Show no connection snackbar
  void _showNoConnectionSnackbar() {
    Get.snackbar(
      'No Internet Connection',
      'Please check your internet connection and try again',
      snackPosition: SnackPosition.BOTTOM,
      duration: Duration(seconds: 3),
    );
  }

  // Check if connected
  Future<bool> isConnected() async {
    final result = await _connectivity.checkConnectivity();
    return result != ConnectivityResult.none;
  }

  // Get connection type
  Future<ConnectivityResult> getConnectionType() async {
    return await _connectivity.checkConnectivity();
  }

  // Check if WiFi connected
  bool get isWiFiConnected => connectionType.value == ConnectivityResult.wifi;

  // Check if mobile data connected
  bool get isMobileConnected => connectionType.value == ConnectivityResult.mobile;
}
