// Task Controller
// Manages task-related operations and state using GetX

import 'package:get/get.dart';
import '../services/task_service.dart';
import '../services/api_service.dart';

class TaskController extends GetxController {
  final TaskService _taskService = Get.find<TaskService>();
  final ApiService _apiService = Get.find<ApiService>();

  // Reactive variables
  final isLoading = false.obs;
  final tasks = <Map<String, dynamic>>[].obs;
  final filteredTasks = <Map<String, dynamic>>[].obs;
  final selectedTask = Rxn<Map<String, dynamic>>();
  final errorMessage = ''.obs;

  // Filter and search
  final searchQuery = ''.obs;
  final selectedFilter = 'all'.obs;
  final selectedStatus = 'all'.obs;

  // Task statistics
  final totalTasks = 0.obs;
  final pendingTasks = 0.obs;
  final inProgressTasks = 0.obs;
  final completedTasks = 0.obs;

  // Available filters
  final List<String> filterOptions = [
    'all',
    'pending',
    'in_progress',
    'completed',
    'overdue'
  ];

  @override
  void onInit() {
    super.onInit();
    loadTasks();
    
    // Listen to search query changes
    ever(searchQuery, (_) => filterTasks());
    ever(selectedFilter, (_) => filterTasks());
    ever(selectedStatus, (_) => filterTasks());
  }

  // Load all tasks
  Future<void> loadTasks() async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await _taskService.getAllTasks();
      tasks.value = result;
      
      updateTaskStatistics();
      filterTasks();
      
    } catch (e) {
      errorMessage.value = 'Failed to load tasks';
      print('Load tasks error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Filter tasks based on search and filters
  void filterTasks() {
    var filtered = tasks.toList();
    
    // Apply search filter
    if (searchQuery.value.isNotEmpty) {
      filtered = filtered.where((task) {
        final title = task['title']?.toString().toLowerCase() ?? '';
        final description = task['description']?.toString().toLowerCase() ?? '';
        final query = searchQuery.value.toLowerCase();
        return title.contains(query) || description.contains(query);
      }).toList();
    }
    
    // Apply status filter
    if (selectedFilter.value != 'all') {
      filtered = filtered.where((task) {
        return task['status'] == selectedFilter.value;
      }).toList();
    }
    
    filteredTasks.value = filtered;
  }

  // Update task statistics
  void updateTaskStatistics() {
    totalTasks.value = tasks.length;
    pendingTasks.value = tasks.where((task) => task['status'] == 'pending').length;
    inProgressTasks.value = tasks.where((task) => task['status'] == 'in_progress').length;
    completedTasks.value = tasks.where((task) => task['status'] == 'completed').length;
  }

  // Create new task
  Future<void> createTask(Map<String, dynamic> taskData) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await _taskService.createTask(taskData);
      
      if (result['success']) {
        await loadTasks(); // Reload tasks
        Get.back(); // Close create task screen
        Get.snackbar('Success', 'Task created successfully');
      } else {
        errorMessage.value = result['message'] ?? 'Failed to create task';
      }
      
    } catch (e) {
      errorMessage.value = 'Failed to create task';
      print('Create task error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Update task
  Future<void> updateTask(String taskId, Map<String, dynamic> updates) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await _taskService.updateTask(taskId, updates);
      
      if (result['success']) {
        await loadTasks(); // Reload tasks
        Get.snackbar('Success', 'Task updated successfully');
      } else {
        errorMessage.value = result['message'] ?? 'Failed to update task';
      }
      
    } catch (e) {
      errorMessage.value = 'Failed to update task';
      print('Update task error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Delete task
  Future<void> deleteTask(String taskId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';
      
      final result = await _taskService.deleteTask(taskId);
      
      if (result['success']) {
        await loadTasks(); // Reload tasks
        Get.snackbar('Success', 'Task deleted successfully');
      } else {
        errorMessage.value = result['message'] ?? 'Failed to delete task';
      }
      
    } catch (e) {
      errorMessage.value = 'Failed to delete task';
      print('Delete task error: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Mark task as completed
  Future<void> completeTask(String taskId) async {
    await updateTask(taskId, {'status': 'completed'});
  }

  // Mark task as in progress
  Future<void> startTask(String taskId) async {
    await updateTask(taskId, {'status': 'in_progress'});
  }

  // Get task by ID
  Map<String, dynamic>? getTaskById(String taskId) {
    try {
      return tasks.firstWhere((task) => task['id'] == taskId);
    } catch (e) {
      return null;
    }
  }

  // Select task
  void selectTask(Map<String, dynamic> task) {
    selectedTask.value = task;
  }

  // Clear selected task
  void clearSelectedTask() {
    selectedTask.value = null;
  }

  // Update search query
  void updateSearchQuery(String query) {
    searchQuery.value = query;
  }

  // Update filter
  void updateFilter(String filter) {
    selectedFilter.value = filter;
  }

  // Clear search and filters
  void clearFilters() {
    searchQuery.value = '';
    selectedFilter.value = 'all';
    selectedStatus.value = 'all';
  }

  // Refresh tasks
  Future<void> refreshTasks() async {
    await loadTasks();
  }

  // Get tasks by status
  List<Map<String, dynamic>> getTasksByStatus(String status) {
    return tasks.where((task) => task['status'] == status).toList();
  }

  // Get overdue tasks
  List<Map<String, dynamic>> getOverdueTasks() {
    final now = DateTime.now();
    return tasks.where((task) {
      final dueDate = DateTime.tryParse(task['due_date'] ?? '');
      return dueDate != null && dueDate.isBefore(now) && task['status'] != 'completed';
    }).toList();
  }

  // Get task statistics
  Map<String, int> getTaskStatistics() {
    return {
      'total': totalTasks.value,
      'pending': pendingTasks.value,
      'in_progress': inProgressTasks.value,
      'completed': completedTasks.value,
      'overdue': getOverdueTasks().length,
    };
  }
}
