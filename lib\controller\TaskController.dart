import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';
import 'package:get/get.dart';

String otp = '';
String mobile_no = '';
var responseJson;
//
//void verifyLogin(String mobile_Number, BuildContext context) {
//  if (mobile_Number.isEmpty) {
//    MyUtils.showToast("Please Enter Mobile Number.");
//  } else if (mobile_Number.length != 10) {
//    MyUtils.showToast("Please Enter Proper Mobile Number.");
//  } else {
////    MyUtils.showToast("Api Call");
//    callLoginApi(mobile_Number, context);
//  }
//}

Future<dynamic> callTaskApi(
  String acc_id,
  String user_id,
  String task_type,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'task_type': task_type,
  };
  return postWithContent(Constants.POST_TASK_LIST_URL, body, context);
}

Future<dynamic> callDashboardApi(
  String acc_id,
  String user_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
  };

  print("call dashboard url :${Constants.POST_DASHBOARD_URL}");
  print("acc Id :$acc_id user_Id :$user_id");

  return postWithContent(Constants.POST_DASHBOARD_URL, body, context);
}

Future<dynamic> postDocRequiredApi(
  String acc_id,
  String user_id,
  String task_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'task_id': task_id,
  };

  return postWithContent(Constants.POST_DOC_REQUIRED_URL, body, context);
}

Future<dynamic> postDocAssignApi(
  String acc_id,
  String user_id,
  String task_id,
  BuildContext context,
) async {
  Map<String, String> body = {
    'acc_id': acc_id,
    'user_id': user_id,
    'task_id': task_id,
  };
  return postWithContent(Constants.POST_DOC_ASSIGN_URL, body, context);
}

Future<dynamic> post(String url, var body, BuildContext context) async {
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http.post(Uri.parse(url), body: body, headers: {
    "Accept": "application/json",
    "Cache-Control": "false",
    "eO2-Secret-Code": Constants.ACCESS_TOKEN
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> postWithContent(
    String url, var body, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http
      .post(Uri.parse(url), body: json.encode(body), headers: {
    "Id": "erpca",
    "Pass": "e736258681ac6d7126d298cc93a732db1dad2996",
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> postWithContentToken(
    String url, var body, String token, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http
      .post(Uri.parse(url), body: json.encode(body), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> getWithContent(
    String url, String token, var body, BuildContext context) async {
  
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http.get(Uri.parse(url), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}
