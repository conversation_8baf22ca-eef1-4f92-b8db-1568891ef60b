import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/DocumentAssignActivity.dart';
import 'package:erpcacustomer/activity/DocumentRequiredActivity.dart';
import 'package:erpcacustomer/activity/OtherDocumentActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/DialogUtils.dart';
import 'package:erpcacustomer/common/MyBlinkingButton.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/CustomerTaskDetailsModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class GstFragment extends StatefulWidget {
  String? taskId = "";
  String? taskName = "";
  String? task_type = "";

  GstFragment({Key? key, this.taskId, this.task_type, this.taskName})
      : super(key: key);

  @override
  GstFragmentState createState() {
    return new GstFragmentState();
  }
}

class GstFragmentState extends State<GstFragment> {
  // GetX reactive variables
  var hasCardObs = false.obs;
  var isLoadingObs = false.obs;

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    _media = MediaQuery.of(context).size;

    children.add(_buildBackground());
    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
          primaryColor: PrimaryColor, fontFamily: 'GlacialIndifference'),
      home: Stack(
        children: children,
      ),
    );
  }

  Widget _buildBackground() {
    return Material(
      child: Stack(children: <Widget>[
        FutureBuilder<CustomerTaskDetailsModel>(
          future: getCustomerTaskDetailsCall(context),
          builder: (context, snapshot) {
            if (snapshot.hasData) {
              Color colorTask = Colors.green;

              if (double.parse(
                      snapshot.data!.data!.task![0].totalPercent.toString()) <=
                  25) {
                colorTask = Color(new CommonColor().red_light_Color);
              } else if (double.parse(
                          snapshot.data!.data!.task![0].totalPercent.toString()) >
                      25 &&
                  double.parse(
                          snapshot.data!.data!.task![0].totalPercent.toString()) <=
                      50) {
                colorTask = Color(new CommonColor().blue_lighter_Color);
              } else if (double.parse(
                  snapshot.data!.data!.task![0].totalPercent.toString()) >
                      25 &&
                  double.parse(
                      snapshot.data!.data!.task![0].totalPercent.toString()) <=
                      50) {
                colorTask = Color(new CommonColor().blue_lighter_Color);
              } else {
                colorTask = Colors.green;
              }
              return SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 8.0, top: 15, bottom: 5, right: 8),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Service",
                            style:
                                new CSSStyle().poppinsBlackBold13W400(context),
                          ),
                          Text(
                            snapshot.data!.data!.task![0].serviceType.toString(),
                            style: new CSSStyle().poppinsGreyRegular12(context),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8,
                        right: 8,
                      ),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 8.0, top: 5, bottom: 5, right: 8),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Due Date",
                            style:
                                new CSSStyle().poppinsBlackBold13W400(context),
                          ),
                          Text(
                            snapshot.data!.data!.task![0].endDate.toString(),
                            style: new CSSStyle().poppinsGreyRegular12(context),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8,
                        right: 8,
                      ),
                      child: Divider(),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 8.0, top: 5, bottom: 5, right: 8),
                      child: Row(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Tax/ Work Period",
                            style:
                                new CSSStyle().poppinsBlackBold13W400(context),
                          ),
                          Text(
                            snapshot.data!.data!.task![0].asessmentPeriod.toString(),
                            style: new CSSStyle().poppinsGreyRegular12(context),
                          ),
                        ],
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8,
                        right: 8,
                      ),
                      child: Divider(),
                    ),
                    (snapshot.data!.data!.task![0].spentHrs.toString() != "0.00")
                        ? Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 5, bottom: 5, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Hrs Logged",
                                      style: new CSSStyle()
                                          .poppinsBlackBold13W400(context),
                                    ),
                                    Text(
                                      snapshot.data!.data!.task![0].spentHrs.toString(),
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 8,
                                  right: 8,
                                ),
                                child: Divider(),
                              ),
                            ],
                          )
                        : Container(),
                    (snapshot.data!.data!.task![0].fees.toString() != "0.00")
                        ? Column(
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 5, bottom: 5, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Fees",
                                      style: new CSSStyle()
                                          .poppinsBlackBold13W400(context),
                                    ),
                                    Text(
                                      "₹ " + snapshot.data!.data!.task![0].fees.toString(),
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                  left: 8,
                                  right: 8,
                                ),
                                child: Divider(),
                              ),
                            ],
                          )
                        : Container(),
                    Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(
                              left: 8.0, top: 5, bottom: 5, right: 8),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Dependency",
                                style: new CSSStyle()
                                    .poppinsBlackBold13W400(context),
                              ),
                              (widget.task_type != "2")
                                  ? Text(
                                snapshot.data!.data!.task![0].dataDependency.toString(),
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    )
                                  : Text(
                                      "No Dependency",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                            ],
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 8,
                            right: 8,
                          ),
                          child: Divider(),
                        ),
                      ],
                    ),

                    /*  Padding(
                          padding: const EdgeInsets.only(left: 8.0, right: 8),
                          child: Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              Container(
                                  decoration: new BoxDecoration(
                                    color: Colors.deepOrange[400],
                                    shape: BoxShape.rectangle,
                                    borderRadius: BorderRadius.circular(5),
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                        left: 15.0,
                                        right: 15,
                                        top: 5,
                                        bottom: 5),
                                    child: Text(
                                      snapshot.data.data.task[0].tStatus,
                                      style: new CSSStyle()
                                          .poppinsWhiteRegular12(context),
                                    ),
                                  )),
                            ],
                          ),
                        ),*/
                    (snapshot.data!.data!.workingTeam!.length > 0)
                        ? Padding(
                            padding: const EdgeInsets.only(
                                left: 8.0, right: 8, top: 25, bottom: 10),
                            child: Container(
                                child: Text(
                              "Working Team",
                              style: new CSSStyle()
                                  .poppinsBlackBold13W400(context),
                            )),
                          )
                        : Container(),
                    (snapshot.data!.data!.workingTeam!.length > 0)
                        ? Container(
                            height: 150,
                            child: ListView.separated(
                              physics: ClampingScrollPhysics(),
                              shrinkWrap: true,
                              separatorBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(left: 5.0),
                                  child: Divider(),
                                );
                              },
                              scrollDirection: Axis.horizontal,
                              padding: EdgeInsets.zero,
                              itemCount: snapshot.data!.data!.workingTeam!.length,
                              itemBuilder: (BuildContext context, int index) {
                                return _gstBottomValue(
                                    snapshot.data!.data!.workingTeam![index]);
                              },
                            ),
                          )
                        : Container(),
                    Padding(
                      padding:
                          const EdgeInsets.only(left: 5.0, right: 5, top: 5),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 6.0, right: 6, top: 3, bottom: 15),
                            child: Row(
                              children: [
                                Text(
                                  "Progress",
                                  style: new CSSStyle()
                                      .poppinsBlackBold13W400(context),
                                ),
                                Text(
                                  " (" +
                                      double.parse(snapshot
                                              .data!.data!.task![0].totalPercent
                                              .toString())
                                          .toInt()
                                          .toString() +
                                      "%)",
                                  style: new CSSStyle()
                                      .poppinsBlackRegular12(context),
                                ),
                              ],
                            ),
                          ),
                          if (double.parse(snapshot
                                      .data!.data!.task![0].totalPercent
                                      .toString()) /
                                  100 <
                              1)
                            LinearPercentIndicator(
                              lineHeight: 5.0,
                              percent: double.parse(snapshot
                                      .data!.data!.task![0].totalPercent
                                      .toString()) /
                                  100,
                              backgroundColor: Colors.grey[200],
                              progressColor: colorTask,
                            )
                          else
                            LinearPercentIndicator(
                              lineHeight: 5.0,
                              percent: 0.0,
                              backgroundColor: Colors.grey[200],
                              progressColor: Colors.green,
                            ),
                          /*  Padding(
                      padding: const EdgeInsets.only(left: 13.0, right: 13),
                      child: Text(
                        "Service Description",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                    ),*/
                        ],
                      ),
                    ),
                    /* Padding(
                      padding: const EdgeInsets.only(top: 20.0, left: 8),
                      child: Text(
                        "Kindly shore the data for GST 3B filing needed for team. Try to share the data prior to 3 days of due date.",
                        style: new CSSStyle().poppinsBlueLight15(context),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: 5.0, left: 8),
                      child: Text(
                        "-Bipin Singh, Reviewer  21 Oct 2020 4:30 PM",
                        style: new CSSStyle().poppinsGreyRegular12(context),
                      ),
                    ),*/
                    Container(
                      child: ListView.separated(
                        physics: ClampingScrollPhysics(),
                        shrinkWrap: true,
                        separatorBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 5.0),
                            child: Divider(),
                          );
                        },
                        scrollDirection: Axis.vertical,
                        padding: EdgeInsets.zero,
                        itemCount: 3,
                        itemBuilder: (BuildContext context, int index) {
                          return _documentValue(
                              index, snapshot.data!.data!.task![0].dataDependency.toString());
                        },
                      ),
                    ),
                    /*    GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      DocumentDetailsActivity()),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 8.0, top: 5, bottom: 5),
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 8.0, right: 8, top: 35, bottom: 15),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.folder,
                                            color: Colors.amber,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8.0),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: 250,
                                                  child: Text(
                                                    "Data/Documents needed for assignment",
                                                    style: new CSSStyle()
                                                        .poppinsLightBlackRegular14(
                                                            context),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.grey,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      DocumentDetailsActivity()),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 8.0, top: 5, bottom: 5),
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 8.0, right: 8, top: 35, bottom: 15),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          Icon(
                                            Icons.folder,
                                            color: Colors.amber,
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: 8.0),
                                            child: Column(
                                              mainAxisSize: MainAxisSize.max,
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                Container(
                                                  width: 250,
                                                  child: Text(
                                                    "Documents shared by assignee",
                                                    style: new CSSStyle()
                                                        .poppinsLightBlackRegular14(
                                                            context),
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                      Icon(
                                        Icons.arrow_forward_ios,
                                        color: Colors.grey,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),*/
                    (snapshot.data!.data!.communication!.length > 0)
                        ? Padding(
                            padding: const EdgeInsets.only(
                                left: 8.0, right: 8, top: 25, bottom: 10),
                            child: Container(
                                child: Text(
                              "Recent Communication",
                              style:
                                  new CSSStyle().poppinsGreyRegular18(context),
                            )),
                          )
                        : Container(),
                    ListView.separated(
                      physics: ClampingScrollPhysics(),
                      shrinkWrap: true,
                      separatorBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 5.0),
                          child: Divider(),
                        );
                      },
                      scrollDirection: Axis.vertical,
                      padding: EdgeInsets.zero,
                      itemCount: snapshot.data!.data!.communication!.length,
                      itemBuilder: (BuildContext context, int index) {
                        return _recentCommunicationValue(
                            snapshot.data!.data!.communication![index]);
                      },
                    ),
                  ],
                ),
              );
            } else {
              return Center(
                  child: Container(
                      color: Colors.white,
                      child: new MyUtils().kLoadingWidget(context)));
            }
          },
        ),
      ]),
    );
  }

  Widget _gstBottomValue(WorkingTeam preparer) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0),
      child: GestureDetector(
        onTap: () {
          DialogUtils.showCustomDialog(
            context,
            imagePic: preparer.dpPic,
            userName: preparer.userName,
            details: "N/A",
            userType: preparer.userType,
            emailId: preparer.email,
            mobileNo: preparer.mobileNo,
            okBtnFunction: () {
              return;
            }, title: '',
          );
        },
        child: Column(
          children: [
            (preparer.dpPic == null)
                ? Image.asset('assets/images/user.png',
                    width: 80.0, height: 80.0)
                : Container(
                    height: 80,
                    width: 80,
                    decoration: new BoxDecoration(
                        shape: BoxShape.circle,
                        image: new DecorationImage(
                            fit: BoxFit.fill,
                            image: new NetworkImage(preparer.dpPic.toString()))),
                  ),
            Padding(
              padding: const EdgeInsets.only(left: 8.0, top: 10),
              child: (preparer.userName != null)
                  ? Text(
                      preparer.userName.toString(),
                      style: new CSSStyle().poppinsGreyRegular12(context),
                    )
                  : Text(
                      "N/A",
                      style: new CSSStyle().poppinsGreyRegular12(context),
                    ),
            ),
            Text(
              preparer.userType.toString(),
              style: new CSSStyle().poppinsLightGreyRegular12(context),
            ),
          ],
        ),
      ),
    );
  }

  Widget _recentCommunicationValue(Communication communication) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, top: 5, bottom: 5),
      child: Column(
        children: [
          Padding(
            padding:
                const EdgeInsets.only(left: 8.0, top: 8, bottom: 8, right: 8),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                (communication.createdOn != null)
                    ? Text(
                        communication.createdOn.toString(),
                        style: new CSSStyle().poppinsGreyRegular14(context),
                      )
                    : Text(
                        "N/A",
                        style: new CSSStyle().poppinsGreyRegular14(context),
                      ),
                (communication.title != null)
                    ? Text(
                        communication.title.toString(),
                        style: new CSSStyle().poppinsGreyRegular14(context),
                      )
                    : Text(
                        "N/A",
                        style: new CSSStyle().poppinsGreyRegular14(context),
                      ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(left: 8.0, right: 8),
            child: Divider(
              color: Colors.grey[300],
            ),
          ),
        ],
      ),
    );
  }

  Widget? _documentValue(int index, String dataDependency) {
    if (index == 0) {
      return Padding(
        padding: const EdgeInsets.only(top: 20, bottom: 5),
        child: Column(
          children: [
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => DocumentRequiredActivity(
                            taskId: widget.taskId,
                            taskname: widget.taskName,
                            task_type: widget.task_type,
                          )),
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 8.0, right: 8, top: 10, bottom: 5),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.folder,
                          color: Colors.amber,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: 280,
                                child: Text(
                                  "Data/Documents needed for assignment",
                                  style: new CSSStyle()
                                      .poppinsLightBlackRegular14(context),
                                ),
                              )
                            ],
                          ),
                        ),
                        (dataDependency.toLowerCase() != "none".toLowerCase() &&
                                (widget.task_type != "2"))
                            ? Container(
                                width: 10,
                                height: 10,
                                child: Material(
                                  child: Center(
                                    child: MyBlinkingButton(),
                                  ),
                                ),
                              )
                            : Container(),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    } else if (index == 1) {
      return Padding(
        padding: const EdgeInsets.only(top: 5, bottom: 5),
        child: Column(
          children: [
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => DocumentAssignActivity(
                            taskId: widget.taskId,
                            taskname: widget.taskName,
                          )),
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 8.0, right: 8, top: 5, bottom: 10),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.folder,
                          color: Colors.amber,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: 280,
                                child: Text(
                                  "Documents shared by assignee",
                                  style: new CSSStyle()
                                      .poppinsLightBlackRegular14(context),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    } else if (index == 2)  {
   return   Container();/*Padding(
        padding: const EdgeInsets.only(top: 5, bottom: 5),
        child: Column(
          children: [
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => OtherDocumentActivity(
                            taskId: widget.taskId,
                            taskname: widget.taskName,
                          )),
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 8.0, right: 8, top: 5, bottom: 10),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.folder,
                          color: Colors.amber,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                width: 280,
                                child: Text(
                                  "Other Document(s)",
                                  style: new CSSStyle()
                                      .poppinsLightBlackRegular14(context),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );*/
    }
  }

  postGetCustomerTaskDetailsCall(
      BuildContext context) async {
    String userToken = "";
    bool isFirstTime = true;
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await getCustomerTaskDetailsApi(
          Constants.ACC_ID, Constants.USER_ID, widget.taskId!, context);
      int flag = responseJson["success"];

      //  String getJson=  getJsonFromJWT(userToken);
      //  Map<String, dynamic> tokenData =  parseJwt(userToken);


      if (flag == 1) {
        CustomerTaskDetailsModel parsedResponse =
            CustomerTaskDetailsModel.fromJson(responseJson);

        return parsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<CustomerTaskDetailsModel> getCustomerTaskDetailsCall(
      BuildContext context) async {
    return await postGetCustomerTaskDetailsCall(context);
  }
}
