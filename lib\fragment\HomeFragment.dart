import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;
import 'dart:isolate';
import 'dart:math';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/CoreDocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/NewServicesActivity.dart';
import 'package:erpcacustomer/activity/ProjectDocumentActivity.dart';
import 'package:erpcacustomer/activity/ServiceRequestActivity.dart';
import 'package:erpcacustomer/activity/TaskActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/controller/TaskController.dart';
import 'package:erpcacustomer/model/DashboardModel.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:get/get.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:razorpay_flutter/razorpay_flutter.dart';
import 'package:table_calendar/table_calendar.dart';

import '../activity/InvoiceActivity.dart';
import '../common/CommonColor.dart';
import '../common/PaymentAcknowlegement.dart';
import '../common/circular_percent_indicator.dart';
import '../controller/InvoiceController.dart';
import '../model/CheckPaymentGateWayModel.dart';
import '../model/GetNetPaymentAmount.dart';
import '../model/RazorPayPaymentActionModel.dart';
import '../model/StoreOrderModel.dart';

class HomeFragment extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return HomeFragmentState();
  }
}

class HomeFragmentState extends State<HomeFragment>
    with TickerProviderStateMixin {
  // GetX reactive variables
  var displayNameObs = ''.obs;
  var isSavingObs = false.obs;
  var selectedEventsObs = <dynamic>[].obs;
  var actualDropdownObs = 'Last 7 days'.obs;
  var errorObs = ''.obs;
  var netAmountObs = 0.obs;
  var showLoaderObs = false.obs;
  var permissionStatusObs = Rxn<PermissionStatus>();

  // Original variables
  int primaryColor = new CommonColor().erpca_blue_color;
  bool _saving = false;
  PermissionStatus? _permissionStatus;
  ReceivePort _port = ReceivePort();

  String displayName = "";
  AnimationController? _animationController;
  final _selectedDay = DateTime.now();
  Map<DateTime, List>? _events;
  List _selectedEvents = [];
  TextEditingController controller1 = new TextEditingController();
  final Map<DateTime, List> _holidays = {
    DateTime(2020, 11, 1): ['New Year\'s Day'],
    DateTime(2020, 9, 6): ['Epiphany'],
    DateTime(2020, 11, 14): ['Valentine\'s Day'],
    DateTime(2020, 10, 21): ['Easter Sunday'],
    DateTime(2020, 10, 22): ['Easter Monday'],
    DateTime(2020, 12, 20): ['Easter Monday'],
    DateTime(2020, 12, 22): ['Easter Monday'],
  };
  String ZendeskAccountKey = 'TrSMqJzDTc1wN04e7t50kBpQRRH4swzd';
  static final List<String> chartDropdownItems = [
    'Last 7 days',
    'Last month',
    'Last year'
  ];
  String actualDropdown = chartDropdownItems[0];
  String error = "";
  Razorpay? _razorpay;
  int netAmount = 0;
  String netAmountFromApi = '';
  String razorPayOId = '';
  bool showLoader = false;
  @override
  void initState() {
    // TODO: implement initState
    Future<String> selectedResidentId =
    new PreferenceManagerUtil().getProposerDisplayName();
    selectedResidentId.then((val) {
      displayName = val;
    });
    checkallpermission_opencamera();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    _animationController!.forward();
    _events = {
      _selectedDay.subtract(Duration(days: 30)): [
        'Event A0',
        'Event B0',
        'Event C0'
      ],
      _selectedDay.subtract(Duration(days: 27)): ['Event A1'],
      _selectedDay.subtract(Duration(days: 20)): [
        'Event A2',
        'Event B2',
        'Event C2',
        'Event D2'
      ],
      _selectedDay.subtract(Duration(days: 16)): ['Event A3', 'Event B3'],
      _selectedDay.subtract(Duration(days: 10)): [
        'Event A4',
        'Event B4',
        'Event C4'
      ],
      _selectedDay.subtract(Duration(days: 4)): [
        'Event A5',
        'Event B5',
        'Event C5'
      ],
      _selectedDay.subtract(Duration(days: 2)): ['Event A6', 'Event B6'],
      _selectedDay: ['Event A7', 'Event B7', 'Event C7', 'Event D7'],
      _selectedDay.add(Duration(days: 1)): [
        'Event A8',
        'Event B8',
        'Event C8',
        'Event D8'
      ],
      _selectedDay.add(Duration(days: 3)):
      Set.from(['Event A9', 'Event A9', 'Event B9']).toList(),
      _selectedDay.add(Duration(days: 7)): [
        'Event A10',
        'Event B10',
        'Event C10'
      ],
      _selectedDay.add(Duration(days: 11)): ['Event A11', 'Event B11'],
      _selectedDay.add(Duration(days: 17)): [
        'Event A12',
        'Event B12',
        'Event C12',
        'Event D12'
      ],
      _selectedDay.add(Duration(days: 22)): ['Event A13', 'Event B13'],
      _selectedDay.add(Duration(days: 26)): [
        'Event A14',
        'Event B14',
        'Event C14'
      ],
    };
    _selectedEvents = _events![_selectedDay] ?? [];
    //initZendesk();
    IsolateNameServer.registerPortWithName(
        _port.sendPort, 'downloader_send_port');
    _port.listen((dynamic data) {
      String id = data[0];
      DownloadTaskStatus status = data[1];
      int progress = data[2];
      //setState(() {});
    });
    _razorpay = Razorpay();
    _razorpay!.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay!.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay!.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
    FlutterDownloader.registerCallback(downloadCallback);
    super.initState();
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    // Do something when payment succeeds
    razorPayActionApi(response.paymentId, 'success',
        'Online payment using Razorpay', context);
    // GetX reactive update - NO setState needed!
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => PaymentAcknowledgement(true,
                response.paymentId.toString(), netAmountFromApi.toString())));
  }

  _handlePaymentError(PaymentFailureResponse response) {
    // Do something when payment fails
    var res = json.decode(response.message.toString());
    razorPayActionApi(
        'paymentId', 'error', '${res["error"]["description"]}', context);
    // GetX reactive update - NO setState needed!
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (context) => PaymentAcknowledgement(false, '', '')));
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    // Do something when an external wallet is selected
  }

  postGetNetAmount(
      String acc_id, String billingId, context) async {
    var connectivityResult =
    await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await getAmount(acc_id, billingId, context);
      int flag = responseJson["success"];
      List data = responseJson["data"];
      var netAmnt = data[0]['net_amount'];
      if (flag == 1) {
        GetNetPaymentAmountModel currentParsedResponse =
        GetNetPaymentAmountModel.fromJson(responseJson);
        // GetX reactive update - NO setState needed!
        netAmount = netAmnt;
        netAmountObs.value = netAmnt;  // GetX automatically updates UI

        return currentParsedResponse;
      } else {}
    }
  }

  Future<GetNetPaymentAmountModel> getNetAmount(
      String acc_id, String billingId, context) async {
    return await postGetNetAmount(acc_id, billingId, context);
  }

  generateOrderId(rId, rSecretKey) async {
    String d = '$rId:$rSecretKey';
    String encodedString = base64.encode(d.codeUnits);
    var headers = {
      'Content-Type': 'application/json',
      'Authorization': 'Basic $encodedString'
    };
    var connectivityResult =
    await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      // var responseJson = await ordersApi(headers,1 * 100,'INR',context);
      var responseJson =
      await ordersApi(headers, netAmount * 100, 'INR', context);
      var razorPayOrderId = responseJson['id'];
      // GetX reactive update - NO setState needed!
      razorPayOId = razorPayOrderId;
    }
  }

  storeId(orderId, billingId) async {
    var headers = {
      'id': 'erpca',
      'pass': 'e736258681ac6d7126d298cc93a732db1dad2996',
      'Content-Type': 'application/json',
    };
    var connectivityResult =
    await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await storeAction(headers, Constants.ACC_ID,
          billingId.toString(), Constants.USER_ID, orderId, context);
      StoreOrderModel currentParsedResponse =
      StoreOrderModel.fromJson(responseJson);
      return currentParsedResponse;
    }
  }

  void openCheckout(String name, String key, String orderId, int amount) async {
    var options = {
      'key': key,
      'amount': amount * 100,
      'order_id': orderId,
      'name': name,
      'description': 'Payment',
      'external': {
        'wallets': ['paytm']
      },

      //'theme': {'backdrop_color':'00008B'},
    };

    try {
      _razorpay!.open(options);
    } catch (e) {
    }
  }

  razorPayActionApi(paymentId, status, remark, context) async {
    var headers = {
      'id': 'erpca',
      'pass': 'e736258681ac6d7126d298cc93a732db1dad2996',
      'Content-Type': 'application/json',
    };
    var connectivityResult =
    await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await razorPayAction(headers, Constants.ACC_ID,
          paymentId, razorPayOId, status, remark, context);
      RazorPayPaymentAction currentParsedResponse =
      RazorPayPaymentAction.fromJson(responseJson);
      return currentParsedResponse;
    }
  }

  void dispose() {
    // TODO: implement dispose
    super.dispose();
    _razorpay!.clear();
  }

// Zendesk is asynchronous, so we initialize in an async method.
/*
  Future<void> initZendesk() async {
    zendesk.init(ZendeskAccountKey).then((r) {

    }).catchError((e) {

    });

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    // But we aren't calling setState, so the above point is rather moot now.
  }
*/

  static void downloadCallback(
      String id, int status, int progress) {
    final SendPort? send =
    IsolateNameServer.lookupPortByName('downloader_send_port');
    send!.send([id, status, progress]);
  }

  void _onDaySelected(DateTime day, List events, List holidays) {
    // GetX reactive update - NO setState needed!
    _selectedEvents = events;
    selectedEventsObs.value = events;  // GetX automatically updates UI
  }

  void _onVisibleDaysChanged(
      DateTime first, DateTime last, CalendarFormat format) {
  }

  void _onCalendarCreated(
      DateTime first, DateTime last, CalendarFormat format) {
  }

  // Helper method to get dashboard data with proper USER_ID handling
  Future<DashboardModel> _getDashboardData(BuildContext context) async {
    // Wait a bit for USER_ID to be loaded if it's empty
    if (Constants.USER_ID.isEmpty) {
      await Future.delayed(Duration(milliseconds: 500));
    }

    // Use a default USER_ID if still empty
    String userId = Constants.USER_ID.isEmpty ? "0" : Constants.USER_ID;
    return await callDashboardCall(Constants.ACC_ID, userId, context);
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    final _media = MediaQuery.of(context).size;
    return Stack(
      children: [
        FutureBuilder<DashboardModel>(
          future: _getDashboardData(context),
            builder: (context, snapshot) {
              return snapshot.hasData
                ? Scaffold(
              floatingActionButton: new FloatingActionButton(
                heroTag: "plus",
                backgroundColor:
                Color(new CommonColor().erpca_blue_color),
                onPressed: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => NewServicesActivity()),
                  );
                  //checkpermission_opencamera();
                }, // Switch tabs
                child: new Icon(Icons.add,color: Colors.white,),
              ),
              body: _gstBottomValue(snapshot.data!),
            )
                : Center(
                child: Container(
                    color: Colors.white,
                    child: new MyUtils().kLoadingWidget(context)));
          },
        ),
        // ✅ OPTIMIZED: Only wrap specific reactive widget with Obx
        Obx(() => Visibility(
          visible: showLoaderObs.value,
          child: Center(
              child: Container(
                  color: Colors.white,
                  child: new MyUtils().kLoadingWidget(context))),
        )),
      ],
    );
  }

  postCallDashboardCall(
      String acc_id, String user_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */

    var connectivityResult =
    await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await callDashboardApi(acc_id, user_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        DashboardModel currentParsedResponse =
        DashboardModel.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        error = responseJson['message'].toString();
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<DashboardModel> callDashboardCall(
      String acc_id, String user_id, BuildContext context) async {
    return await postCallDashboardCall(acc_id, user_id, context);
  }

   postCheckPaymentButtonVisibility(
      String acc_id) async {
    var connectivityResult =
    await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await checkPaymentApi(acc_id, context);
      int flag = responseJson["success"];
      /* List data = responseJson["data"];
      var razorPayData = data[0]['api_key'];
      String gatewayType = responseJson['payment_gateway_type'];

     ;*/

      if (flag == 1) {
        CheckPaymentGatewayModel currentParsedResponse =
        CheckPaymentGatewayModel.fromJson(responseJson);
        /*  setState(() {
          buttonVisibility = true;
          razorPayApiKey = razorPayData;
          paymentGateWayType = gatewayType;
        });*/
        return currentParsedResponse;
      } else {}
    }
  }

  Future<CheckPaymentGatewayModel> checkPaymentButtonVisibility(
      String acc_id) async {
    return await postCheckPaymentButtonVisibility(acc_id);
  }



  double getMalePercentage(int maleValue, int femaleValue) {
    int totalValue = maleValue + femaleValue;

    double malePercecntage = maleValue / totalValue * 100;
    return malePercecntage;
  }

  final List<List<double>> charts = [
    [
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4
    ],
    [
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
    ],
    [
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4
    ]
  ];
  int actualChart = 0;

  double getFeMalePercentage(int maleValue, int femaleValue) {
    int totalValue = maleValue + femaleValue;

    double feMalePercecntage = femaleValue / totalValue * 100;
    return feMalePercecntage;
  }

  Widget _buildTile(Widget child, {Function()? onTap}) {
    return Material(
        elevation: 1.0,
        borderRadius: BorderRadius.circular(5.0),
        shadowColor: Color(0x802196F3),
        child: child);
  }

  void _askCameraPermission() async {
    if (await Permission.manageExternalStorage.request().isGranted) {
      // ✅ OPTIMIZED: GetX reactive state management
      _permissionStatus = await Permission.manageExternalStorage.status;
      permissionStatusObs.value = _permissionStatus;
      // setState(() {}); // Removed
    }
  }

  checkallpermission_opencamera() async {
    Map<Permission, PermissionStatus> statuses = await [
      Permission.storage,
      Permission.storage,
      Permission.manageExternalStorage,
    ].request();

    if (statuses[Permission.storage]!.isGranted) {
      if (statuses[Permission.manageExternalStorage]!.isGranted) {
      } else {
        _askCameraPermission();

        /*showToast(
            "Camera needs to access your microphone, please provide permission",
            position: ToastPosition.bottom);*/
      }
    } else {
      _askCameraPermission();

      /* showToast("Provide Camera permission to use camera.",
          position: ToastPosition.bottom);*/
    }
  }

  openCamera() {}

  Widget _gstBottomValue(DashboardModel data) {
    final width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;
    if ((data.data!.length > 0)) {
      double activeProjects = double.parse(data.data![0].activeProjects.toString());
      double completeProjects = double.parse(data.data![0].completedProjects.toString());
      double totalProjects = activeProjects + completeProjects;
      double activePercentage = activeProjects / totalProjects;
      return Padding(
        padding: const EdgeInsets.all(10.0),
        child: FutureBuilder<CheckPaymentGatewayModel>(
          future: checkPaymentButtonVisibility(Constants.ACC_ID),
          builder: (context, snap) {
            return
              ListView(
                children: [
                  Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Padding(
                          padding:
                          const EdgeInsets.only(top: 25.0, bottom: 15),
                          child: InkWell(
                            onTap: () {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => TaskActivity(
                                        task_type: "1",
                                        title: "Active Project(s)",
                                        count:
                                        data.data![0].activeProjects.toString(),
                                      )));
                            },
                            child: CircularPercentIndicator(
                              radius: 90.0,
                              lineWidth: 8.0,

                              percent: activePercentage,
                              center: Padding(
                                padding: const EdgeInsets.only(top: 50.0),
                                child: Center(
                                  child: Column(
                                    children: [
                                      Text('Active Project(s)',
                                          style: TextStyle(
                                              color: Colors.black,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w700)),
                                      if (data.data![0].activeProjects != null)
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: 5.0),
                                          child: Text(
                                              data.data![0].activeProjects.toString(),
                                              style: TextStyle(
                                                  fontSize: 30,
                                                  color: Colors.black,
                                                  height: 44.5 / 30,
                                                  fontWeight:
                                                  FontWeight.w500)),
                                        )
                                      else
                                        Text("N/A",
                                            style: new CSSStyle()
                                                .poppinsBlackBold30(
                                                context)),
                                      Padding(
                                        padding:
                                        const EdgeInsets.only(top: 8.0),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                          MainAxisAlignment.center,
                                          children: [
                                            Text('View More',
                                                style: new CSSStyle()
                                                    .poppinsBlueLight12(
                                                    context)),
                                            Icon(
                                              Icons.arrow_forward_ios,
                                              size: 10,
                                            )
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                              linearGradient: LinearGradient(colors: [
                                Color(0xFF22E966),
                                Color(0xFF43B8FF),
                              ]),
                              backgroundColor: Color(0xFFEEEEEE),

                              // progressColor: Color(new CommonColor().white_Color),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Row(
                    children: [
                      new Flexible(
                        fit: FlexFit.tight,
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => TaskActivity(
                                    task_type: "2",
                                    title: "Completed Task(s)",
                                    count:
                                    data.data![0].completedProjects.toString(),
                                  )),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Container(
                              child: Material(
                                elevation: 1.0,
                                borderRadius: BorderRadius.circular(3.0),
                                shadowColor: Color(0x802196F3),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Column(
                                      mainAxisAlignment:
                                      MainAxisAlignment.center,
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          children: [
                                            Image.asset(
                                              'assets/images/dashboard/complete.png',
                                              height: 60,
                                              width: 60,
                                            ),
                                            (data.data![0]
                                                .completedProjects !=
                                                null)
                                                ? Padding(
                                              padding:
                                              const EdgeInsets
                                                  .only(
                                                  left: 8.0),
                                              child: Text(
                                                  data.data![0]
                                                      .completedProjects.toString(),
                                                  style: TextStyle(
                                                      fontSize: 30,
                                                      color: Colors
                                                          .black,
                                                      height:
                                                      44.5 / 30,
                                                      fontWeight:
                                                      FontWeight
                                                          .w500)),
                                            )
                                                : Text("N/A",
                                                style: new CSSStyle()
                                                    .poppinsBlackBold30(
                                                    context)),
                                          ],
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: 15.0, left: 10),
                                          child: Text(
                                            'Project(s) Completed',
                                            style: new CSSStyle()
                                                .poppinsBlueRegular13(
                                                context),
                                          ),
                                        )
                                      ]),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      new Flexible(
                        fit: FlexFit.tight,
                        child: GestureDetector(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      ServiceRequestActivity()),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Container(
                              child: Material(
                                elevation: 1.0,
                                borderRadius: BorderRadius.circular(3.0),
                                shadowColor: Color(0x802196F3),
                                child: Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: Column(
                                      mainAxisAlignment:
                                      MainAxisAlignment.center,
                                      crossAxisAlignment:
                                      CrossAxisAlignment.start,
                                      children: <Widget>[
                                        Row(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          children: [
                                            Image.asset(
                                              'assets/images/dashboard/service_request.png',
                                              height: 60,
                                              width: 60,
                                            ),
                                            (data.data![0]
                                                .totalServiceRequests !=
                                                null)
                                                ? Padding(
                                              padding:
                                              const EdgeInsets
                                                  .only(
                                                  left: 8.0),
                                              child: Text(
                                                  data.data![0]
                                                      .totalServiceRequests.toString(),
                                                  style: TextStyle(
                                                      fontSize: 30,
                                                      color: Colors
                                                          .black,
                                                      height:
                                                      44.5 / 30,
                                                      fontWeight:
                                                      FontWeight
                                                          .w500)),
                                            )
                                                : Text("N/A",
                                                style: new CSSStyle()
                                                    .poppinsBlackBold30(
                                                    context)),
                                          ],
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: 15.0, left: 9),
                                          child: Text(
                                            'Service Request(s)',
                                            style: new CSSStyle()
                                                .poppinsBlueRegular13(
                                                context),
                                          ),
                                        )
                                      ]),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Row(
                        children: [
                          new Flexible(
                            fit: FlexFit.tight,
                            child: GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          ProjectDocumentActivity()),
                                );
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Container(
                                  child: Material(
                                    elevation: 1.0,
                                    borderRadius:
                                    BorderRadius.circular(3.0),
                                    shadowColor: Color(0x802196F3),
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: Column(
                                          mainAxisAlignment:
                                          MainAxisAlignment.center,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Row(
                                              mainAxisSize:
                                              MainAxisSize.max,
                                              crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                              MainAxisAlignment.start,
                                              children: [
                                                Image.asset(
                                                  'assets/images/dashboard/project_document.png',
                                                  height: 60,
                                                  width: 60,
                                                ),
                                                (data.data![0]
                                                    .workDocuments !=
                                                    null)
                                                    ? Padding(
                                                  padding:
                                                  const EdgeInsets
                                                      .only(
                                                      left: 8.0),
                                                  child: Text(
                                                      data.data![0]
                                                          .workDocuments.toString(),
                                                      style: TextStyle(
                                                          fontSize:
                                                          30,
                                                          color: Colors
                                                              .black,
                                                          height:
                                                          44.5 /
                                                              30,
                                                          fontWeight:
                                                          FontWeight
                                                              .w500)),
                                                )
                                                    : Text("N/A",
                                                    style: new CSSStyle()
                                                        .poppinsBlackBold30(
                                                        context)),
                                              ],
                                            ),
                                            Padding(
                                              padding:
                                              const EdgeInsets.only(
                                                  top: 15.0, left: 9),
                                              child: Text(
                                                'Project Document(s)',
                                                style: new CSSStyle()
                                                    .poppinsBlueRegular13(
                                                    context),
                                              ),
                                            )
                                          ]),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                          new Flexible(
                            fit: FlexFit.tight,
                            child: GestureDetector(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          CoreDocumentDetailsActivity()),
                                );
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Container(
                                  child: Material(
                                    elevation: 1.0,
                                    borderRadius:
                                    BorderRadius.circular(3.0),
                                    shadowColor: Color(0x802196F3),
                                    child: Padding(
                                      padding: const EdgeInsets.all(10.0),
                                      child: Column(
                                          mainAxisAlignment:
                                          MainAxisAlignment.center,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          children: <Widget>[
                                            Row(
                                              mainAxisSize:
                                              MainAxisSize.max,
                                              crossAxisAlignment:
                                              CrossAxisAlignment.center,
                                              mainAxisAlignment:
                                              MainAxisAlignment.start,
                                              children: [
                                                Image.asset(
                                                  'assets/images/dashboard/core_document.png',
                                                  height: 60,
                                                  width: 60,
                                                ),
                                                (data.data![0]
                                                    .coreDocuments !=
                                                    null)
                                                    ? Padding(
                                                  padding:
                                                  const EdgeInsets
                                                      .only(
                                                      left: 8.0),
                                                  child: Text(
                                                      data.data![0]
                                                          .coreDocuments.toString(),
                                                      style: TextStyle(
                                                          fontSize:
                                                          30,
                                                          color: Colors
                                                              .black,
                                                          height:
                                                          44.5 /
                                                              30,
                                                          fontWeight:
                                                          FontWeight
                                                              .w500)),
                                                )
                                                    : Text("N/A",
                                                    style: new CSSStyle()
                                                        .poppinsBlackBold30(
                                                        context)),
                                              ],
                                            ),
                                            Padding(
                                              padding:
                                              const EdgeInsets.only(
                                                  top: 15.0, left: 9),
                                              child: Text(
                                                'Core Document(s)',
                                                style: new CSSStyle()
                                                    .poppinsBlueRegular13(
                                                    context),
                                              ),
                                            )
                                          ]),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (data.billDueData!.length > 0)
                        Align(
                            alignment: Alignment.centerLeft,
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  left: 8.0, top: 10, bottom: 10),
                              child: Text(
                                "Bill(s) Due",
                                style: new CSSStyle()
                                    .poppinsBlackRegular15(context),
                              ),
                            )),
                      for (int i = 0; i < data.billDueData!.length; i++)
                        Card(
                            elevation: 1,
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  left: 8.0, right: 8, top: 18, bottom: 18),
                              child: Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, right: 8),
                                child: Row(
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceEvenly,
                                  children: [
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        mainAxisSize: MainAxisSize.max,
                                        crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                        mainAxisAlignment:
                                        MainAxisAlignment.start,
                                        children: [
                                          Text(
                                            data.billDueData![i].billNo.toString(),
                                            style: new CSSStyle()
                                                .poppinsBlackRegular12(
                                                context),
                                          ),
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                top: 4.0),
                                            child: Text(
                                              "Invoice no.",
                                              style: new CSSStyle()
                                                  .poppinsGreyRegular10(
                                                  context),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: Container(
                                        margin: EdgeInsets.only(
                                            left: io.Platform.isAndroid
                                                ? 8
                                                : 3),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          children: [
                                            Text(
                                              "₹ " +
                                                  data.billDueData![i]
                                                      .netAmount.toString(),
                                              style: new CSSStyle()
                                                  .poppinsBlackRegular12(
                                                  context),
                                            ),
                                            Padding(
                                              padding:
                                              const EdgeInsets.only(
                                                  top: 4.0),
                                              child: Text(
                                                "Amount",
                                                style: new CSSStyle()
                                                    .poppinsGreyRegular10(
                                                    context),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 2,
                                      child: Container(
                                        margin: EdgeInsets.only(
                                            right: io.Platform.isAndroid
                                                ? 5
                                                : 0),
                                        child: Column(
                                          mainAxisSize: MainAxisSize.max,
                                          crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                          mainAxisAlignment:
                                          MainAxisAlignment.start,
                                          children: [
                                            Text(
                                              data.billDueData![i].billDate.toString(),
                                              style: new CSSStyle()
                                                  .poppinsBlackRegular12(
                                                  context),
                                            ),
                                            Padding(
                                              padding:
                                              const EdgeInsets.only(
                                                  top: 4.0),
                                              child: Text(
                                                "Bill Date",
                                                style: new CSSStyle()
                                                    .poppinsGreyRegular10(
                                                    context),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: InkWell(
                                        onTap: () async {
                                          MyUtils().getDownloadUrl1(
                                              data.billDueData![i].billLink.toString());
                                        },
                                        child: Container(
                                          margin:
                                          EdgeInsets.only(right: 20),
                                          child: Icon(
                                            Icons.download_rounded,
                                            color: Color(0xFF12BBE0),
                                          ),
                                        ),
                                      ),
                                    ),
                                    if(snap.hasData) if (snap.data!.success == 1 &&
                                        snap.data!.paymentGatewayType.toString() ==
                                            'razorpay')
                                      Card(
                                        elevation: 10,
                                        shadowColor: Colors.grey,
                                        child: InkWell(
                                          onTap: () async {
                                            // GetX reactive update - NO setState needed!
                                            showLoader = true;
                                            showLoaderObs.value = true;  // GetX automatically updates UI
                                            if (showLoader)
                                              showModalBottomSheet(
                                                  isDismissible: false,
                                                  enableDrag: false,
                                                  shape:
                                                  const RoundedRectangleBorder(
                                                    // <-- SEE HERE
                                                    borderRadius:
                                                    BorderRadius
                                                        .vertical(
                                                      top: Radius.circular(
                                                          20),
                                                    ),
                                                  ),
                                                  context: context,
                                                  builder: (context) {
                                                    return Container(
                                                      height: 0.3 * height,
                                                      child: Column(
                                                        mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                        children: [
                                                          Container(
                                                            // height: 0.7 * width,
                                                            // width: 0.6 * width,
                                                            // /color: Colors.red,
                                                              child: Container(

                                                                  child: new MyUtils()
                                                                      .kLoadingWidgetLarge(
                                                                      context))),
                                                          SizedBox(
                                                            height:
                                                            0.1 * width,
                                                          ),
                                                          Container(
                                                            child: Text(
                                                              'Processing ...',
                                                              style:
                                                              TextStyle(
                                                                fontSize:
                                                                0.06 *
                                                                    width,
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    );
                                                  });
                                            await getNetAmount(
                                                Constants.ACC_ID,
                                                data.billDueData![i].billId.toString(),
                                                context);
                                            await generateOrderId(
                                                snap.data!.data![0].apiKey,
                                                snap.data!.data![0]
                                                    .secretKey);
                                            await storeId(razorPayOId,
                                                data.billDueData![0].billId);
                                            // GetX reactive update - NO setState needed!
                                            showLoader = false;
                                            showLoaderObs.value = false;  // GetX automatically updates UI
                                            netAmountFromApi = data
                                                .billDueData![i].netAmount
                                                .toString();
                                            Navigator.pop(context);
                                            openCheckout(
                                                data.billDueData![i]
                                                    .companyName.toString(),
                                                snap.data!.data![0]
                                                    .apiKey.toString(),
                                                razorPayOId,
                                                netAmount);
                                            // GetX reactive update - NO setState needed!
                                            /*
                                    // Navigator.push(context, MaterialPageRoute(builder: (context)=>PaymentAcknowledgement(false)));



                                    setState(() {
                                      showLoader = false;
                                    });

                                    // Navigator.push(context, MaterialPageRoute(builder: (context)=>PaymentAcknowledgement(true,'response.paymentId.toString()',netAmount.toString(),widget.type)));
                                    */
                                          },
                                          child: Container(
                                              alignment: Alignment.center,
                                              // margin: EdgeInsets.only(right: 8),
                                              padding: EdgeInsets.all(
                                                  0.015 * width),
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                BorderRadius.circular(
                                                    0.012 * width),
                                                color: Colors.blue,
                                              ),
                                              child: Text(
                                                'Pay Now',
                                                textAlign: TextAlign.center,
                                                style: TextStyle(
                                                  color: Colors.white,
                                                ),
                                              )),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            )),
                      if (data.billDueData!.length > 0)
                        InkWell(
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => InvoiceActivity(
                                    type: "0",
                                  )),
                            );
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Align(
                                alignment: Alignment.center,
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    "View All",
                                    style: new CSSStyle()
                                        .poppinsBlackRegular15(context),
                                  ),
                                )),
                          ),
                        ),
                    ],
                  ),
                ],
              );
          },
        ),
      );
    } else {
      return Center(child: Text(data.message.toString()));
    }
  }
}

class CustomDialogBox extends StatefulWidget {
  final String? title, descriptions, text;
  final Image? img;

  const CustomDialogBox(
      {Key ?key, this.title, this.descriptions, this.text, this.img})
      : super(key: key);

  @override
  _CustomDialogBoxState createState() => _CustomDialogBoxState();
}

class _CustomDialogBoxState extends State<CustomDialogBox> {
  bool checkedProgress = false;
  bool checkedStarted = false;
  bool checkedCompleted = false;
  bool checkedBilled = false;

  String radioItem = 'Mango';

  int id = 1;
  String radioButtonItem = 'Due This Week';

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  contentBox(context) {
    return Stack(
      children: <Widget>[
        SingleChildScrollView(
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: Color(new CommonColor().erpca_light_pink_color),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  child: Column(
                    children: [
                      Card(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                "Reset",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                            Text(
                              "Filter",
                              style: new CSSStyle().poppinsBlackBold15(context),
                            ),
                            IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  color: Colors.black,
                                ),
                                onPressed: () {
                                  Navigator.of(context, rootNavigator: true)
                                      .pop("Discard");
                                }),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Card(
                          elevation: 1,
                          child: Column(
                            children: [
                              /*CheckboxListTile(
                                title: Text(
                                  "In Progress",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedProgress,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedProgress = newValue;
                                  // Add GetX reactive variable if needed: checkedProgressObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "In Progress",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedProgress,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedProgress = v!;
                                            // Add GetX reactive variable if needed: checkedProgressObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Not Started",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedStarted,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedStarted = v!;
                                            // Add GetX reactive variable if needed: checkedStartedObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Completed",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedCompleted,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedCompleted = v!;
                                            // Add GetX reactive variable if needed: checkedCompletedObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8, bottom: 3),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Biled",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedBilled,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedBilled = v!;
                                            // Add GetX reactive variable if needed: checkedBilledObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              /*     CheckboxListTile(
                                title: Text(
                                  "Not Started",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedStarted,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedStarted = newValue;
                                  // Add GetX reactive variable if needed: checkedStartedObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              /* CheckboxListTile(
                                title: Text(
                                  "Completed",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedCompleted,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedCompleted = newValue;
                                  // Add GetX reactive variable if needed: checkedCompletedObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
/*
                              CheckboxListTile(
                                title: Text(
                                  "Biled",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedBilled,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedBilled = newValue;
                                  // Add GetX reactive variable if needed: checkedBilledObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),
*/
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            bottom: 8.0, left: 8.0, right: 8.0),
                        child: Card(
                          elevation: 1,
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due This Week',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 1,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // GetX reactive update - NO setState needed!
                                          radioButtonItem = 'Due This Week';
                                          id = 1;
                                          // Add GetX reactive variables if needed: radioButtonItemObs.value = 'Due This Week'; idObs.value = 1;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due This Month',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 2,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // GetX reactive update - NO setState needed!
                                          radioButtonItem = 'Due This Month';
                                          id = 2;
                                          // Add GetX reactive variables if needed: radioButtonItemObs.value = 'Due This Month'; idObs.value = 2;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due in current quater',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 3,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // GetX reactive update - NO setState needed!
                                          radioButtonItem =
                                          'Due in current quater';
                                          id = 3;
                                          // Add GetX reactive variables if needed: radioButtonItemObs.value = 'Due in current quater'; idObs.value = 3;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due in 6 month',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 4,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // GetX reactive update - NO setState needed!
                                          radioButtonItem = 'Due in 6 month';
                                          id = 4;
                                          // Add GetX reactive variables if needed: radioButtonItemObs.value = 'Due in 6 month'; idObs.value = 4;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due this year',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 5,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // GetX reactive update - NO setState needed!
                                          radioButtonItem = 'Due this year';
                                          id = 5;
                                          // Add GetX reactive variables if needed: radioButtonItemObs.value = 'Due this year'; idObs.value = 5;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Overdue',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 6,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // GetX reactive update - NO setState needed!
                                          radioButtonItem = 'Overdue';
                                          id = 6;
                                          // Add GetX reactive variables if needed: radioButtonItemObs.value = 'Overdue'; idObs.value = 6;
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Card(
                          elevation: 1,
                          child: Column(
                            children: [
                              /*CheckboxListTile(
                                title: Text(
                                  "In Progress",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedProgress,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedProgress = newValue;
                                  // Add GetX reactive variable if needed: checkedProgressObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "In Progress",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedProgress,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedProgress = v!;
                                            // Add GetX reactive variable if needed: checkedProgressObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Not Started",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedStarted,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedStarted = v!;
                                            // Add GetX reactive variable if needed: checkedStartedObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Completed",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedCompleted,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedCompleted = v!;
                                            // Add GetX reactive variable if needed: checkedCompletedObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8, bottom: 3),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                  MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Biled",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedBilled,
                                          onChanged: (v) {
                                            // GetX reactive update - NO setState needed!
                                            checkedBilled = v!;
                                            // Add GetX reactive variable if needed: checkedBilledObs.value = v!;
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              /*     CheckboxListTile(
                                title: Text(
                                  "Not Started",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedStarted,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedStarted = newValue;
                                  // Add GetX reactive variable if needed: checkedStartedObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              /* CheckboxListTile(
                                title: Text(
                                  "Completed",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedCompleted,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedCompleted = newValue;
                                  // Add GetX reactive variable if needed: checkedCompletedObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
/*
                              CheckboxListTile(
                                title: Text(
                                  "Biled",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedBilled,
                                onChanged: (newValue) {
                                  // GetX reactive update - NO setState needed!
                                  checkedBilled = newValue;
                                  // Add GetX reactive variable if needed: checkedBilledObs.value = newValue;
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),
*/
                            ],
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.fromLTRB(8, 8, 8, 8),
                        constraints:
                        BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
                        child: GradientButton(
                          gradient: LinearGradient(
                              colors: [
                                Color(new CommonColor().erpca_blue_color),
                                Color(new CommonColor().erpca_blue_color)
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight),
                          //color: Colors.cyan,
                          elevation: 1.0,
                          shape: new RoundedRectangleBorder(
                              borderRadius: new BorderRadius.circular(10.0)),
                          //splashColor: Colors.blueGrey,
                          //color: Theme.of(context).accentColor,
                          //textColor: Theme.of(context).primaryColorLight,
                          child: Text(
                            'APPLY',
                            style: new CSSStyle().verdanaWhiteLight14(context),
                          ),
                          callback: () {
                            Navigator.of(context, rootNavigator: true)
                                .pop("Discard");
                          },
                          increaseWidthBy: 225.0,
                          increaseHeightBy: 50.0,
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget checkboxTile(String title) {
    return Row(children: [
      Text(title),
      Checkbox(value: true, onChanged: (v) => null),
    ]);
  }
}
