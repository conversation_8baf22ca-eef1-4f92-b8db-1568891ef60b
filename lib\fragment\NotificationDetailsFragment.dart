import 'dart:io';
import 'dart:async';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:erpcacustomer/model/NotificationDetailsModel.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'dart:ui';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';

class NotificationDetailsFragment extends StatefulWidget {
  String? msg_id;
  String? notification_type;

  NotificationDetailsFragment({Key? key, this.msg_id, this.notification_type})
      : super(key: key);

  @override
  NotificationDetailsFragmentState createState() {
    return new NotificationDetailsFragmentState();
  }
}

class NotificationDetailsFragmentState
    extends State<NotificationDetailsFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var savingObs = false.obs;
  var imageObs = Rxn<File>();

  // Original variables (keeping for compatibility)
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  ImagePickerHandler? imagePicker;
  File? _image;
  AnimationController? _controller;
  String document_primary_key_id = "";
  bool _loadingPath = false;
  String _directoryPath = '';
  List<PlatformFile>? _paths;
  FileType _pickingType = FileType.any;
  bool _multiPick = false;
  String _extension = '';
  String _fileName = '';
  bool _saving = false;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
  }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Material(
        child: Stack(
          children: [
            _buildBackground(),
          ],
        ),
      ),
    );
  }

  Widget _buildBackground() {
    // ✅ OPTIMIZED: Obx wrapper for GetX reactive updates
    return Obx(() => ModalProgressHUD(
      inAsyncCall: savingObs.value,
      child: FutureBuilder<NotificationDetailsModel>(
        future: notificationDetailsCall(Constants.ACC_ID, Constants.USER_ID,
            widget.msg_id.toString(), widget.notification_type.toString(), context),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            return Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                /*  Expanded(
                    flex: 9,
                    child: WebView(
                      initialUrl: Uri.dataFromString(
                              snapshot.data.notificationMessage,
                              mimeType: 'text/html')
                          .toString(),
                    ),
                  ),*/
                ],
              ),
            );
          } else {
            return Center(
                child: Container(
                    color: Colors.white,
                    child: new MyUtils().kLoadingWidget(context)));
          }
        },
      ),
    )); // Close Obx
  }
/*

  void _onNavigationDelegateExample(
      String data, WebViewController controller, BuildContext context) async {
    final String contentBase64 =
        base64Encode(const Utf8Encoder().convert(data));
    await controller.loadUrl('data:text/html;base64,$contentBase64');
  }
*/

  postNotificationDetailsCall(
      String acc_id,
      String user_id,
      String msg_id,
      String notification_type,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await notificationDetailsApi(
          acc_id, user_id, msg_id, notification_type, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        NotificationDetailsModel currentParsedResponse =
            NotificationDetailsModel.fromJson(responseJson);

        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<NotificationDetailsModel> notificationDetailsCall(
      String acc_id,
      String user_id,
      String msg_id,
      String notification_type,
      BuildContext context) async {
    return await postNotificationDetailsCall(acc_id, user_id, msg_id, notification_type, context);
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  Future<void> _dialogCall(String imagePath, BuildContext context) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return MyDialog(
            imagePath: imagePath,
          );
        });
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    // ✅ OPTIMIZED: GetX reactive state management
    this._image = _image;
    imageObs.value = _image;
    /* workCategoryUploadDocRequiredListCall(Constants.ACC_ID, Constants.USER_ID,
        widget.service_request_id, document_primary_key_id, _image, context);*/
    // setState(() { this._image = _image; }); // Removed
  }
}

class MyDialog extends StatefulWidget {
  String? imagePath;
  MyDialog({Key? key, @required this.imagePath}) : super(key: key);
  @override
  _MyDialogState createState() => new _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {
  String? imagePath;
  Image? image;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: new SingleChildScrollView(
        child: new ListBody(
          children: <Widget>[
            Image.network(
              widget.imagePath.toString(),
            ),
          ],
        ),
      ),
    );
  }

/*  Future getImageFromCamera() async {
    var x = await ImagePicker.pickImage(source: ImageSource.camera);
    imagePath = x.path;
    image = Image(image: FileImage(x));
  }*/
}
