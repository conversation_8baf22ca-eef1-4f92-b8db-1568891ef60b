import 'dart:io';

import 'package:erpcacustomer/activity/LoginActivity.dart';
import 'package:erpcacustomer/activity/SplashActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:flutter/material.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:get/get.dart';

class AboutFragment extends StatefulWidget {
  String? userName;
  String? emailId;
  String? location;
  String? notificaiton_Enabled;

  AboutFragment(
      {Key? key,
      this.userName,
      this.emailId,
      this.location,
      this.notificaiton_Enabled})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return AboutFragmentState();
  }
}

class AboutFragmentState extends State<AboutFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var savingObs = false.obs;
  var selectedIndexObs = 0.obs;
  var isSwitchedObs = false.obs;

  // Original variables (keeping for compatibility)
  String actualName = "";
  String actualEmail = "";
  String profilePic = "";
  String departmentName = "";
  String designationName = "";
  String dateOfBirth = "";
  DateTime? dateOfBirthDate;
  String actualContact = "";
  ImagePickerHandler? imagePicker;
  AnimationController? _controller;
  bool _saving = false;
  TabController? _tabController;
  int _selectedIndex = 0;
  File? _image;
  bool isSwitched = false;

  @override
  Widget build(BuildContext context) {
    // ✅ OPTIMIZED: Obx wrapper for GetX reactive updates
    return Obx(() => ModalProgressHUD(
      inAsyncCall: savingObs.value,
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 15, top: 30, right: 15),
            child: Column(children: <Widget>[
              Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Username",
                        style: new CSSStyle().poppinsGreyRegular12(context),
                      ),
                      Text(
                        widget.userName.toString(),
                        style:
                            new CSSStyle().poppinsLightBlackRegular15(context),
                      ),
                    ],
                  ),
                  Text(
                    "Edit",
                    style: new CSSStyle().poppinsBlueLightBold15(context),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Email",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Text(
                          widget.emailId.toString(),
                          style: new CSSStyle()
                              .poppinsLightBlackRegular15(context),
                        ),
                      ],
                    ),
                    Text(
                      "Edit",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Location",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Container(
                          width: 250,
                          child: Text(
                            widget.location.toString(),
                            style: new CSSStyle()
                                .poppinsLightBlackRegular15(context),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      "Edit",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
              Obx(() => Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Receives Notification",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Text(
                          widget.notificaiton_Enabled ?? "Enabled",
                          style: new CSSStyle()
                              .poppinsLightBlackRegular15(context),
                        ),
                      ],
                    ),
                    Switch(
                      activeTrackColor:
                          Color(new CommonColor().erpca_blue_color),
                      activeColor: Color(new CommonColor().white_Color),
                      value: isSwitchedObs.value,
                      onChanged: (value) {
                        // ✅ OPTIMIZED: GetX reactive state management
                        isSwitched = value;
                        isSwitchedObs.value = value;
                        // setState(() { isSwitched = value; }); // Removed
                      },
                    )
                  ],
                ),
              )),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Text(
                          "",
                          style: new CSSStyle()
                              .poppinsLightBlackRegular15(context),
                        ),
                      ],
                    ),
                    GestureDetector(
                      onTap: () {
                        _onLogoutPressed();
                      },
                      child: Text(
                        "Logout",
                        style: new CSSStyle().poppinsBlueLightBold15(context),
                      ),
                    )
                  ],
                ),
              ),
            ]),
          ),
        ),
      ),
    )); // Close Obx
  }

  _signoutMethodCall() async {
    /* Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LoginActivity()),
    );*/

    new PreferenceManagerUtil().setMobile("");
    new PreferenceManagerUtil().setMPin("");
    new PreferenceManagerUtil().setProposerDisplayName("");
    new PreferenceManagerUtil().setListOfCompany("");
    new PreferenceManagerUtil().setBuisnessName("");
    new PreferenceManagerUtil().setProfilePic("");
    new PreferenceManagerUtil().setUserId("");
    new PreferenceManagerUtil().setProposerDisplayName("");

    Navigator.pushAndRemoveUntil<dynamic>(
      context,
      MaterialPageRoute<dynamic>(
        builder: (BuildContext context) => SplashActivity(),
      ),
      (route) => false, //if you want to disable back feature set to false
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
    _tabController = TabController(length: 3, vsync: this);
    _controller!.addListener(() {
      // ✅ OPTIMIZED: GetX reactive state management
      _selectedIndex = _tabController!.index;
      selectedIndexObs.value = _selectedIndex;
      // setState(() { _selectedIndex = _tabController!.index; }); // Removed
    });
  }

   _onLogoutPressed() {
    return showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text('Are you sure?'),
            content: new Text('Do you want to logout from App'),
            actions: <Widget>[
              new GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("NO"),
                ),
              ),
              SizedBox(height: 16),
              new GestureDetector(
                onTap: () {
                  _signoutMethodCall();

                  return Navigator.of(context).pop(true);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("YES"),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  _addForgotPasswordButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 0),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 10.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_blue_Color),
          Color(new CommonColor().oxygen_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'Reset Password',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginActivity(),
              ));
        },
        increaseWidthBy: 100.0,
        increaseHeightBy: 10.0,
      ),
    );
  }

  _addAccessMyAccountButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 20),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 40.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_dark_blue_Color),
          Color(new CommonColor().oxygen_dark_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'LOGOUT',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          // ✅ OPTIMIZED: GetX reactive state management
          _onLogoutPressed();
          // setState(() { _onLogoutPressed(); }); // Removed
        },
        increaseWidthBy: 225.0,
        increaseHeightBy: 50.0,
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // GetX reactive update - NO setState needed!
      this._image = _image;
    }
  }

  DateTime convertDateFromString(String strDate) {
    DateTime todayDate = DateTime.parse(strDate);
    ;
    return todayDate;
  }
}
