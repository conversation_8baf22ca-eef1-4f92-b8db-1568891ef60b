import 'package:erpcacustomer/activity/DocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/DocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/GstActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TaskListFragment extends StatefulWidget {
  TaskListFragment({Key? key}) : super(key: key);

  @override
  TaskListFragmentState createState() {
    return new TaskListFragmentState();
  }
}

class TaskListFragmentState extends State<TaskListFragment> {
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Stack(
        children: [
          _buildBackground(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Material(
      child: Stack(children: <Widget>[
        //Above card

        ListView.separated(
          physics: ClampingScrollPhysics(),
          shrinkWrap: true,
          separatorBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(left: 13.0, right: 13),
              child: Divider(),
            );
          },
          padding: EdgeInsets.zero,
          itemCount: 10,
          itemBuilder: (BuildContext context, int index) {
            return _gstBottomValue(index);
          },
        ),
        // Positioned to take only AppBar size
      ]),
    );
  }

  Widget _gstBottomValue(int index) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => GstActivity()),
        );
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              left: 13.0,
              right: 13,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      width: 55,
                      height: 89,
                      child: Card(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5)),
                        elevation: 3,
                        child: Column(
                          children: [
                            Row(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Column(
                                  children: [
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8.0),
                                      child: Text(
                                        "Wed",
                                        style: new CSSStyle()
                                            .poppinsBlackRegular14(context),
                                      ),
                                    ),
                                    Padding(
                                      padding: const EdgeInsets.all(8.0),
                                      child: Text(
                                        "08",
                                        style: new CSSStyle()
                                            .poppinsBlackRegular14(context),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            Container(
                              height: 25,
                              width: double.infinity,
                              color: Colors.green,
                              child: Center(
                                child: Text(
                                  "2020",
                                  style: new CSSStyle()
                                      .poppinsWhiteRegular12(context),
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 15.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "ITR",
                            style: new CSSStyle()
                                .poppinsLightBlackRegular16(context),
                          ),
                          Text(
                            "Complete",
                            style: new CSSStyle()
                                .poppinsLightBlackRegular16(context),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
