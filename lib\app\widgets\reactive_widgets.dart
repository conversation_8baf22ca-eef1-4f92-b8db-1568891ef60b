// Reactive Widgets
// GetX reactive UI components for the ERPC Customer App

import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Reactive Loading Button
class ReactiveLoadingButton extends StatelessWidget {
  final RxBool isLoading;
  final VoidCallback onPressed;
  final String text;
  final String? loadingText;
  final Color? backgroundColor;
  final Color? textColor;
  final double? width;
  final double? height;

  const ReactiveLoadingButton({
    Key? key,
    required this.isLoading,
    required this.onPressed,
    required this.text,
    this.loadingText,
    this.backgroundColor,
    this.textColor,
    this.width,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => SizedBox(
      width: width,
      height: height ?? 50,
      child: ElevatedButton(
        onPressed: isLoading.value ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? Theme.of(context).primaryColor,
          foregroundColor: textColor ?? Colors.white,
        ),
        child: isLoading.value
            ? Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? Colors.white,
                      ),
                    ),
                  ),
                  if (loadingText != null) ...[
                    SizedBox(width: 8),
                    Text(loadingText!),
                  ],
                ],
              )
            : Text(text),
      ),
    ));
  }
}

// Reactive Counter Widget
class ReactiveCounter extends StatelessWidget {
  final RxInt count;
  final String label;
  final Color? textColor;
  final double? fontSize;

  const ReactiveCounter({
    Key? key,
    required this.count,
    required this.label,
    this.textColor,
    this.fontSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          count.value.toString(),
          style: TextStyle(
            fontSize: fontSize ?? 24,
            fontWeight: FontWeight.bold,
            color: textColor ?? Theme.of(context).primaryColor,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: textColor ?? Colors.grey[600],
          ),
        ),
      ],
    ));
  }
}

// Reactive Error Message
class ReactiveErrorMessage extends StatelessWidget {
  final RxString errorMessage;
  final Color? backgroundColor;
  final Color? textColor;
  final IconData? icon;

  const ReactiveErrorMessage({
    Key? key,
    required this.errorMessage,
    this.backgroundColor,
    this.textColor,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => errorMessage.value.isEmpty
        ? SizedBox.shrink()
        : Container(
            padding: EdgeInsets.all(12),
            margin: EdgeInsets.symmetric(vertical: 8),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.red[50],
              border: Border.all(color: Colors.red[300]!),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  icon ?? Icons.error_outline,
                  color: textColor ?? Colors.red[700],
                  size: 20,
                ),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    errorMessage.value,
                    style: TextStyle(
                      color: textColor ?? Colors.red[700],
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ));
  }
}

// Reactive Text Field
class ReactiveTextField extends StatelessWidget {
  final RxString value;
  final String label;
  final String? hint;
  final bool obscureText;
  final TextInputType? keyboardType;
  final String? Function(String?)? validator;
  final Widget? prefixIcon;
  final Widget? suffixIcon;

  const ReactiveTextField({
    Key? key,
    required this.value,
    required this.label,
    this.hint,
    this.obscureText = false,
    this.keyboardType,
    this.validator,
    this.prefixIcon,
    this.suffixIcon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onChanged: (val) => value.value = val,
      obscureText: obscureText,
      keyboardType: keyboardType,
      validator: validator,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        border: OutlineInputBorder(),
      ),
    );
  }
}

// Reactive Search Bar
class ReactiveSearchBar extends StatelessWidget {
  final RxString searchQuery;
  final String hint;
  final VoidCallback? onClear;

  const ReactiveSearchBar({
    Key? key,
    required this.searchQuery,
    this.hint = 'Search...',
    this.onClear,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => TextField(
      onChanged: (value) => searchQuery.value = value,
      decoration: InputDecoration(
        hintText: hint,
        prefixIcon: Icon(Icons.search),
        suffixIcon: searchQuery.value.isNotEmpty
            ? IconButton(
                icon: Icon(Icons.clear),
                onPressed: () {
                  searchQuery.value = '';
                  onClear?.call();
                },
              )
            : null,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(25),
        ),
        contentPadding: EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    ));
  }
}

// Reactive Status Badge
class ReactiveStatusBadge extends StatelessWidget {
  final RxString status;
  final Map<String, Color>? statusColors;

  const ReactiveStatusBadge({
    Key? key,
    required this.status,
    this.statusColors,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colors = statusColors ?? {
      'pending': Colors.orange,
      'in_progress': Colors.blue,
      'completed': Colors.green,
      'cancelled': Colors.red,
    };

    return Obx(() => Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: colors[status.value] ?? Colors.grey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.value.toUpperCase(),
        style: TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    ));
  }
}

// Reactive List View
class ReactiveListView<T> extends StatelessWidget {
  final RxList<T> items;
  final Widget Function(T item, int index) itemBuilder;
  final Widget? emptyWidget;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ReactiveListView({
    Key? key,
    required this.items,
    required this.itemBuilder,
    this.emptyWidget,
    this.shrinkWrap = false,
    this.physics,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Obx(() => items.isEmpty
        ? emptyWidget ?? Center(child: Text('No items found'))
        : ListView.builder(
            shrinkWrap: shrinkWrap,
            physics: physics,
            itemCount: items.length,
            itemBuilder: (context, index) => itemBuilder(items[index], index),
          ));
  }
}
