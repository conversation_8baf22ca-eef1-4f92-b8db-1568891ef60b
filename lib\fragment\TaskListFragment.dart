import 'package:erpcacustomer/activity/DocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/DocumentDetailsActivity.dart';
import 'package:erpcacustomer/activity/CustomerDocumentScreen3Activity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:flutter/material.dart';
import 'package:erpcacustomer/model/CustomerDocumentScreen2Model.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:get/get.dart';

import 'package:erpcacustomer/controller/DocumentController.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/Constants.dart';

class DocumentDetailsSecondFragment extends StatefulWidget {
  String? asst_year;
  DocumentDetailsSecondFragment({Key? key, required this.asst_year})
      : super(key: key);

  @override
  DocumentDetailsSecondFragmentState createState() {
    return new DocumentDetailsSecondFragmentState();
  }
}

class DocumentDetailsSecondFragmentState
    extends State<DocumentDetailsSecondFragment> {
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Stack(
        children: [
          _buildBackground(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Material(
      child: Stack(children: <Widget>[
        //Above card

        FutureBuilder<CustomerDocumentScreen2Model>(
          future: customerDocumentScreen2Call(
              Constants.ACC_ID, Constants.USER_ID, widget.asst_year!, context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? Padding(
                    padding: const EdgeInsets.only(top: 20.0),
                    child: ListView.separated(
                      physics: ClampingScrollPhysics(),
                      shrinkWrap: true,
                      separatorBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 13.0, right: 13),
                          child: Divider(),
                        );
                      },
                      padding: EdgeInsets.zero,
                      itemCount: snapshot.data!.data!.length,
                      itemBuilder: (BuildContext context, int index) {
                        return _gstBottomValue(snapshot.data!.data![index]);
                      },
                    ),
                  )
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
        // Positioned to take only AppBar size
      ]),
    );
  }

   postCustomerDocumentScreen2Call(
      String acc_id,
      String user_id,
      String asst_year,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await customerDocumentScreen2Api(acc_id, user_id, asst_year, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        CustomerDocumentScreen2Model currentParsedResponse =
            CustomerDocumentScreen2Model.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<CustomerDocumentScreen2Model> customerDocumentScreen2Call(
      String acc_id,
      String user_id,
      String asst_year,
      BuildContext context) async {
    return await postCustomerDocumentScreen2Call(acc_id, user_id, asst_year, context);
  }

  Widget _gstBottomValue(Data data) {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => CustomerDocumentScreen3Activity(
                    asst_year: widget.asst_year,
                    workcategory_id: data.workcategoryId,
                    docName: data.workcategory,
                  )),
        );
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 13.0, right: 13),
            child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.folder,
                      color: Colors.amber,
                      size: 50,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            data.workcategory.toString(),
                            style: new CSSStyle()
                                .poppinsLightBlackRegular16(context),
                          ),
                          Text(
                            data.documentAvailable.toString() + " Document(s)",
                            style: new CSSStyle()
                                .poppinsLightGreyRegular14(context),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                /* Icon(
                  Icons.arrow_forward_ios,
                  color: Colors.grey,
                ),*/
              ],
            ),
          ),
        ],
      ),
    );
  }
}
