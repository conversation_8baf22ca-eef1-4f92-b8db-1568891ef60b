import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';

import 'package:erpcacustomer/fragment/ReceiptDetailsFragment.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ReceiptDetailsActivity extends StatefulWidget {
  String? billing_id;
  String? invoiceNo;

  ReceiptDetailsActivity(
      {Key? key, @required this.billing_id, @required this.invoiceNo})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return ReceiptDetailsActivityState();
  }
}

class ReceiptDetailsActivityState extends State<ReceiptDetailsActivity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          "Receipt Details (" + widget.invoiceNo.toString() + ")",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: ReceiptDetailsFragment(
        billing_id: widget.billing_id.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
