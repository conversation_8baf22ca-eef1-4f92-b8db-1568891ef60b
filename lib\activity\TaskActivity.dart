import 'dart:io' as io;

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/GstActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/controller/TaskController.dart';
import 'package:erpcacustomer/model/TaskListModel.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import "package:percent_indicator/linear_percent_indicator.dart";

class TaskActivity extends StatefulWidget {
  final String task_type;
  final String title;
  final String count;

  const TaskActivity(
      {Key? key,
      required this.title,
      required this.count,
      required this.task_type})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return TaskActivityState();
  }
}

class TaskActivityState extends State<TaskActivity>
    with TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var displayNameObs = ''.obs;
  var errorObs = ''.obs;
  var isLoadingObs = false.obs;
  var actualDropdownObs = 'Last 7 days'.obs;

  // Original variables (keeping for compatibility)
  int primaryColor = new CommonColor().erpca_blue_color;
  String displayName = "";
  AnimationController? _animationController;
  TextEditingController controller1 = new TextEditingController();
  static final List<String> chartDropdownItems = [
    'Last 7 days',
    'Last month',
    'Last year'
  ];
  String actualDropdown = chartDropdownItems[0];
  String error = "";
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadDisplayName();
    _initializeAnimation();
  }

  void _loadDisplayName() async {
    final selectedResidentId = await PreferenceManagerUtil().getProposerDisplayName();
    if (mounted) {
      // ✅ OPTIMIZED: GetX reactive state management
      displayNameObs.value = selectedResidentId;
      displayName = selectedResidentId; // Keep for compatibility
      // setState(() { displayName = selectedResidentId; }); // Removed
    }
  }

  void _initializeAnimation() {
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _animationController!.forward();
  }

  @override
  void dispose() {
    _animationController?.dispose();
    controller1.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          // Status bar color
          statusBarColor: Colors.white,

          // Status bar brightness (optional)
          statusBarIconBrightness: Brightness.dark, // For Android (dark icons)
          statusBarBrightness: io.Platform.isAndroid
              ? Brightness.light
              : Brightness.dark, // For iOS (dark icons)
        ),
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              // moveToLastScreen();
              Navigator.pop(context); 
            }),
        /* actions: <Widget>[
          /*   FlatButton(
            textColor: Colors.white,
            onPressed: () {},
            child: Text(
              widget.count,
              style: new CSSStyle().poppinsWhiteRegular14(context),
            ),
            shape: CircleBorder(side: BorderSide(color: Colors.transparent)),
          ),*/
        ], */
        title: Text(
          widget.title,
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /* floatingActionButton: new FloatingActionButton(
        heroTag: "plus",
        backgroundColor: Color(new CommonColor().erpca_blue_color),
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => NewServicesActivity()),
          );
        }, // Switch tabs
        child: new Icon(Icons.add,color: Colors.white,),
      ), */
      body:
          /*FutureBuilder<TaskListModel>(
        future: currentDayCall(Constants.ACC_ID, Constants.USER_ID, context),
        builder: (context, snapshot) {
          return snapshot.hasData
              ? _gstBottomValue(snapshot.data)
              : Center(
                  child: Container(
                      color: Colors.white,
                      child: new MyUtils().kLoadingWidget(context)));
        },
      )*/
          FutureBuilder<TaskListModel>(
        future: postCurrentDayCall(
            Constants.ACC_ID, Constants.USER_ID, widget.task_type, context),
        builder: (context, snapShot) {
          return snapShot.hasData
              ? (snapShot.data!.data!.length > 0)
                  ? ListView.separated(
                      physics: ClampingScrollPhysics(),
                      shrinkWrap: true,
                      separatorBuilder: (context, index) {
                        return Padding(
                          padding: const EdgeInsets.only(left: 85.0),
                        );
                      },
                      padding: EdgeInsets.zero,
                      itemCount: snapShot.data!.data!.length,
                      itemBuilder: (BuildContext context, int index) {
                        return _gstBottomValue(
                            snapShot.data!.data![index], index);
                      },
                    )
                  : Center(child: Text(snapShot.data?.message?.toString() ?? 'No data available'))
              : Center(
                  child: Container(
                      color: Colors.white,
                      child: new MyUtils().kLoadingWidget(context)));
        },
      ),
    );
  }

  void moveToLastScreen() {
    Get.back();
  }

 currentDayCall(String accId, String userId,
      String taskType, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await callTaskApi(accId, userId, taskType, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        TaskListModel currentParsedResponse =
            TaskListModel.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        error = responseJson['message'].toString();
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<TaskListModel> postCurrentDayCall(String accId, String userId,
      String taskType, BuildContext context) async {
    return await currentDayCall(accId, userId, taskType, context);
  }

  double getMalePercentage(int maleValue, int femaleValue) {
    int totalValue = maleValue + femaleValue;

    double malePercecntage = maleValue / totalValue * 100;
    return malePercecntage;
  }

  final List<List<double>> charts = [
    [
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4
    ],
    [
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
    ],
    [
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4,
      0.0,
      0.3,
      0.7,
      0.6,
      0.55,
      0.8,
      1.2,
      1.3,
      1.35,
      0.9,
      1.5,
      1.7,
      1.8,
      1.7,
      1.2,
      0.8,
      1.9,
      2.0,
      2.2,
      1.9,
      2.2,
      2.1,
      2.0,
      2.3,
      2.4,
      2.45,
      2.6,
      3.6,
      2.6,
      2.7,
      2.9,
      2.8,
      3.4
    ]
  ];
  int actualChart = 0;

  double getFeMalePercentage(int maleValue, int femaleValue) {
    int totalValue = maleValue + femaleValue;

    double feMalePercecntage = femaleValue / totalValue * 100;
    return feMalePercecntage;
  }



  Widget _gstBottomValue(Data data, int index) {
    Color color = Color(new CommonColor().orange_light_Color);
    if (data.tStatus!.toLowerCase() == "in-process") {
      color = Colors.green;
    } else if (data.tStatus!.toLowerCase() == "completed") {
      color = Colors.green;
    } else {
      color = Colors.green;
    }
    Color cardColor = Color(new CommonColor().white_Color);
    if (index % 2 == 0) {
      cardColor = Color(new CommonColor().white_Color);
    } else {
      cardColor = Color(new CommonColor().white_Color);
    }
    Color colorTask = Colors.green;

    if (double.parse(data.taskProgress.toString()) <= 25) {
      colorTask = Color(new CommonColor().red_light_Color);
    } else if (double.parse(data.taskProgress.toString()) > 25 &&
        double.parse(data.taskProgress.toString()) <= 50) {
      colorTask = Color(new CommonColor().blue_lighter_Color);
    } else if (double.parse(data.taskProgress.toString()) > 25 &&
        double.parse(data.taskProgress.toString()) <= 50) {
      colorTask = Color(new CommonColor().blue_lighter_Color);
    } else {
      colorTask = Colors.green;
    }

    return GestureDetector(
      onTap: () {
        Get.to(() => GstActivity(
          taskId: data.taskId,
          task_type: widget.task_type,
          taskName: data.taskTitle,
        ));
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Card(
          color: cardColor,
          elevation: 2,
          shadowColor: Colors.white,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding:
                    const EdgeInsets.only(left: 13.0, right: 13, top: 13.0),
                child: Container(
                  width: 300,
                  child: Text(
                    data.workcategory.toString(),
                    style: new CSSStyle().poppinsBlackRegular15(context),
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 13.0, right: 13, top: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Due Date",
                          style: new CSSStyle().poppinsBlackBold13W400(context),
                        ),
                        Row(
                          children: [
                            Text(
                              data.endDate.toString(),
                              style:
                                  new CSSStyle().poppinsGreyRegular12(context),
                            ),
                            (data.asessmentPeriod!.trim() != "")
                                ? Padding(
                                    padding: const EdgeInsets.only(
                                        left: 10.0, bottom: 1),
                                    child: Card(
                                        elevation: 2,
                                        child: Container(
                                            width: 80,
                                            decoration: new BoxDecoration(
                                              color: color,
                                              shape: BoxShape.rectangle,
                                              borderRadius:
                                                  BorderRadius.circular(3),
                                            ),
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  left: 2.0,
                                                  right: 2,
                                                  top: 2,
                                                  bottom: 2),
                                              child: Text(
                                                data.asessmentPeriod!.toString(),
                                                textAlign: TextAlign.center,
                                                style: new CSSStyle()
                                                    .poppinsWhiteRegular11(
                                                        context),
                                              ),
                                            ))),
                                  )
                                : Container(),
                          ],
                        ),
                      ],
                    ),
                    Container(
                      width: 95,
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "Dependency",
                            style:
                                new CSSStyle().poppinsBlackBold13W400(context),
                          ),
                          (widget.task_type != "2")
                              ? Padding(
                                  padding: const EdgeInsets.only(top: 5.0),
                                  child: Text(
                                    data.dataDependency.toString(),
                                    style: new CSSStyle()
                                        .poppinsGreyRegular12(context),
                                  ),
                                )
                              : Padding(
                                  padding: const EdgeInsets.only(top: 5.0),
                                  child: Text(
                                    "No Dependency",
                                    style: new CSSStyle()
                                        .poppinsGreyRegular12(context),
                                  ),
                                ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              /*  Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 15),
                child: Text(
                  data.invoiceType,
                  style: new CSSStyle().poppinsBlackRegular12(context),
                ),
              ),*/
              /* Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 5),
                child: Text(
                  data.billDescription,
                  style: new CSSStyle().poppinsBlackRegular12(context),
                ),
              ),*/

              Padding(
                padding: const EdgeInsets.only(
                  left: 8,
                  right: 8,
                ),
                child: Divider(),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 5.0, right: 5, top: 5),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 8.0, right: 8, top: 3, bottom: 6),
                      child: Row(
                        children: [
                          Text(
                            "Progress",
                            style:
                                new CSSStyle().poppinsBlackBold13W400(context),
                          ),
                          Text(
                            " (" +
                                double.parse(data.taskProgress.toString())
                                    .toInt()
                                    .toString() +
                                "%)",
                            style:
                                new CSSStyle().poppinsBlackRegular12(context),
                          ),
                        ],
                      ),
                    ),
                    if (double.parse(data.taskProgress.toString()) / 100 < 1)
                      LinearPercentIndicator(
                        lineHeight: 5.0,
                        percent:
                            double.parse(data.taskProgress.toString()) / 100,
                        backgroundColor: Colors.grey[200],
                        progressColor: colorTask,
                      )
                    else
                      LinearPercentIndicator(
                        lineHeight: 5.0,
                        percent: 0.0,
                        backgroundColor: Colors.grey[200],
                        progressColor: Colors.green,
                      ),
                    /*  Padding(
                      padding: const EdgeInsets.only(left: 13.0, right: 13),
                      child: Text(
                        "Service Description",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                    ),*/
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: 8,
                  left: 8,
                  right: 8,
                ),
                child: Divider(),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    left: 13.0, right: 13, top: 6, bottom: 13.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        (data.photoPath == "")
                            ? Center(
                                child: Container(
                                  child: Icon(
                                    Icons.account_circle,
                                    color: Color(
                                        new CommonColor().lightest_grey_Color),
                                    size: 30,
                                  ),
                                ),
                              )
                            : Container(
                                height: 30,
                                width: 30,
                                decoration: new BoxDecoration(
                                    shape: BoxShape.circle,
                                    image: new DecorationImage(
                                        fit: BoxFit.fill,
                                        image:
                                            new NetworkImage(data.photoPath!))),
                              ),
                        Padding(
                          padding: const EdgeInsets.only(left: 8.0),
                          child: Column(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              /* Text(
                                "Assigned to",
                                style: new CSSStyle().poppinsBlackBold13W400(context),
                              ),*/
                              Row(
                                children: [
                                  Text(
                                    data.workingResource.toString(),
                                    style: new CSSStyle()
                                        .poppinsGreyRegular12(context),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Container(
                      width: 95,
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: GestureDetector(
                              onTap: () {
                                new MyUtils()
                                    .sendMail(data.mailTo.toString(), "", "", context);
                              },
                              child: Icon(
                                Icons.email,
                                size: 20,
                                color:
                                    Color(new CommonColor().green_light_Color),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: GestureDetector(
                              onTap: () {
                                new MyUtils()
                                    .callMe(data.contactNumber!, context);
                              },
                              child: Icon(
                                Icons.local_phone_outlined,
                                size: 20,
                                color:
                                    Color(new CommonColor().green_light_Color),
                              ),
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.all(2.0),
                            child: GestureDetector(
                              onTap: () {
                                new MyUtils().launchWhatsApp(
                                    "+91 " + data.contactNumber.toString());
                              },
                              child: FaIcon(
                                FontAwesomeIcons.whatsapp,
                                size: 20,
                                color: Colors.green[500],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }




}

class CustomDialogBox extends StatefulWidget {
  final String? title, descriptions, text;
  final Image? img;

  const CustomDialogBox(
      {Key? key, this.title, this.descriptions, this.text, this.img})
      : super(key: key);

  @override
  _CustomDialogBoxState createState() => _CustomDialogBoxState();
}

class _CustomDialogBoxState extends State<CustomDialogBox> {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var checkedProgressObs = false.obs;
  var checkedStartedObs = false.obs;
  var checkedCompletedObs = false.obs;
  var checkedBilledObs = false.obs;
  var radioItemObs = 'Mango'.obs;
  var idObs = 1.obs;
  var radioButtonItemObs = 'Due This Week'.obs;

  // Original variables (keeping for compatibility)
  bool checkedProgress = false;
  bool checkedStarted = false;
  bool checkedCompleted = false;
  bool checkedBilled = false;
  String radioItem = 'Mango';
  int id = 1;
  String radioButtonItem = 'Due This Week';

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  contentBox(context) {
    return Stack(
      children: <Widget>[
        SingleChildScrollView(
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: Color(new CommonColor().erpca_light_pink_color),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  child: Column(
                    children: [
                      Card(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                "Reset",
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                            Text(
                              "Filter",
                              style: new CSSStyle().poppinsBlackBold15(context),
                            ),
                            IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  color: Colors.black,
                                ),
                                onPressed: () {
                                  Navigator.of(context, rootNavigator: true)
                                      .pop("Discard");
                                }),
                          ],
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Card(
                          elevation: 1,
                          child: Column(
                            children: [
                              /*CheckboxListTile(
                                title: Text(
                                  "In Progress",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedProgress,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedProgressObs.value = newValue!;
                                  checkedProgress = newValue; // Keep for compatibility
                                  // setState(() { checkedProgress = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "In Progress",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedProgress,
                                          onChanged: (v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedProgressObs.value = v!;
                                            checkedProgress = v; // Keep for compatibility
                                            // setState(() { checkedProgress = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Not Started",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedStarted,
                                          onChanged: (bool? v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedStartedObs.value = v!;
                                            checkedStarted = v; // Keep for compatibility
                                            // setState(() { checkedStarted = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Completed",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedCompleted,
                                          onChanged: (v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedCompletedObs.value = v!;
                                            checkedCompleted = v; // Keep for compatibility
                                            // setState(() { checkedCompleted = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8, bottom: 3),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Biled",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedBilled,
                                          onChanged: (v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedBilledObs.value = v!;
                                            checkedBilled = v; // Keep for compatibility
                                            // setState(() { checkedBilled = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              /*     CheckboxListTile(
                                title: Text(
                                  "Not Started",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedStarted,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedStartedObs.value = newValue!;
                                  checkedStarted = newValue; // Keep for compatibility
                                  // setState(() { checkedStarted = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              /* CheckboxListTile(
                                title: Text(
                                  "Completed",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedCompleted,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedCompletedObs.value = newValue!;
                                  checkedCompleted = newValue; // Keep for compatibility
                                  // setState(() { checkedCompleted = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
/*
                              CheckboxListTile(
                                title: Text(
                                  "Biled",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedBilled,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedBilledObs.value = newValue!;
                                  checkedBilled = newValue; // Keep for compatibility
                                  // setState(() { checkedBilled = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),
*/
                            ],
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                            bottom: 8.0, left: 8.0, right: 8.0),
                        child: Card(
                          elevation: 1,
                          child: Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due This Week',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 1,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // ✅ OPTIMIZED: GetX reactive state management
                                          radioButtonItemObs.value = 'Due This Week';
                                          idObs.value = 1;
                                          radioButtonItem = 'Due This Week'; // Keep for compatibility
                                          id = 1; // Keep for compatibility
                                          // setState(() { radioButtonItem = 'Due This Week'; id = 1; }); // Removed
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due This Month',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 2,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // ✅ OPTIMIZED: GetX reactive state management
                                          radioButtonItemObs.value = 'Due This Month';
                                          idObs.value = 2;
                                          radioButtonItem = 'Due This Month'; // Keep for compatibility
                                          id = 2; // Keep for compatibility
                                          // setState(() { radioButtonItem = 'Due This Month'; id = 2; }); // Removed
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due in current quater',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 3,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // ✅ OPTIMIZED: GetX reactive state management
                                          radioButtonItemObs.value = 'Due in current quater';
                                          idObs.value = 3;
                                          radioButtonItem = 'Due in current quater'; // Keep for compatibility
                                          id = 3; // Keep for compatibility
                                          // setState(() { radioButtonItem = 'Due in current quater'; id = 3; }); // Removed
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due in 6 month',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 4,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // ✅ OPTIMIZED: GetX reactive state management
                                          radioButtonItemObs.value = 'Due in 6 month';
                                          idObs.value = 4;
                                          radioButtonItem = 'Due in 6 month'; // Keep for compatibility
                                          id = 4; // Keep for compatibility
                                          // setState(() { radioButtonItem = 'Due in 6 month'; id = 4; }); // Removed
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Due this year',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 5,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // ✅ OPTIMIZED: GetX reactive state management
                                          radioButtonItemObs.value = 'Due this year';
                                          idObs.value = 5;
                                          radioButtonItem = 'Due this year'; // Keep for compatibility
                                          id = 5; // Keep for compatibility
                                          // setState(() { radioButtonItem = 'Due this year'; id = 5; }); // Removed
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Overdue',
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Radio(
                                        value: 6,
                                        groupValue: id,
                                        onChanged: (val) {
                                          // ✅ OPTIMIZED: GetX reactive state management
                                          radioButtonItemObs.value = 'Overdue';
                                          idObs.value = 6;
                                          radioButtonItem = 'Overdue'; // Keep for compatibility
                                          id = 6; // Keep for compatibility
                                          // setState(() { radioButtonItem = 'Overdue'; id = 6; }); // Removed
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Card(
                          elevation: 1,
                          child: Column(
                            children: [
                              /*CheckboxListTile(
                                title: Text(
                                  "In Progress",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedProgress,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedProgressObs.value = newValue!;
                                  checkedProgress = newValue; // Keep for compatibility
                                  // setState(() { checkedProgress = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "In Progress",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedProgress,
                                          onChanged: (v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedProgressObs.value = v!;
                                            checkedProgress = v; // Keep for compatibility
                                            // setState(() { checkedProgress = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Not Started",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedStarted,
                                          onChanged: (v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedStartedObs.value = v!;
                                            checkedStarted = v; // Keep for compatibility
                                            // setState(() { checkedStarted = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Completed",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedCompleted,
                                          onChanged: (v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedCompletedObs.value = v!;
                                            checkedCompleted = v; // Keep for compatibility
                                            // setState(() { checkedCompleted = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.only(
                                    left: 8.0, top: 3, right: 8, bottom: 3),
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Biled",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    SizedBox(
                                      width: 25,
                                      height: 25,
                                      child: Checkbox(
                                          value: checkedBilled,
                                          onChanged: (v) {
                                            // ✅ OPTIMIZED: GetX reactive state management
                                            checkedBilledObs.value = v!;
                                            checkedBilled = v; // Keep for compatibility
                                            // setState(() { checkedBilled = v!; }); // Removed
                                          }),
                                    )
                                  ],
                                ),
                              ),
                              /*     CheckboxListTile(
                                title: Text(
                                  "Not Started",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedStarted,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedStartedObs.value = newValue!;
                                  checkedStarted = newValue; // Keep for compatibility
                                  // setState(() { checkedStarted = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
                              /* CheckboxListTile(
                                title: Text(
                                  "Completed",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedCompleted,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedCompletedObs.value = newValue!;
                                  checkedCompleted = newValue; // Keep for compatibility
                                  // setState(() { checkedCompleted = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),*/
/*
                              CheckboxListTile(
                                title: Text(
                                  "Biled",
                                  style: new CSSStyle()
                                      .poppinsGreyRegular12(context),
                                ),
                                value: checkedBilled,
                                onChanged: (newValue) {
                                  // ✅ OPTIMIZED: GetX reactive state management
                                  checkedBilledObs.value = newValue!;
                                  checkedBilled = newValue; // Keep for compatibility
                                  // setState(() { checkedBilled = newValue; }); // Removed
                                },
                                controlAffinity: ListTileControlAffinity
                                    .trailing, //  <-- leading Checkbox
                              ),
*/
                            ],
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.fromLTRB(8, 8, 8, 8),
                        constraints:
                            BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(new CommonColor().erpca_blue_color),
                            elevation: 5.0,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(10.0),
                            ),
                            minimumSize: Size(225.0, 50.0),
                          ),
                          child: Text(
                            'APPLY',
                            style: new CSSStyle().verdanaWhiteLight14(context),
                          ),
                          onPressed: () {
                            Get.back();
                          },
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget checkboxTile(String title) {
    return Row(children: [
      Text(title),
      Checkbox(value: true, onChanged: (v) => null),
    ]);
  }
}
