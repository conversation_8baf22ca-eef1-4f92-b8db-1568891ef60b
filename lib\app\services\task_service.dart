// Task Service
// Handles task-related API operations

import 'package:get/get.dart';
import 'api_service.dart';

class TaskService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get all tasks
  Future<List<Map<String, dynamic>>> getAllTasks() async {
    try {
      final response = await _apiService.get('/tasks');
      if (response['success']) {
        return List<Map<String, dynamic>>.from(response['data']['tasks']);
      }
      return [];
    } catch (e) {
      print('Get tasks error: $e');
      return [];
    }
  }

  // Get task by ID
  Future<Map<String, dynamic>?> getTaskById(String taskId) async {
    try {
      final response = await _apiService.get('/tasks/$taskId');
      if (response['success']) {
        return response['data']['task'];
      }
      return null;
    } catch (e) {
      print('Get task error: $e');
      return null;
    }
  }

  // Create new task
  Future<Map<String, dynamic>> createTask(Map<String, dynamic> taskData) async {
    try {
      return await _apiService.post('/tasks', body: taskData);
    } catch (e) {
      return {'success': false, 'message': 'Failed to create task'};
    }
  }

  // Update task
  Future<Map<String, dynamic>> updateTask(String taskId, Map<String, dynamic> updates) async {
    try {
      return await _apiService.put('/tasks/$taskId', body: updates);
    } catch (e) {
      return {'success': false, 'message': 'Failed to update task'};
    }
  }

  // Delete task
  Future<Map<String, dynamic>> deleteTask(String taskId) async {
    try {
      return await _apiService.delete('/tasks/$taskId');
    } catch (e) {
      return {'success': false, 'message': 'Failed to delete task'};
    }
  }

  // Get tasks by status
  Future<List<Map<String, dynamic>>> getTasksByStatus(String status) async {
    try {
      final response = await _apiService.get('/tasks?status=$status');
      if (response['success']) {
        return List<Map<String, dynamic>>.from(response['data']['tasks']);
      }
      return [];
    } catch (e) {
      print('Get tasks by status error: $e');
      return [];
    }
  }
}
