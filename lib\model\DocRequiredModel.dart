import '_TaskInfo.dart';

class DocRequiredModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  DocRequiredModel(
      {int? status,
      String? message,
      int? success,
      List<Data>? data,
      String? errorDev}) {
    this._status = status;
    this._message = message;
    this._success = success;
    this._data = data;
    this._errorDev = errorDev;
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  DocRequiredModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _documentPrimaryKeyId;
  String? _wcDocName;
  String? _filePath;
  String? _isComplete;
  String? _docStatus;
  TaskInfo? _task;

  Data(
      {String? documentPrimaryKeyId,
        String? wcDocName,
        String? filePath,
        String? isComplete,
      TaskInfo? task,
        String? docStatus}) {
    this._documentPrimaryKeyId = documentPrimaryKeyId;
    this._wcDocName = wcDocName;
    this._filePath = filePath;
    this._isComplete = isComplete;
    this._docStatus = docStatus;
    this._task = task;
  }

  String? get documentPrimaryKeyId => _documentPrimaryKeyId;
  set documentPrimaryKeyId(String? documentPrimaryKeyId) =>
      _documentPrimaryKeyId = documentPrimaryKeyId;
  String? get wcDocName => _wcDocName;
  set wcDocName(String? wcDocName) => _wcDocName = wcDocName;
  String? get filePath => _filePath;
  set filePath(String? filePath) => _filePath = filePath;
  String? get isComplete => _isComplete;
  set isComplete(String? isComplete) => _isComplete = isComplete;
  String? get docStatus => _docStatus;
  TaskInfo? get task => _task;
  set docStatus(String? docStatus) => _docStatus = docStatus;
  set task(TaskInfo? task) => _task = task;

  Data.fromJson(Map<String, dynamic> json) {
    _documentPrimaryKeyId = json['document_primary_key_id'];
    _wcDocName = json['wc_doc_name'];
    _filePath = json['file_path'];
    _isComplete = json['is_complete'];
    _docStatus = json['doc_status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['document_primary_key_id'] = this._documentPrimaryKeyId;
    data['wc_doc_name'] = this._wcDocName;
    data['file_path'] = this._filePath;
    data['is_complete'] = this._isComplete;
    data['doc_status'] = this._docStatus;
    return data;
  }
}
