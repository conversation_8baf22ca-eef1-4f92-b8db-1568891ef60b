class UserDetailsModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  UserDetailsModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  UserDetailsModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _userId;
  String? _firstName;
  String? _lastName;
  String? _accId;
  String? _userGroupId;
  String? _photoThumb;
  String? _userEmail;
  String? _contactPhone1;
  String? _contactPhone2;
  String? _about;
  String? _twitterUrl;
  String? _linkedinUrl;
  String? _youtubeUrl;
  String? _facebookUrl;
  String? _address;
  String? _city;
  String? _state;
  String? _pincode;
  String? _mobileNo;
  String? _companyName;
  String? _branchId;
  String? _customerGroupId;
  String? _branchName;
  String? _groupName;
  String? _gstNo;
  String? _stateCode;
  String? _customerId;
  String? _panNo;
  String? _destinationAddress;
  String? _additionalEmail;
  String? _businessPanNo;

  Data(
      {String? userId,
        String? firstName,
        String? lastName,
        String? accId,
        String? userGroupId,
        String? photoThumb,
        String? userEmail,
        String? contactPhone1,
        String? contactPhone2,
        String? about,
        String? twitterUrl,
        String? linkedinUrl,
        String? youtubeUrl,
        String? facebookUrl,
        String? address,
        String? city,
        String? state,
        String? pincode,
        String? mobileNo,
        String? companyName,
        String? branchId,
        String? customerGroupId,
        String? branchName,
        String? groupName,
        String? gstNo,
        String? stateCode,
        String? customerId,
        String? panNo,
        String? destinationAddress,
        String? additionalEmail,
        String? businessPanNo}) {
    if (userId != null) {
      this._userId = userId;
    }
    if (firstName != null) {
      this._firstName = firstName;
    }
    if (lastName != null) {
      this._lastName = lastName;
    }
    if (accId != null) {
      this._accId = accId;
    }
    if (userGroupId != null) {
      this._userGroupId = userGroupId;
    }
    if (photoThumb != null) {
      this._photoThumb = photoThumb;
    }
    if (userEmail != null) {
      this._userEmail = userEmail;
    }
    if (contactPhone1 != null) {
      this._contactPhone1 = contactPhone1;
    }
    if (contactPhone2 != null) {
      this._contactPhone2 = contactPhone2;
    }
    if (about != null) {
      this._about = about;
    }
    if (twitterUrl != null) {
      this._twitterUrl = twitterUrl;
    }
    if (linkedinUrl != null) {
      this._linkedinUrl = linkedinUrl;
    }
    if (youtubeUrl != null) {
      this._youtubeUrl = youtubeUrl;
    }
    if (facebookUrl != null) {
      this._facebookUrl = facebookUrl;
    }
    if (address != null) {
      this._address = address;
    }
    if (city != null) {
      this._city = city;
    }
    if (state != null) {
      this._state = state;
    }
    if (pincode != null) {
      this._pincode = pincode;
    }
    if (mobileNo != null) {
      this._mobileNo = mobileNo;
    }
    if (companyName != null) {
      this._companyName = companyName;
    }
    if (branchId != null) {
      this._branchId = branchId;
    }
    if (customerGroupId != null) {
      this._customerGroupId = customerGroupId;
    }
    if (branchName != null) {
      this._branchName = branchName;
    }
    if (groupName != null) {
      this._groupName = groupName;
    }
    if (gstNo != null) {
      this._gstNo = gstNo;
    }
    if (stateCode != null) {
      this._stateCode = stateCode;
    }
    if (customerId != null) {
      this._customerId = customerId;
    }
    if (panNo != null) {
      this._panNo = panNo;
    }
    if (destinationAddress != null) {
      this._destinationAddress = destinationAddress;
    }
    if (additionalEmail != null) {
      this._additionalEmail = additionalEmail;
    }
    if (businessPanNo != null) {
      this._businessPanNo = businessPanNo;
    }
  }

  String? get userId => _userId;
  set userId(String? userId) => _userId = userId;
  String? get firstName => _firstName;
  set firstName(String? firstName) => _firstName = firstName;
  String? get lastName => _lastName;
  set lastName(String? lastName) => _lastName = lastName;
  String? get accId => _accId;
  set accId(String? accId) => _accId = accId;
  String? get userGroupId => _userGroupId;
  set userGroupId(String? userGroupId) => _userGroupId = userGroupId;
  String? get photoThumb => _photoThumb;
  set photoThumb(String? photoThumb) => _photoThumb = photoThumb;
  String? get userEmail => _userEmail;
  set userEmail(String? userEmail) => _userEmail = userEmail;
  String? get contactPhone1 => _contactPhone1;
  set contactPhone1(String? contactPhone1) => _contactPhone1 = contactPhone1;
  String? get contactPhone2 => _contactPhone2;
  set contactPhone2(String? contactPhone2) => _contactPhone2 = contactPhone2;
  String? get about => _about;
  set about(String? about) => _about = about;
  String? get twitterUrl => _twitterUrl;
  set twitterUrl(String? twitterUrl) => _twitterUrl = twitterUrl;
  String? get linkedinUrl => _linkedinUrl;
  set linkedinUrl(String? linkedinUrl) => _linkedinUrl = linkedinUrl;
  String? get youtubeUrl => _youtubeUrl;
  set youtubeUrl(String? youtubeUrl) => _youtubeUrl = youtubeUrl;
  String? get facebookUrl => _facebookUrl;
  set facebookUrl(String? facebookUrl) => _facebookUrl = facebookUrl;
  String? get address => _address;
  set address(String? address) => _address = address;
  String? get city => _city;
  set city(String? city) => _city = city;
  String? get state => _state;
  set state(String? state) => _state = state;
  String? get pincode => _pincode;
  set pincode(String? pincode) => _pincode = pincode;
  String? get mobileNo => _mobileNo;
  set mobileNo(String? mobileNo) => _mobileNo = mobileNo;
  String? get companyName => _companyName;
  set companyName(String? companyName) => _companyName = companyName;
  String? get branchId => _branchId;
  set branchId(String? branchId) => _branchId = branchId;
  String? get customerGroupId => _customerGroupId;
  set customerGroupId(String? customerGroupId) =>
      _customerGroupId = customerGroupId;
  String? get branchName => _branchName;
  set branchName(String? branchName) => _branchName = branchName;
  String? get groupName => _groupName;
  set groupName(String? groupName) => _groupName = groupName;
  String? get gstNo => _gstNo;
  set gstNo(String? gstNo) => _gstNo = gstNo;
  String? get stateCode => _stateCode;
  set stateCode(String? stateCode) => _stateCode = stateCode;
  String? get customerId => _customerId;
  set customerId(String? customerId) => _customerId = customerId;
  String? get panNo => _panNo;
  set panNo(String? panNo) => _panNo = panNo;
  String? get destinationAddress => _destinationAddress;
  set destinationAddress(String? destinationAddress) =>
      _destinationAddress = destinationAddress;
  String? get additionalEmail => _additionalEmail;
  set additionalEmail(String? additionalEmail) =>
      _additionalEmail = additionalEmail;
  String? get businessPanNo => _businessPanNo;
  set businessPanNo(String? businessPanNo) => _businessPanNo = businessPanNo;

  Data.fromJson(Map<String, dynamic> json) {
    _userId = json['user_id'];
    _firstName = json['first_name'];
    _lastName = json['last_name'];
    _accId = json['acc_id'];
    _userGroupId = json['user_group_id'];
    _photoThumb = json['photo_thumb'];
    _userEmail = json['user_email'];
    _contactPhone1 = json['contact_phone_1'];
    _contactPhone2 = json['contact_phone_2'];
    _about = json['about'];
    _twitterUrl = json['twitter_url'];
    _linkedinUrl = json['linkedin_url'];
    _youtubeUrl = json['youtube_url'];
    _facebookUrl = json['facebook_url'];
    _address = json['address'];
    _city = json['city'];
    _state = json['state'];
    _pincode = json['pincode'];
    _mobileNo = json['mobile_no'];
    _companyName = json['company_name'];
    _branchId = json['branch_id'];
    _customerGroupId = json['customer_group_id'];
    _branchName = json['branch_name'];
    _groupName = json['group_name'];
    _gstNo = json['gst_no'];
    _stateCode = json['state_code'];
    _customerId = json['customer_id'];
    _panNo = json['pan_no'];
    _destinationAddress = json['destination_address'];
    _additionalEmail = json['additional_email'];
    _businessPanNo = json['business_pan_no'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this._userId;
    data['first_name'] = this._firstName;
    data['last_name'] = this._lastName;
    data['acc_id'] = this._accId;
    data['user_group_id'] = this._userGroupId;
    data['photo_thumb'] = this._photoThumb;
    data['user_email'] = this._userEmail;
    data['contact_phone_1'] = this._contactPhone1;
    data['contact_phone_2'] = this._contactPhone2;
    data['about'] = this._about;
    data['twitter_url'] = this._twitterUrl;
    data['linkedin_url'] = this._linkedinUrl;
    data['youtube_url'] = this._youtubeUrl;
    data['facebook_url'] = this._facebookUrl;
    data['address'] = this._address;
    data['city'] = this._city;
    data['state'] = this._state;
    data['pincode'] = this._pincode;
    data['mobile_no'] = this._mobileNo;
    data['company_name'] = this._companyName;
    data['branch_id'] = this._branchId;
    data['customer_group_id'] = this._customerGroupId;
    data['branch_name'] = this._branchName;
    data['group_name'] = this._groupName;
    data['gst_no'] = this._gstNo;
    data['state_code'] = this._stateCode;
    data['customer_id'] = this._customerId;
    data['pan_no'] = this._panNo;
    data['destination_address'] = this._destinationAddress;
    data['additional_email'] = this._additionalEmail;
    data['business_pan_no'] = this._businessPanNo;
    return data;
  }
}
