import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:erpcacustomer/activity/SubmitRequestActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:erpcacustomer/model/WorkCategoryDocRequiredModel.dart';
import 'package:erpcacustomer/model/DocumentRequiredDeleteModel.dart';
import 'package:get/get.dart';

import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'dart:io' as Io;
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:background_app_bar/background_app_bar.dart';
import 'dart:ui';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class RequiredDocumentsFragment extends StatefulWidget {
  String? workcategoryId;
  String? service_request_id;

  RequiredDocumentsFragment(
      {Key? key, this.workcategoryId, this.service_request_id})
      : super(key: key);

  @override
  RequiredDocumentsFragmentState createState() {
    return new RequiredDocumentsFragmentState();
  }
}

class RequiredDocumentsFragmentState extends State<RequiredDocumentsFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var hasCardObs = false.obs;
  var loadingPathObs = false.obs;
  var savingObs = false.obs;
  var imageObs = Rx<File?>(null);
  var directoryPathObs = ''.obs;

  // Original variables (keeping for compatibility)
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  ImagePickerHandler? imagePicker;
  File? _image;
  AnimationController? _controller;
  String document_primary_key_id = "";
  bool _loadingPath = false;
  String _directoryPath = '';
  List<PlatformFile>? _paths;
  FileType _pickingType = FileType.any;
  bool _multiPick = false;
  String _extension = '';
  String _fileName = '';
  bool _saving = false;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
  }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Stack(
        children: [
          _buildBackground(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Material(
      child: NestedScrollView(
        headerSliverBuilder: (_, __) => <Widget>[
          new SliverAppBar(
            leading: IconButton(
                icon: Icon(Icons.arrow_back),
                onPressed: () {
                  moveToLastScreen();
                }),
            expandedHeight: 250,
            floating: false,
            pinned: true,
            snap: false,
            elevation: 0.0,
            backgroundColor: Colors.transparent,
            flexibleSpace: new BackgroundFlexibleSpaceBar(
              title: Text(
                "List of document required \nto execute your services",
                textAlign: TextAlign.center,
                style: new CSSStyle().poppinsWhiteRegular12(context),
              ),
              centerTitle: true,
              collapseMode: CollapseMode.none,
              titlePadding: const EdgeInsets.only(left: 20.0, bottom: 20.0),
              background: new ClipRect(
                child: new Container(
                  child: new BackdropFilter(
                    filter: new ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                    child: new Container(),
                  ),
                  decoration: new BoxDecoration(
                      image: new DecorationImage(
                    image: new AssetImage(
                      "assets/images/gp2.png",
                    ),
                    fit: BoxFit.fill,
                  )),
                ),
              ),
            ),
          ),
        ],
        body: Stack(children: <Widget>[
          //Above card

          /* FutureBuilder<WorkCategoryDocRequiredModel>(
            future: workCategoryDocRequiredListCall(Constants.ACC_ID,
                widget.workcategoryId.toString(), widget.service_request_id.toString(), context),
            builder: (context, snapshot) {
              return snapshot.hasData
                  ? Padding(
                      padding: const EdgeInsets.only(top: 5.0),
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            flex: 9,
                            child: ListView.separated(
                              physics: ClampingScrollPhysics(),
                              shrinkWrap: true,
                              separatorBuilder: (context, index) {
                                return Padding(
                                  padding: const EdgeInsets.only(
                                      left: 13.0, right: 13),
                                  child: Divider(),
                                );
                              },
                              padding: EdgeInsets.zero,
                              itemCount: snapshot.data!.data!.length,
                              itemBuilder: (BuildContext context, int index) {
                                return _gstBottomValue(
                                    index, snapshot.data!.data!);
                              },
                            ),
                          ),
                          _addAccessMyAccountButtonUi()
                        ],
                      ),
                    )
                  : Center(
                      child: Container(
                          color: Colors.white,
                          child: new MyUtils().kLoadingWidget(context)
                      ));
            },
          ), */
          FutureBuilder<WorkCategoryDocRequiredModel>(
  future: workCategoryDocRequiredListCall(
    Constants.ACC_ID,
    widget.workcategoryId.toString(),
    widget.service_request_id.toString(),
    context,
  ),
  builder: (context, snapshot) {
    if (snapshot.connectionState == ConnectionState.waiting) {
      return Center(
        child: Container(
          color: Colors.white,
          child: MyUtils().kLoadingWidget(context),
        ),
      );
    } else if (snapshot.hasData) {
      final dataList = snapshot.data!.data;

      if (dataList == null || dataList.isEmpty) {
        return Center(
          child: Text(
            'No document required',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
          ),
        );
      }

      return Padding(
        padding: const EdgeInsets.only(top: 5.0),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 9,
              child: ListView.separated(
                physics: ClampingScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (context, index) {
                  return Padding(
                    padding: const EdgeInsets.only(left: 13.0, right: 13),
                    child: Divider(),
                  );
                },
                padding: EdgeInsets.zero,
                itemCount: dataList.length,
                itemBuilder: (BuildContext context, int index) {
                  return _gstBottomValue(index, dataList);
                },
              ),
            ),
            _addAccessMyAccountButtonUi(),
          ],
        ),
      );
    } else {
      return Center(
        child: Text(
          'Something went wrong!',
          style: TextStyle(color: Colors.red, fontSize: 16),
        ),
      );
    }
  },
),

          // Positioned to take only AppBar size
        ]),
      ),
    );
  }

   postWorkCategoryDocRequiredListCall(
      String acc_id,
      String workcategory_id,
      String service_request_id,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await workCategoryDocRequiredListApi(
          acc_id, workcategory_id, service_request_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        WorkCategoryDocRequiredModel currentParsedResponse =
            WorkCategoryDocRequiredModel.fromJson(responseJson);

        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<WorkCategoryDocRequiredModel> workCategoryDocRequiredListCall(
      String acc_id,
      String workcategory_id,
      String service_request_id,
      BuildContext context) async {
    return await postWorkCategoryDocRequiredListCall(acc_id, workcategory_id, service_request_id, context);
  }

  Widget _gstBottomValue(int index, List<Data> data) {
    {
      Data allData = data[index];

      return GestureDetector(
        onTap: () {
          /*  Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => DocumentDetailsSecondActivity(
                      asst_year: allData.asstYear,
                    )),
          );*/
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 13.0, right: 13, top: 5, bottom: 5),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Icon(
                        Icons.insert_drive_file,
                        color: Color(new CommonColor().green_light_Color),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              width: 250,
                              child: Text(
                                allData.docRequiredName.toString(),
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                            /*  Text(
                              allData.availableDocuments,
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),*/
                          ],
                        ),
                      ),
                    ],
                  ),
                  (allData.filePath == "")
                      ? Row(
                          children: [
                            GestureDetector(
                              onTap: () async {
                                //   imagePicker.showDialog(context);
                                document_primary_key_id = allData.wcDocReqId!;

                                _openFileExplorer();
                              },
                              child: Icon(
                                Icons.cloud_upload,
                                size: 20,
                                color:
                                    Color(new CommonColor().green_light_Color),
                              ),
                            ),
                            GestureDetector(
                              onTap: () async {
                                //   imagePicker.showDialog(context);
                                document_primary_key_id = allData.wcDocReqId!;

                                imagePicker!.showDialog(context);
                              },
                              child: Icon(
                                Icons.camera_alt,
                                size: 20,
                                color:
                                    Color(new CommonColor().green_light_Color),
                              ),
                            )
                          ],
                        )
                      : Row(
                          children: [
                            GestureDetector(
                              onTap: () async {
                                document_primary_key_id = allData.wcDocReqId!;
                                _onLogoutPressed();


                                //   await _dialogCall(allData.filePath, context);
                              },
                              child: Icon(
                                Icons.clear,
                                size: 20,
                                color:
                                    Color(new CommonColor().red_lighter_Color),
                              ),
                            ),
                            GestureDetector(
                              onTap: () async {
                                MyUtils().getDownloadUrl1(allData.filePath!);
                                },
                              child: Icon(
                                Icons.image_search_outlined,
                                size: 20,
                                color:
                                    Color(new CommonColor().blue_lighter_Color),
                              ),
                            ),
                          ],
                        ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  @override
  userImage(Io.File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // ✅ OPTIMIZED: GetX reactive state management
      imageObs.value = _image;
      loadingPathObs.value = false;
      savingObs.value = true;
      this._image = _image; // Keep for compatibility
      _loadingPath = false; // Keep for compatibility
      _saving = true; // Keep for compatibility
      // setState(() { ... }); // Removed
      workCategoryUploadDocRequiredListCall(
          Constants.ACC_ID,
          Constants.USER_ID,
          widget.service_request_id.toString(),
          document_primary_key_id,
          _image.path,
          context);
    }
  }

  Object _onLogoutPressed() {
    return showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text('Are you sure?'),
            content: new Text('Do you really want to delete file?'),
            actions: <Widget>[
              new GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("NO"),
                ),
              ),
              SizedBox(height: 16),
              new GestureDetector(
                onTap: () {
                  workCategoryDeleteDocRequiredListCall(
                      Constants.ACC_ID,
                      Constants.USER_ID,
                      widget.service_request_id.toString(),
                      document_primary_key_id,
                      context);
                  return Navigator.of(context).popUntil((route) => route.isFirst);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("YES"),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

 workCategoryUploadDocRequiredListCall(
      String acc_id,
      String user_id,
      String service_request_id,
      String document_primary_key_id,
      String _image,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    final bytes = Io.File(_image).readAsBytesSync();

    String img64 = base64Encode(bytes);
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await workCategoryUploadDocRequiredListApi(acc_id,
          user_id, service_request_id, document_primary_key_id, img64, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        WorkCategoryDocRequiredModel currentParsedResponse =
            WorkCategoryDocRequiredModel.fromJson(responseJson);
        // ✅ OPTIMIZED: GetX reactive state management
        savingObs.value = false;
        _saving = false; // Keep for compatibility
        // setState(() { _saving = false; }); // Removed

        MyUtils.showOkDialog(
            context, "Success", responseJson['message'].toString());
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
        // ✅ OPTIMIZED: GetX reactive state management
        savingObs.value = false;
        _saving = false; // Keep for compatibility
        // setState(() { _saving = false; }); // Removed
      }
    } else {
      // ✅ OPTIMIZED: GetX reactive state management
      savingObs.value = false;
      _saving = false; // Keep for compatibility
      // setState(() { _saving = false; }); // Removed
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  workCategoryDeleteDocRequiredListCall(
      String acc_id,
      String user_id,
      String service_request_id,
      String document_primary_key_id,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    // ✅ OPTIMIZED: GetX reactive state management
    savingObs.value = true;
    _saving = true; // Keep for compatibility
    // setState(() { _saving = true; }); // Removed

    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      try {
        var responseJson = await workCategoryDeleteDocRequiredListApi(acc_id,
            user_id, service_request_id, document_primary_key_id, context);
        int flag = responseJson["success"];

        if (flag == 1) {
          DocumentRequiredDeleteModel currentParsedResponse =
              DocumentRequiredDeleteModel.fromJson(responseJson);
          // ✅ OPTIMIZED: GetX reactive state management
          savingObs.value = false;
          _saving = false; // Keep for compatibility
          // setState(() { _saving = false; }); // Removed
          MyUtils.showOkDialog(
              context, "Success", responseJson['message'].toString());
          return currentParsedResponse;
        } else {
          // ✅ OPTIMIZED: GetX reactive state management
          savingObs.value = false;
          _saving = false; // Keep for compatibility
          // setState(() { _saving = false; }); // Removed
          MyUtils.showOkDialog(
              context, "Error", responseJson['message'].toString());
        }
      } catch (e) {
        // ✅ OPTIMIZED: Exception handling
        savingObs.value = false;
        _saving = false; // Keep for compatibility
        MyUtils.showOkDialog(context, "Error", "Failed to delete document");
      }
    } else {
      // ✅ OPTIMIZED: GetX reactive state management
      savingObs.value = false;
      _saving = false; // Keep for compatibility
      // setState(() { _saving = false; }); // Removed
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  void _openFileExplorer() async {
    // ✅ OPTIMIZED: GetX reactive state management
    loadingPathObs.value = true;
    _loadingPath = true; // Keep for compatibility
    // setState(() => _loadingPath = true); // Removed

    try {
      _directoryPath = '';
      directoryPathObs.value = '';
      _paths = (await FilePicker.platform.pickFiles(
        type: _pickingType,
        allowMultiple: _multiPick,
        allowedExtensions: (_extension?.isNotEmpty ?? false)
            ? _extension?.replaceAll(' ', '').split(',')
            : null,
      ))
          ?.files;
    } on PlatformException catch (e) {
      // ✅ OPTIMIZED: Exception handling
      loadingPathObs.value = false;
      _loadingPath = false;
      MyUtils.showOkDialog(context, "Error", "Failed to pick file");
      return;
    } catch (ex) {
      // ✅ OPTIMIZED: Exception handling
      loadingPathObs.value = false;
      _loadingPath = false;
      MyUtils.showOkDialog(context, "Error", "Failed to pick file");
      return;
    }
    if (!mounted) return;

    // ✅ OPTIMIZED: GetX reactive state management
    loadingPathObs.value = false;
    savingObs.value = true;
    _loadingPath = false; // Keep for compatibility
    _saving = true; // Keep for compatibility
    // setState(() { ... }); // Removed

    if (_paths != null && _paths!.isNotEmpty) {
      workCategoryUploadDocRequiredListCall(
          Constants.ACC_ID,
          Constants.USER_ID,
          widget.service_request_id.toString(),
          document_primary_key_id,
          _paths!.first.path.toString(),
          context);
      _fileName = _paths != null ? _paths!.map((e) => e.name).toString() : '...';
    }
  }

  _addAccessMyAccountButtonUi() {
    return Column(
      children: [
        Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            margin: EdgeInsets.only(left: 15, right: 15, bottom: 15),
            constraints: BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
            child: GradientButton(
              gradient: LinearGradient(colors: [
                Color(new CommonColor().erpca_blue_color),
                Color(new CommonColor().erpca_blue_color)
              ], begin: Alignment.centerLeft, end: Alignment.centerRight),
              //color: Colors.cyan,
              elevation: 5.0,
              shape: new RoundedRectangleBorder(
                  borderRadius: new BorderRadius.circular(10.0)),
              //splashColor: Colors.blueGrey,
              //color: Theme.of(context).accentColor,
              //textColor: Theme.of(context).primaryColorLight,
              child: Text(
                'SUBMIT ',
                style: new CSSStyle().verdanaWhiteLight14(context),
              ),
              callback: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => SubmitRequestActivity(
                            service_request_id: widget.service_request_id,
                          )),
                );
                //postCreateOrderCall(context);
              },
              increaseWidthBy: 500.0,
              increaseHeightBy: 80.0,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _dialogCall(String imagePath, BuildContext context) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return MyDialog(
            imagePath: imagePath,
          );
        });
  }
}

class MyDialog extends StatefulWidget {
  String imagePath;
  MyDialog({Key? key, required this.imagePath}) : super(key: key);
  @override
  _MyDialogState createState() => new _MyDialogState();
}

class _MyDialogState extends State<MyDialog> {
  String imagePath = '';
  Image? image;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      content: new SingleChildScrollView(
        child: new ListBody(
          children: <Widget>[
            Image.network(
              widget.imagePath,
            ),
          ],
        ),
      ),
    );
  }

/*  Future getImageFromCamera() async {
    var x = await ImagePicker.pickImage(source: ImageSource.camera);
    imagePath = x.path;
    image = Image(image: FileImage(x));
  }*/
}
