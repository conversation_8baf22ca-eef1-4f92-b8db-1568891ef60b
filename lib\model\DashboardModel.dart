class DashboardModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  List<BillDueData>? _billDueData;
  String? _errorDev;

  DashboardModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        List<BillDueData>? billDueData,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (billDueData != null) {
      this._billDueData = billDueData;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  List<BillDueData>? get billDueData => _billDueData;
  set billDueData(List<BillDueData>? billDueData) => _billDueData = billDueData;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  DashboardModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    if (json['bill_due_data'] != null) {
      _billDueData = <BillDueData>[];
      json['bill_due_data'].forEach((v) {
        _billDueData!.add(new BillDueData.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    if (this._billDueData != null) {
      data['bill_due_data'] =
          this._billDueData!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _userId;
  String? _activeProjects;
  String? _completedProjects;
  String? _billedAmount;
  String? _paidAmount;
  String? _balanceAmount;
  String? _workDocuments;
  String? _coreDocuments;
  String? _totalServiceRequests;

  Data(
      {String? userId,
        String? activeProjects,
        String? completedProjects,
        String? billedAmount,
        String? paidAmount,
        String? balanceAmount,
        String? workDocuments,
        String? coreDocuments,
        String? totalServiceRequests}) {
    if (userId != null) {
      this._userId = userId;
    }
    if (activeProjects != null) {
      this._activeProjects = activeProjects;
    }
    if (completedProjects != null) {
      this._completedProjects = completedProjects;
    }
    if (billedAmount != null) {
      this._billedAmount = billedAmount;
    }
    if (paidAmount != null) {
      this._paidAmount = paidAmount;
    }
    if (balanceAmount != null) {
      this._balanceAmount = balanceAmount;
    }
    if (workDocuments != null) {
      this._workDocuments = workDocuments;
    }
    if (coreDocuments != null) {
      this._coreDocuments = coreDocuments;
    }
    if (totalServiceRequests != null) {
      this._totalServiceRequests = totalServiceRequests;
    }
  }

  String? get userId => _userId;
  set userId(String? userId) => _userId = userId;
  String? get activeProjects => _activeProjects;
  set activeProjects(String? activeProjects) =>
      _activeProjects = activeProjects;
  String? get completedProjects => _completedProjects;
  set completedProjects(String? completedProjects) =>
      _completedProjects = completedProjects;
  String? get billedAmount => _billedAmount;
  set billedAmount(String? billedAmount) => _billedAmount = billedAmount;
  String? get paidAmount => _paidAmount;
  set paidAmount(String? paidAmount) => _paidAmount = paidAmount;
  String? get balanceAmount => _balanceAmount;
  set balanceAmount(String? balanceAmount) => _balanceAmount = balanceAmount;
  String? get workDocuments => _workDocuments;
  set workDocuments(String? workDocuments) => _workDocuments = workDocuments;
  String? get coreDocuments => _coreDocuments;
  set coreDocuments(String? coreDocuments) => _coreDocuments = coreDocuments;
  String? get totalServiceRequests => _totalServiceRequests;
  set totalServiceRequests(String? totalServiceRequests) =>
      _totalServiceRequests = totalServiceRequests;

  Data.fromJson(Map<String, dynamic> json) {
    _userId = json['user_id'];
    _activeProjects = json['active_projects'];
    _completedProjects = json['completed_projects'];
    _billedAmount = json['billed_amount'];
    _paidAmount = json['paid_amount'];
    _balanceAmount = json['balance_amount'];
    _workDocuments = json['work_documents'];
    _coreDocuments = json['core_documents'];
    _totalServiceRequests = json['total_service_requests'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this._userId;
    data['active_projects'] = this._activeProjects;
    data['completed_projects'] = this._completedProjects;
    data['billed_amount'] = this._billedAmount;
    data['paid_amount'] = this._paidAmount;
    data['balance_amount'] = this._balanceAmount;
    data['work_documents'] = this._workDocuments;
    data['core_documents'] = this._coreDocuments;
    data['total_service_requests'] = this._totalServiceRequests;
    return data;
  }
}

class BillDueData {
  String? _billId;
  String? _invoiceType;
  String? _billNo;
  String? _billDate;
  String? _netAmount;
  String? _billStatus;
  String? _amountPaid;
  String? _balanceToBePaid;
  String? _billLink;
  String? _companyName;

  BillDueData(
      {String? billId,
        String? invoiceType,
        String? billNo,
        String? billDate,
        String? netAmount,
        String? billStatus,
        String? amountPaid,
        String? balanceToBePaid,
        String? billLink,
        String? companyName}) {
    if (billId != null) {
      this._billId = billId;
    }
    if (invoiceType != null) {
      this._invoiceType = invoiceType;
    }
    if (billNo != null) {
      this._billNo = billNo;
    }
    if (billDate != null) {
      this._billDate = billDate;
    }
    if (netAmount != null) {
      this._netAmount = netAmount;
    }
    if (billStatus != null) {
      this._billStatus = billStatus;
    }
    if (amountPaid != null) {
      this._amountPaid = amountPaid;
    }
    if (balanceToBePaid != null) {
      this._balanceToBePaid = balanceToBePaid;
    }
    if (billLink != null) {
      this._billLink = billLink;
    }
    if (companyName != null) {
      this._companyName = companyName;
    }
  }

  String? get billId => _billId;
  set billId(String? billId) => _billId = billId;
  String? get invoiceType => _invoiceType;
  set invoiceType(String? invoiceType) => _invoiceType = invoiceType;
  String? get billNo => _billNo;
  set billNo(String? billNo) => _billNo = billNo;
  String? get billDate => _billDate;
  set billDate(String? billDate) => _billDate = billDate;
  String? get netAmount => _netAmount;
  set netAmount(String? netAmount) => _netAmount = netAmount;
  String? get billStatus => _billStatus;
  set billStatus(String? billStatus) => _billStatus = billStatus;
  String? get amountPaid => _amountPaid;
  set amountPaid(String? amountPaid) => _amountPaid = amountPaid;
  String? get balanceToBePaid => _balanceToBePaid;
  set balanceToBePaid(String? balanceToBePaid) =>
      _balanceToBePaid = balanceToBePaid;
  String? get billLink => _billLink;
  set billLink(String? billLink) => _billLink = billLink;
  String? get companyName => _companyName;
  set companyName(String? companyName) => _companyName = companyName;

  BillDueData.fromJson(Map<String, dynamic> json) {
    _billId = json['bill_id'];
    _invoiceType = json['invoice_type'];
    _billNo = json['bill_no'];
    _billDate = json['bill_date'];
    _netAmount = json['net_amount'];
    _billStatus = json['bill_status'];
    _amountPaid = json['amount_paid'];
    _balanceToBePaid = json['balance_to_be_paid'];
    _billLink = json['bill_link'];
    _companyName = json['company_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['bill_id'] = this._billId;
    data['invoice_type'] = this._invoiceType;
    data['bill_no'] = this._billNo;
    data['bill_date'] = this._billDate;
    data['net_amount'] = this._netAmount;
    data['bill_status'] = this._billStatus;
    data['amount_paid'] = this._amountPaid;
    data['balance_to_be_paid'] = this._balanceToBePaid;
    data['bill_link'] = this._billLink;
    data['company_name'] = this._companyName;
    return data;
  }
}
