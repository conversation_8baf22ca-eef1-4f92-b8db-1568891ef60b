import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsFragment.dart';
import 'package:erpcacustomer/fragment/NewServicesFragment.dart';
import 'package:erpcacustomer/activity/HomePageActivity.dart';

import 'package:flutter/material.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:get/get.dart';

import 'package:erpcacustomer/activity/NewServicesActivity.dart';
import 'package:erpcacustomer/activity/GstActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/controller/TaskController.dart';
import 'package:erpcacustomer/model/TaskListModel.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:table_calendar/table_calendar.dart';
import "package:percent_indicator/linear_percent_indicator.dart";
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class MyBlinkingButton extends StatefulWidget {
  @override
  _MyBlinkingButtonState createState() => _MyBlinkingButtonState();
}

class _MyBlinkingButtonState extends State<MyBlinkingButton>
    with SingleTickerProviderStateMixin {
  AnimationController? _animationController;

  @override
  void initState() {
    _animationController =
        new AnimationController(vsync: this, duration: Duration(seconds: 1));
    _animationController!.repeat(reverse: true);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _animationController!,
      child: Material(
          color: Colors.redAccent,
          shape: CircleBorder(),
          child: Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(""),
          )),
    );
  }

  @override
  void dispose() {
    _animationController!.dispose();
    super.dispose();
  }
}
