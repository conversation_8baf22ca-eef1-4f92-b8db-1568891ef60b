class CustomerDocumentScreen1Model {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  List<OthersFolders>? _othersFolders;
  String? _errorDev;

  CustomerDocumentScreen1Model(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        List<OthersFolders>? othersFolders,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (othersFolders != null) {
      this._othersFolders = othersFolders;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  List<OthersFolders>? get othersFolders => _othersFolders;
  set othersFolders(List<OthersFolders>? othersFolders) =>
      _othersFolders = othersFolders;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CustomerDocumentScreen1Model.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    if (json['others_folders'] != null) {
      _othersFolders = <OthersFolders>[];
      json['others_folders'].forEach((v) {
        _othersFolders!.add(new OthersFolders.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    if (this._othersFolders != null) {
      data['others_folders'] =
          this._othersFolders!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _displayAsstYear;
  String? _asstYear;
  String? _availableDocuments;

  Data(
      {String? displayAsstYear, String? asstYear, String? availableDocuments}) {
    if (displayAsstYear != null) {
      this._displayAsstYear = displayAsstYear;
    }
    if (asstYear != null) {
      this._asstYear = asstYear;
    }
    if (availableDocuments != null) {
      this._availableDocuments = availableDocuments;
    }
  }

  String? get displayAsstYear => _displayAsstYear;
  set displayAsstYear(String? displayAsstYear) =>
      _displayAsstYear = displayAsstYear;
  String? get asstYear => _asstYear;
  set asstYear(String? asstYear) => _asstYear = asstYear;
  String? get availableDocuments => _availableDocuments;
  set availableDocuments(String? availableDocuments) =>
      _availableDocuments = availableDocuments;

  Data.fromJson(Map<String, dynamic> json) {
    _displayAsstYear = json['display_asst_year'];
    _asstYear = json['asst_year'];
    _availableDocuments = json['available_documents'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['display_asst_year'] = this._displayAsstYear;
    data['asst_year'] = this._asstYear;
    data['available_documents'] = this._availableDocuments;
    return data;
  }
}

class OthersFolders {
  String? _id;
  String? _parentId;
  String? _folderName;
  String? _accId;
  String? _customerId;
  String? _createdOn;

  OthersFolders(
      {String? id,
        String? parentId,
        String? folderName,
        String? accId,
        String? customerId,
        String? createdOn}) {
    if (id != null) {
      this._id = id;
    }
    if (parentId != null) {
      this._parentId = parentId;
    }
    if (folderName != null) {
      this._folderName = folderName;
    }
    if (accId != null) {
      this._accId = accId;
    }
    if (customerId != null) {
      this._customerId = customerId;
    }
    if (createdOn != null) {
      this._createdOn = createdOn;
    }
  }

  String? get id => _id;
  set id(String? id) => _id = id;
  String? get parentId => _parentId;
  set parentId(String? parentId) => _parentId = parentId;
  String? get folderName => _folderName;
  set folderName(String? folderName) => _folderName = folderName;
  String? get accId => _accId;
  set accId(String? accId) => _accId = accId;
  String? get customerId => _customerId;
  set customerId(String? customerId) => _customerId = customerId;
  String? get createdOn => _createdOn;
  set createdOn(String? createdOn) => _createdOn = createdOn;

  OthersFolders.fromJson(Map<String, dynamic> json) {
    _id = json['id'];
    _parentId = json['parent_id'];
    _folderName = json['folder_name'];
    _accId = json['acc_id'];
    _customerId = json['customer_id'];
    _createdOn = json['created_on'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this._id;
    data['parent_id'] = this._parentId;
    data['folder_name'] = this._folderName;
    data['acc_id'] = this._accId;
    data['customer_id'] = this._customerId;
    data['created_on'] = this._createdOn;
    return data;
  }
}
