import 'dart:io';

import 'package:erpcacustomer/activity/LoginActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:flutter/material.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:get/get.dart';

class ContactPersonFragment extends StatefulWidget {
  String? name;
  String? mobileNo;
  String? email;
  String? location;

  ContactPersonFragment(
      {Key? key, this.name, this.mobileNo, this.email, this.location})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    return ContactPersonFragmentState();
  }
}

class ContactPersonFragmentState extends State<ContactPersonFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var selectedIndexObs = 0.obs;
  var savingObs = false.obs;
  var imageObs = Rx<File?>(null);

  // Original variables (keeping for compatibility)
  String actualName = "";
  String actualEmail = "";
  String profilePic = "";
  String departmentName = "";
  String designationName = "";
  String dateOfBirth = "";
  DateTime? dateOfBirthDate;
  String actualContact = "";
  ImagePickerHandler? imagePicker;
  AnimationController? _controller;
  bool _saving = false;
  TabController? _tabController;
  int _selectedIndex = 0;
  File? _image;

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      inAsyncCall: _saving,
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 15, top: 30, right: 15),
            child: Column(children: <Widget>[
              Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Name",
                        style: new CSSStyle().poppinsGreyRegular12(context),
                      ),
                      Text(
                        widget.name.toString(),
                        style:
                            new CSSStyle().poppinsLightBlackRegular15(context),
                      ),
                    ],
                  ),
                  Text(
                    "Edit",
                    style: new CSSStyle().poppinsBlueLightBold15(context),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Mobile No",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Text(
                          widget.mobileNo.toString(),
                          style: new CSSStyle()
                              .poppinsLightBlackRegular15(context),
                        ),
                      ],
                    ),
                    Text(
                      "Edit",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
              /*      Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Name",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Text(
                          "Nikhil Mahajan",
                          style: new CSSStyle()
                              .poppinsLightBlackRegular15(context),
                        ),
                      ],
                    ),
                    Text(
                      "Edit",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),*/
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Email",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Text(
                          widget.email.toString(),
                          style: new CSSStyle()
                              .poppinsLightBlackRegular15(context),
                        ),
                      ],
                    ),
                    Text(
                      "Edit",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Location",
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                        Container(
                          width: 250,
                          child: Text(
                            widget.location.toString(),
                            style: new CSSStyle()
                                .poppinsLightBlackRegular15(context),
                          ),
                        ),
                      ],
                    ),
                    Text(
                      "Edit",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
            ]),
          ),
        ),
      ),
    );
  }

  _signoutMethodCall() async {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LoginActivity()),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
    _tabController = TabController(length: 3, vsync: this);
    _controller!.addListener(() {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedIndexObs.value = _tabController!.index;
      _selectedIndex = _tabController!.index; // Keep for compatibility
      // setState(() { _selectedIndex = _tabController!.index; }); // Removed

    });
  }

 _onLogoutPressed() {
    return showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text('Are you sure?'),
            content: new Text('Do you want to logout from App'),
            actions: <Widget>[
              new GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("NO"),
                ),
              ),
              SizedBox(height: 16),
              new GestureDetector(
                onTap: () {
                  _signoutMethodCall();

                  return Navigator.of(context).pop(true);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("YES"),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  _addForgotPasswordButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 0),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 10.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_blue_Color),
          Color(new CommonColor().oxygen_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'Reset Password',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginActivity(),
              ));
        },
        increaseWidthBy: 100.0,
        increaseHeightBy: 10.0,
      ),
    );
  }

  _addAccessMyAccountButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 20),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 40.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_dark_blue_Color),
          Color(new CommonColor().oxygen_dark_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'LOGOUT',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          // ✅ OPTIMIZED: GetX reactive update - NO setState needed!
          _onLogoutPressed();
          // setState(() { _onLogoutPressed(); }); // Removed
        },
        increaseWidthBy: 225.0,
        increaseHeightBy: 50.0,
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // ✅ OPTIMIZED: GetX reactive state management
      imageObs.value = _image;
      this._image = _image; // Keep for compatibility
      // setState(() { this._image = _image; }); // Removed
    }
  }

  DateTime convertDateFromString(String strDate) {
    DateTime todayDate = DateTime.parse(strDate);
    ;
    return todayDate;
  }
}
