import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsSecondFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsSecondFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsSecondFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsSecondFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsSecondFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsSecondFragment.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TaskListActivity extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return TaskListActivityState();
  }
}

class TaskListActivityState extends State<TaskListActivity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        actions: [
          IconButton(icon: Icon(Icons.notifications_none), onPressed: () {}),
        ],
        title: Text(
          "Documents",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/

      body: TaskListFragment(),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
