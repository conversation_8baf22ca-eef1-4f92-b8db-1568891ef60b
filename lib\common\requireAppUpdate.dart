import 'package:erpcacustomer/activity/SplashActivity.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
class RequiredAppUpdate extends StatefulWidget {
  @override
  State<RequiredAppUpdate> createState() => _RequiredAppUpdateState();
}

class _RequiredAppUpdateState extends State<RequiredAppUpdate> {
  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.of(context).size.height;
    var width = MediaQuery.of(context).size.width;
    return SafeArea(
      child: Scaffold(
        body: WillPopScope(
          onWillPop: () async{
            Navigator.push(context, MaterialPageRoute(builder: (context)=>SplashActivity()));
            return true;
          },
          child: Container(
            alignment: Alignment.center,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: 0.2 * width,),
                Container(
                  height: 0.2 * width,
                  width: 0.2 * width,
                  child: Image(
                    image: AssetImage('assets/images/update.png'),
                  ),
                ),
                SizedBox(height: 0.03 * width,),
                Container(
                  margin: EdgeInsets.only(top: 0.01 * width),
                  padding: EdgeInsets.all(0.1 * width),
                  child: Text('An update to newest version is available.',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 0.05 * width,
                 fontWeight: FontWeight.bold,
                 //  fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
                  ),
                  ),
                ),
                SizedBox(height: 0.05 * width,),
                InkWell(
                  onTap: () async{
                    var url = Constants.APP_URL;
                    if (await canLaunch(url)) {
                    await launch(url,
                    );
                    } else {
                    throw 'Could not launch $url';
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 0.02 * width),
                    padding: EdgeInsets.all(0.036 * width),
                    decoration: BoxDecoration(
                      color: Colors.green,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text('Update',
                    style: TextStyle(
                      fontSize: 0.05 * width,
                   color: Colors.white
                   //  fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
                    ),
                    ),
                  ),
                ),


              ],
            ),
          ),
        ),
      ),
    );
  }
}
