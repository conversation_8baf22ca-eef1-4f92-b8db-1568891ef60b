import 'dart:io';

import 'package:erpcacustomer/activity/LoginActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:flutter/material.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:get/get.dart';

class ServicesFragment extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return ServicesFragmentState();
  }
}

class ServicesFragmentState extends State<ServicesFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // GetX reactive variables
  var actualNameObs = ''.obs;
  var actualEmailObs = ''.obs;
  var profilePicObs = ''.obs;
  var departmentNameObs = ''.obs;
  var designationNameObs = ''.obs;
  var dateOfBirthObs = ''.obs;
  var actualContactObs = ''.obs;
  var isSavingObs = false.obs;
  var selectedIndexObs = 0.obs;

  // Original variables
  String actualName = "";
  String actualEmail = "";
  String profilePic = "";
  String departmentName = "";
  String designationName = "";
  String dateOfBirth = "";
  DateTime? dateOfBirthDate;
  String actualContact = "";
  ImagePickerHandler? imagePicker;
  AnimationController? _controller;
  bool _saving = false;
  TabController? _tabController;
  int _selectedIndex = 0;
  File? _image;

  @override
  Widget build(BuildContext context) {
    return Obx(() => ModalProgressHUD(
      inAsyncCall: isSavingObs.value == false ? _saving : isSavingObs.value,
      child: Scaffold(
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.only(left: 15, top: 30, right: 15),
            child: Column(children: <Widget>[
              Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "GST",
                        style:
                            new CSSStyle().poppinsLightBlackRegular17(context),
                      ),
                    ],
                  ),
                  Text(
                    "View",
                    style: new CSSStyle().poppinsBlueLightBold15(context),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Income Tax",
                          style: new CSSStyle()
                              .poppinsLightBlackRegular17(context),
                        ),
                      ],
                    ),
                    Text(
                      "View",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Service Tax",
                          style: new CSSStyle()
                              .poppinsLightBlackRegular17(context),
                        ),
                      ],
                    ),
                    Text(
                      "View",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 30),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "IEC",
                          style: new CSSStyle()
                              .poppinsLightBlackRegular17(context),
                        ),
                      ],
                    ),
                    Text(
                      "View",
                      style: new CSSStyle().poppinsBlueLightBold15(context),
                    )
                  ],
                ),
              ),
            ]),
          ),
        ),
      ),
    ));
  }

  _signoutMethodCall() async {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LoginActivity()),
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
    _tabController = TabController(length: 3, vsync: this);
    _controller!.addListener(() {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedIndexObs.value = _tabController!.index;
      _selectedIndex = _tabController!.index; // Keep for compatibility
      // setState(() { ... }); // Removed

    });
  }

   _onLogoutPressed() {
    return showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text('Are you sure?'),
            content: new Text('Do you want to logout from App'),
            actions: <Widget>[
              new GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("NO"),
                ),
              ),
              SizedBox(height: 16),
              new GestureDetector(
                onTap: () {
                  _signoutMethodCall();

                  return Navigator.of(context).pop(true);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("YES"),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  _addForgotPasswordButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 0),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 10.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_blue_Color),
          Color(new CommonColor().oxygen_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'Reset Password',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginActivity(),
              ));
        },
        increaseWidthBy: 100.0,
        increaseHeightBy: 10.0,
      ),
    );
  }

  _addAccessMyAccountButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 20),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 40.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_dark_blue_Color),
          Color(new CommonColor().oxygen_dark_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'LOGOUT',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          // GetX reactive update - NO setState needed!
          _onLogoutPressed();
          // GetX reactive state management for logout action
        },
        increaseWidthBy: 225.0,
        increaseHeightBy: 50.0,
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // GetX reactive update - NO setState needed!
      this._image = _image;
      // GetX reactive state management for image selection
      // profilePicObs.value = _image.path;
    }
  }

  DateTime convertDateFromString(String strDate) {
    DateTime todayDate = DateTime.parse(strDate);
    ;
    return todayDate;
  }
}
