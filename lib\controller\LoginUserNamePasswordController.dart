import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:erpcacustomer/model/customer_login_pass_model.dart';
import 'package:flutter/material.dart';

import '../common/Constants.dart';

Future<dynamic> validateUserNameApi(
    String userName,
    String userPassword,

    BuildContext context,
    ) async {

  try {
    Map<String, dynamic> requestData = {
      "uname": userName,
      "upwd": userPassword,
    };

    // ✅ API Call Logging
    log('[URL] : ${Constants.CUSTOMER_LOGIN_PASS_URL}\n[PARAMS] : ${json.encode(requestData)}');
    log("[URL] : ${Constants.CUSTOMER_LOGIN_PASS_URL}");

    Dio _dio = Dio();
    final response = await _dio.post(
      Constants.CUSTOMER_LOGIN_PASS_URL,
      data: json.encode(requestData),
      options: Options(
        headers: {
          "Id": "erpca",
          "Pass": "e736258681ac6d7126d298cc93a732db1dad2996",
          "Content-Type": "application/json",
        },
      ),
    );

    // ✅ API Response Logging
    log('[RESPONSE STATUS] : ${response.statusCode}');
    log('[RESPONSE BODY] : ${response.data}');

    int flag = response.data['success'];
    print('flag $flag ');
    CustomerLoginPassModel currentParsedResponse =
    CustomerLoginPassModel.fromJson(response.data);
    return currentParsedResponse;
  } on DioException catch (err) {  // ✅ FIXED: DioError renamed to DioException in dio 5.x
    final errorMessage = err;
    print('error msg $errorMessage');
    throw errorMessage;
  } catch (e) {
    print('e msg $e');
    throw e.toString();
  }
}