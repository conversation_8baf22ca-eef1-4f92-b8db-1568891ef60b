import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/CommonText.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/NotificationBadge.dart';
import 'package:erpcacustomer/model/push_notification.dart';
//import 'package:firebase_core/firebase_core.dart';
//import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:overlay_support/overlay_support.dart';
import 'package:sqflite/sqflite.dart';

import 'SetMPinActivity.dart';
import 'package:erpcacustomer/common/Constants.dart';

import 'package:erpcacustomer/controller/LoginController.dart';
import 'package:erpcacustomer/model/ValidateCustomerMobileModel.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'dart:convert';
import 'package:get/get.dart';

class SetOtpActivity extends StatefulWidget {
  String? mobile_Otp;
  String? mobileNumber;
  SetOtpActivity({Key? key, this.mobile_Otp, this.mobileNumber})
      : super(key: key);

  @override
  MPinActivityState createState() => MPinActivityState();
}

class MPinActivityState extends State<SetOtpActivity> {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var isLoadingObs = false.obs;
  var otpCodeObs = ''.obs;
  var myTokenObs = ''.obs;
  var savingObs = false.obs;
  var obscureTextObs = true.obs;
  var checkedValueObs = false.obs;
  var hasPinErrorObs = false.obs;
  var hasConfirmPinErrorObs = false.obs;

  // Original variables
  bool _obscureText = true;
  bool _saving = false;
  bool _savingForgotPassword = false;
  String myToken = '';
  var checkedValue = false;

  TextEditingController controller1 = new TextEditingController();
  TextEditingController controllerConfirm1 = new TextEditingController();
  TextEditingController controller2 = new TextEditingController();
  TextEditingController controllerConfirm2 = new TextEditingController();
  TextEditingController controller3 = new TextEditingController();
  TextEditingController controllerConfirm3 = new TextEditingController();
  TextEditingController controller4 = new TextEditingController();
  TextEditingController controllerConfirm4 = new TextEditingController();
  TextEditingController controller5 = new TextEditingController();
  TextEditingController controllerConfirm5 = new TextEditingController();
  TextEditingController currController = new TextEditingController();
  TextEditingController currConfirmController = new TextEditingController();

  var _form2Key = GlobalKey<FormState>();
  String userName = "";
  String password = "";
  //FirebaseMessaging firebaseMessaging = new FirebaseMessaging();
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      new FlutterLocalNotificationsPlugin();
  Map<String, String> headerHttp = new Map();
  bool hasPinError = false;
  bool hasConfirmPinError = false;

  // TextEditingController userNameController = TextEditingController();
  // TextEditingController passwordController = TextEditingController();
  //FirebaseApp firebaseApp;
  var _formKey = GlobalKey<FormState>();
  bool _isLoggedIn = false;
  String googleuserName = "";
  TextEditingController mPincontroller = TextEditingController();
  TextEditingController mConfirmPincontroller = TextEditingController();
  String mPin = "";
  String confirmMPin = "";
  String fcmId = "";
  PushNotification? _notificationInfo;
  int _totalNotifications = 0;

  update(String token) {
    // ✅ OPTIMIZED: GetX reactive state management
    myTokenObs.value = token;
    myToken = token; // Keep for compatibility
    // setState(() {}); // Removed - GetX handles reactive updates automatically
  }

  @override
  void dispose() {
    super.dispose();
    controller1.dispose();
    controllerConfirm1.dispose();
    controller2.dispose();
    controllerConfirm2.dispose();
    controller3.dispose();
    controllerConfirm3.dispose();
    controller4.dispose();
    controllerConfirm4.dispose();
    controller5.dispose();
    controllerConfirm5.dispose();
    // controller6.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    //  firebaseApp = FirebaseApp.instance;
    /* firebaseMessaging.getToken().then((token) {
      update(token);
    });
   */
    currController = controller1;
    currConfirmController = controllerConfirm1;
    _totalNotifications = 0;

    // For handling notification when the app is in background
    // but not terminated
  }


  void inputTextToField(String str) {
    //Edit first textField
    if (currController == controller1) {
      controller1.text = str;
      currController = controller2;
    }

    //Edit second textField
    else if (currController == controller2) {
      controller2.text = str;
      currController = controller3;
    }

    //Edit third textField
    else if (currController == controller3) {
      controller3.text = str;
      currController = controller4;
    }

    //Edit fourth textField
    else if (currController == controller4) {
      controller4.text = str;
      currController = controller5;
    }

    // ✅ OPTIMIZED: Update GetX reactive state for OTP tracking
    String currentOtp = controller1.text + controller2.text + controller3.text + controller4.text;
    otpCodeObs.value = currentOtp;
    mPin = currentOtp; // Keep for compatibility

    //Edit fifth textField
/*    else if (currController == controller5) {
      controller5.text = str;
      currController = controller6;
    }

    //Edit sixth textField
    else if (currController == controller6) {
      controller6.text = str;
      currController = controller6;
    }*/
  }

  void inputTextToFieldConfirm(String str) {
    //Edit first textField
    if (currController == controllerConfirm1) {
      controllerConfirm1.text = str;
      currConfirmController = controllerConfirm2;
    }

    //Edit second textField
    else if (currConfirmController == controllerConfirm2) {
      controllerConfirm2.text = str;
      currConfirmController = controllerConfirm3;
    }

    //Edit third textField
    else if (currConfirmController == controllerConfirm3) {
      controllerConfirm3.text = str;
      currConfirmController = controllerConfirm4;
    }

    //Edit fourth textField
    else if (currConfirmController == controllerConfirm4) {
      controllerConfirm4.text = str;
      currConfirmController = controllerConfirm5;
    }

    //Edit fifth textField
/*    else if (currController == controller5) {
      controller5.text = str;
      currController = controller6;
    }

    //Edit sixth textField
    else if (currController == controller6) {
      controller6.text = str;
      currController = controller6;
    }*/
  }

  void deleteText() {
    if (currController.text.length == 0) {
    } else {
      currController.text = "";
      currController = controller4;
      // ✅ OPTIMIZED: Update GetX reactive state after deletion
      String currentOtp = controller1.text + controller2.text + controller3.text + controller4.text;
      otpCodeObs.value = currentOtp;
      mPin = currentOtp; // Keep for compatibility
      return;
    }

    if (currController == controller1) {
      controller1.text = "";
    } else if (currController == controller2) {
      controller1.text = "";
      currController = controller1;
    } else if (currController == controller3) {
      controller2.text = "";
      currController = controller2;
    } else if (currController == controller4) {
      controller3.text = "";
      currController = controller3;
    } else if (currController == controller5) {
      controller4.text = "";
      currController = controller4;
    }

    // ✅ OPTIMIZED: Update GetX reactive state after deletion
    String currentOtp = controller1.text + controller2.text + controller3.text + controller4.text;
    otpCodeObs.value = currentOtp;
    mPin = currentOtp; // Keep for compatibility

    /* else if (currController == controller6) {
      controller5.text = "";
      currController = controller5;
    }*/
  }

  void deleteConfirmText() {
    if (currConfirmController.text.length == 0) {
    } else {
      currConfirmController.text = "";
      currConfirmController = controllerConfirm4;
      return;
    }

    if (currConfirmController == controllerConfirm1) {
      controllerConfirm1.text = "";
    } else if (currConfirmController == controllerConfirm2) {
      controllerConfirm1.text = "";
      currConfirmController = controllerConfirm1;
    } else if (currConfirmController == controllerConfirm3) {
      controllerConfirm2.text = "";
      currConfirmController = controllerConfirm2;
    } else if (currConfirmController == controllerConfirm4) {
      controllerConfirm3.text = "";
      currConfirmController = controllerConfirm3;
    } else if (currConfirmController == controllerConfirm5) {
      controllerConfirm4.text = "";
      currConfirmController = controllerConfirm4;
    }
    /* else if (currController == controller6) {
      controller5.text = "";
      currController = controller5;
    }*/
  }

  void verifyLogin(String accId, String mobileNo, String fcmId,
      String validate, BuildContext context) {
    {
      // ✅ OPTIMIZED: GetX reactive state management
      savingObs.value = true;
      isLoadingObs.value = true;
      _saving = true; // Keep for compatibility
      // setState(() { _saving = true; }); // Removed
      Future.delayed(Duration(seconds: 1), () async {
        var connectivityResult =
            await (Connectivity().checkConnectivity()); // User defined class
        if (connectivityResult == ConnectivityResult.mobile ||
            connectivityResult == ConnectivityResult.wifi) {
          var responseJson =
              await callLoginApi1(accId, mobileNo, fcmId, validate, context);

          int status = responseJson['success'];
          // ✅ OPTIMIZED: GetX reactive state management
          savingObs.value = false;
          isLoadingObs.value = false;
          _saving = false; // Keep for compatibility
          // setState(() { _saving = false; }); // Removed
          if (status != 1) {
            MyUtils.showOkDialog(
                context, "Error", responseJson['message'].toString());
          } else {
            int otp = responseJson['otp'];
            print('response json $otp');
            widget.mobile_Otp = otp.toString();
            ValidateCustomerMobileModel parsedResponse =
                ValidateCustomerMobileModel.fromJson(responseJson);

            MyUtils.showOkDialog(
                context, "Otp Sent", "Otp has been sent to your mobile number");
          }
        } else {
          // ✅ OPTIMIZED: GetX reactive state management
          savingObs.value = false;
          isLoadingObs.value = false;
          _saving = false; // Keep for compatibility
          // setState(() { _saving = false; }); // Removed
          MyUtils.showOkDialog(context, "No Internet", "Check your connection");
          // MyUtils.showToast("check your connection");
        }
      });
    }
  }

  String? _validateUserName(String? userName) {
    if (userName!.isEmpty) {
      return CommonText().user_name;
    } else {
      userName = userName;
    }
  }

  void verifyPin(String mpin, BuildContext context) {
    if (mpin.isEmpty) {
      MyUtils.showOkDialog(context, "Error", "Please Enter M-Pin");
    } else if (mpin.length != 4) {
      MyUtils.showOkDialog(context, "Error", "Please Enter Proper M-Pin");
    } else if (widget.mobile_Otp != mpin) {
      print("OTP ${widget.mobile_Otp}");
      MyUtils.showOkDialog(context, "Error", "Incorrect OTP");
    } else {
      // ✅ OPTIMIZED: GetX navigation (faster and more efficient)
      Get.to(() => SetMPinActivity());
      // Navigator.push(context, MaterialPageRoute(builder: (context) => SetMPinActivity())); // Removed
      //   Navigator.of(context).pushNamed('/SetConfirmMPinActivity');
    }
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> widgetList = [
      Padding(
        padding: EdgeInsets.only(left: 0.0, right: 2.0),
        child: new Container(
          color: Colors.transparent,
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
            height: 1,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              // border: Border(
              //   bottom: BorderSide(width: 1.0, color: Colors.black),
              // ),
            ),
            child: new TextField(
              inputFormatters: [
                LengthLimitingTextInputFormatter(1),
              ],
              enabled: false,
              controller: controller1,
              obscureText: true,
              autofocus: false,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 24.0, color: Colors.black),
            )),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(width: 1.0, color: Colors.black),
            // ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            controller: controller2,
            obscureText: true,
            autofocus: false,
            enabled: false,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 24.0, color: Colors.black),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(width: 1.0, color: Colors.black),
            // ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            keyboardType: TextInputType.number,
            controller: controller3,
            obscureText: true,
            textAlign: TextAlign.center,
            autofocus: false,
            enabled: false,
            style: TextStyle(fontSize: 24.0, color: Colors.black),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(width: 1.0, color: Colors.black),
            // ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            textAlign: TextAlign.center,
            controller: controller4,
            obscureText: true,
            autofocus: false,
            enabled: false,
            style: TextStyle(fontSize: 24.0, color: Colors.black),
          ),
        ),
      ),
    ];
    return WillPopScope(
  onWillPop: () async => false,
    child:  new Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Color(new CommonColor().red_Color),
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.black,
            ),
            onPressed: () {
              //moveToLastScreen();
            }),
        title: Text(
          "Verification Code",
          style: new CSSStyle().poppinsLightBlackRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().white_Color),
      ),
      body: Obx(() => ModalProgressHUD(
        inAsyncCall: savingObs.value || isLoadingObs.value,  // ✅ OPTIMIZED: GetX reactive variables
        child: Stack(
          children: [
            Image.asset(
              'assets/images/login_background.png',
              width: double.infinity,
              fit: BoxFit.fill,
            ),
            Padding(
              padding: const EdgeInsets.only(top: 50.0),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Image.asset('assets/images/otp.png',
                            width: 100.0, height: 100.0)
                      ]),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.only(left: 15, right: 15),
                        child: Text(
                          "Enter the OTP we have sent you on your mobile number " +
                              widget.mobileNumber.toString(),
                          textAlign: TextAlign.center,
                          style: new CSSStyle()
                              .poppinsLighterBlackRegular16(context),
                        ),
                      ),
                      GridView.count(
                          crossAxisCount: 6,
                          mainAxisSpacing: 10.0,
                          shrinkWrap: true,
                          primary: true,
                          scrollDirection: Axis.vertical,
                          children: List<Container>.generate(
                              5,
                              (int index) =>
                                  Container(child: widgetList[index]))),
                      new Container(
                        child: Padding(
                          padding:
                              const EdgeInsets.only(top: 30.0, bottom: 0.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("1");
                                },
                                child: Text("1",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("2");
                                },
                                child: Text("2",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("3");
                                },
                                child: Text("3",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                            ],
                          ),
                        ),
                      ),
                      new Container(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8.0, top: 4.0, right: 8.0, bottom: 0.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("4");
                                },
                                child: Text("4",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("5");
                                },
                                child: Text("5",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("6");
                                },
                                child: Text("6",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                            ],
                          ),
                        ),
                      ),
                      new Container(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8.0, top: 4.0, right: 8.0, bottom: 0.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("7");
                                },
                                child: Text("7",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("8");
                                },
                                child: Text("8",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("9");
                                },
                                child: Text("9",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                            ],
                          ),
                        ),
                      ),
                      new Container(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 8.0, top: 4.0, right: 8.0, bottom: 0.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              MaterialButton(
                                  onPressed: () {
                                    deleteText();
                                  },
                                  child: Image.asset('assets/images/delete.png',
                                      width: 25.0, height: 25.0)),
                              MaterialButton(
                                onPressed: () {
                                  inputTextToField("0");
                                },
                                child: Text("0",
                                    style: TextStyle(
                                        color: Colors.black,
                                        fontSize: 25.0,
                                        fontWeight: FontWeight.w400),
                                    textAlign: TextAlign.center),
                              ),
                              MaterialButton(
                                  onPressed: () {
                                    mPin = controller1.text +
                                        controller2.text +
                                        controller3.text +
                                        controller4.text;
                                    verifyPin(mPin, context);
                                  },
                                  child: Image.asset(
                                      'assets/images/success.png',
                                      width: 25.0,
                                      height: 25.0)),
                            ],
                          ),
                        ),
                      ),
                      GestureDetector(
                        onTap: () {
                          verifyLogin(Constants.ACC_ID, widget.mobileNumber.toString(),
                              fcmId, "1", context);
                        },
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 15, right: 15, top: 15),
                          child: Text(
                            "Resend OTP",
                            textAlign: TextAlign.center,
                            style: new CSSStyle()
                                .poppinsOrangeRegularUnderline15(context),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    ),
    ));
  }

  void moveToLastScreen() {
    // ✅ OPTIMIZED: GetX navigation (faster and more efficient)
    Get.back(result: true);
    // Navigator.pop(context, true); // Removed
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
