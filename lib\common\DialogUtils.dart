import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:get/get.dart';

import 'package:erpcacustomer/activity/NewServicesActivity.dart';
import 'package:erpcacustomer/activity/GstActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/controller/TaskController.dart';
// ignore: unused_import
import "package:erpcacustomer/model/TaskListModel.dart";
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:table_calendar/table_calendar.dart';
import "package:percent_indicator/linear_percent_indicator.dart";
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

class DialogUtils {
  static DialogUtils _instance = new DialogUtils.internal();

  DialogUtils.internal();

  factory DialogUtils() => _instance;

  static void showCustomDialog(BuildContext context,
      {required String title,
      String? imagePic,
      String? userName,
      String? userType,
      String? details,
      String? emailId,
      String? mobileNo,
      required Function okBtnFunction}) {
    showDialog(
        barrierDismissible: true,
        context: context,
        builder: (_) {
          return AlertDialog(
            contentPadding: EdgeInsets.zero,
            insetPadding: EdgeInsets.zero,
            content: Container(
              height: 300,
              child: Column(
                children: [
                  Align(
                      alignment: Alignment.topRight,
                      child: IconButton(
                          icon: Icon(Icons.clear),
                          onPressed: () {
                            Navigator.of(context, rootNavigator: true).pop();
                          })),
                  (imagePic != null)
                      ? Container(
                          height: 80,
                          width: 80,
                          decoration: new BoxDecoration(
                              shape: BoxShape.circle,
                              image: new DecorationImage(
                                  fit: BoxFit.fill,
                                  image: new NetworkImage(imagePic))),
                        )
                      : Image.asset('assets/images/user.png',
                          width: 80.0, height: 80.0),
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0, top: 10),
                    child: (userName != null)
                        ? Text(
                            userName,
                            style: new CSSStyle().poppinsGreyRegular12(context),
                          )
                        : Text(
                            "N/A",
                            style: new CSSStyle().poppinsGreyRegular12(context),
                          ),
                  ),
                  Text(
                    userType.toString(),
                    style: new CSSStyle().poppinsLightGreyRegular12(context),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        left: 8.0, right: 8, top: 15, bottom: 8),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            (emailId != null)
                                ? Container(
                                    width: 280,
                                    child: Text(
                                      emailId,
                                      style: new CSSStyle()
                                          .poppinsGreyRegular15(context),
                                      textAlign: TextAlign.start,
                                    ),
                                  )
                                : Container(
                                    width: 280,
                                    child: Text(
                                      "NA",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular15(context),
                                      textAlign: TextAlign.start,
                                    ),
                                  ),
                            Row(
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    new MyUtils()
                                        .sendMail(emailId.toString(), "", "", context);
                                  },
                                  child: Icon(
                                    Icons.attach_email,
                                    color: Color(
                                        new CommonColor().green_light_Color),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                        Divider(),
                        Row(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            (mobileNo != null)
                                ? Text(
                                    "+91 " + mobileNo,
                                    style: new CSSStyle()
                                        .poppinsGreyRegular15(context),
                                    textAlign: TextAlign.start,
                                  )
                                : Text(
                                    "N/A",
                                    style: new CSSStyle()
                                        .poppinsGreyRegular15(context),
                                    textAlign: TextAlign.start,
                                  ),
                            Row(
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    new MyUtils().callMe(mobileNo.toString(), context);
                                  },
                                  child: Icon(
                                    Icons.local_phone_outlined,
                                    size: 25,
                                    color: Color(
                                        new CommonColor().green_light_Color),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 5.0),
                                  child: GestureDetector(
                                    onTap: () {
                                      new MyUtils()
                                          .launchWhatsApp("+91 " + mobileNo.toString());
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.all(2.0),
                                      child: GestureDetector(
                                        onTap: () {
                                          new MyUtils().launchWhatsApp(
                                              "+91 " + mobileNo.toString());
                                        },
                                        child: FaIcon(
                                          FontAwesomeIcons.whatsapp,
                                          size: 20,
                                          color: Colors.green[500],
                                        ),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }
}
