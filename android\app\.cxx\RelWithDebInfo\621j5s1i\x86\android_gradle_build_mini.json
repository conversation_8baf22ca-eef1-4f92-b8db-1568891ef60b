{"buildFiles": ["C:\\Users\\<USER>\\flutter\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AndroidStudioProjects\\ERPC\\erpcacustomerapp\\android\\app\\.cxx\\RelWithDebInfo\\621j5s1i\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\AndroidStudioProjects\\ERPC\\erpcacustomerapp\\android\\app\\.cxx\\RelWithDebInfo\\621j5s1i\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}