class CustomerTaskDetailsModel {
  int? _status;
  String? _message;
  int? _success;
  Data? _data;
  String? _errorDev;

  CustomerTaskDetailsModel(
      {int? status,
        String? message,
        int? success,
        Data? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  Data? get data => _data;
  set data(Data? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CustomerTaskDetailsModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _data = json['data'] != null ? new Data.fromJson(json['data']) : null;
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.toJson();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  List<Task>? _task;
  List<WorkingTeam>? _workingTeam;
  List<Communication>? _communication;

  Data(
      {List<Task>? task,
        List<WorkingTeam>? workingTeam,
        List<Communication>? communication}) {
    if (task != null) {
      this._task = task;
    }
    if (workingTeam != null) {
      this._workingTeam = workingTeam;
    }
    if (communication != null) {
      this._communication = communication;
    }
  }

  List<Task>? get task => _task;
  set task(List<Task>? task) => _task = task;
  List<WorkingTeam>? get workingTeam => _workingTeam;
  set workingTeam(List<WorkingTeam>? workingTeam) => _workingTeam = workingTeam;
  List<Communication>? get communication => _communication;
  set communication(List<Communication>? communication) =>
      _communication = communication;

  Data.fromJson(Map<String, dynamic> json) {
    if (json['task'] != null) {
      _task = <Task>[];
      json['task'].forEach((v) {
        _task!.add(new Task.fromJson(v));
      });
    }
    if (json['working_team'] != null) {
      _workingTeam = <WorkingTeam>[];
      json['working_team'].forEach((v) {
        _workingTeam!.add(new WorkingTeam.fromJson(v));
      });
    }
    if (json['communication'] != null) {
      _communication = <Communication>[];
      json['communication'].forEach((v) {
        _communication!.add(new Communication.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this._task != null) {
      data['task'] = this._task!.map((v) => v.toJson()).toList();
    }
    if (this._workingTeam != null) {
      data['working_team'] = this._workingTeam!.map((v) => v.toJson()).toList();
    }
    if (this._communication != null) {
      data['communication'] =
          this._communication!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Task {
  String? _taskId;
  String? _taskTitle;
  String? _startDate;
  String? _endDate;
  String? _serviceType;
  String? _tStatus;
  String? _totalPercent;
  String? _asessmentPeriod;
  String? _workingResource;
  String? _reviewerName;
  String? _fees;
  String? _spentHrs;
  String? _dataDependency;

  Task(
      {String? taskId,
        String? taskTitle,
        String? startDate,
        String? endDate,
        String? serviceType,
        String? tStatus,
        String? totalPercent,
        String? asessmentPeriod,
        String? workingResource,
        String? reviewerName,
        String? fees,
        String? spentHrs,
        String? dataDependency}) {
    if (taskId != null) {
      this._taskId = taskId;
    }
    if (taskTitle != null) {
      this._taskTitle = taskTitle;
    }
    if (startDate != null) {
      this._startDate = startDate;
    }
    if (endDate != null) {
      this._endDate = endDate;
    }
    if (serviceType != null) {
      this._serviceType = serviceType;
    }
    if (tStatus != null) {
      this._tStatus = tStatus;
    }
    if (totalPercent != null) {
      this._totalPercent = totalPercent;
    }
    if (asessmentPeriod != null) {
      this._asessmentPeriod = asessmentPeriod;
    }
    if (workingResource != null) {
      this._workingResource = workingResource;
    }
    if (reviewerName != null) {
      this._reviewerName = reviewerName;
    }
    if (fees != null) {
      this._fees = fees;
    }
    if (spentHrs != null) {
      this._spentHrs = spentHrs;
    }
    if (dataDependency != null) {
      this._dataDependency = dataDependency;
    }
  }

  String? get taskId => _taskId;
  set taskId(String? taskId) => _taskId = taskId;
  String? get taskTitle => _taskTitle;
  set taskTitle(String? taskTitle) => _taskTitle = taskTitle;
  String? get startDate => _startDate;
  set startDate(String? startDate) => _startDate = startDate;
  String? get endDate => _endDate;
  set endDate(String? endDate) => _endDate = endDate;
  String? get serviceType => _serviceType;
  set serviceType(String? serviceType) => _serviceType = serviceType;
  String? get tStatus => _tStatus;
  set tStatus(String? tStatus) => _tStatus = tStatus;
  String? get totalPercent => _totalPercent;
  set totalPercent(String? totalPercent) => _totalPercent = totalPercent;
  String? get asessmentPeriod => _asessmentPeriod;
  set asessmentPeriod(String? asessmentPeriod) =>
      _asessmentPeriod = asessmentPeriod;
  String? get workingResource => _workingResource;
  set workingResource(String? workingResource) =>
      _workingResource = workingResource;
  String? get reviewerName => _reviewerName;
  set reviewerName(String? reviewerName) => _reviewerName = reviewerName;
  String? get fees => _fees;
  set fees(String? fees) => _fees = fees;
  String? get spentHrs => _spentHrs;
  set spentHrs(String? spentHrs) => _spentHrs = spentHrs;
  String? get dataDependency => _dataDependency;
  set dataDependency(String? dataDependency) =>
      _dataDependency = dataDependency;

  Task.fromJson(Map<String, dynamic> json) {
    _taskId = json['task_id'];
    _taskTitle = json['task_title'];
    _startDate = json['start_date'];
    _endDate = json['end_date'];
    _serviceType = json['service_type'];
    _tStatus = json['t_status'];
    _totalPercent = json['total_percent'];
    _asessmentPeriod = json['asessment_period'];
    _workingResource = json['working_resource'];
    _reviewerName = json['reviewer_name'];
    _fees = json['fees'];
    _spentHrs = json['spent_hrs'];
    _dataDependency = json['data_dependency'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['task_id'] = this._taskId;
    data['task_title'] = this._taskTitle;
    data['start_date'] = this._startDate;
    data['end_date'] = this._endDate;
    data['service_type'] = this._serviceType;
    data['t_status'] = this._tStatus;
    data['total_percent'] = this._totalPercent;
    data['asessment_period'] = this._asessmentPeriod;
    data['working_resource'] = this._workingResource;
    data['reviewer_name'] = this._reviewerName;
    data['fees'] = this._fees;
    data['spent_hrs'] = this._spentHrs;
    data['data_dependency'] = this._dataDependency;
    return data;
  }
}

class WorkingTeam {
  String? _userId;
  String? _userName;
  String? _email;
  String? _mobileNo;
  String? _dpPic;
  String? _userType;

  WorkingTeam(
      {String? userId,
        String? userName,
        String? email,
        String? mobileNo,
        String? dpPic,
        String? userType}) {
    if (userId != null) {
      this._userId = userId;
    }
    if (userName != null) {
      this._userName = userName;
    }
    if (email != null) {
      this._email = email;
    }
    if (mobileNo != null) {
      this._mobileNo = mobileNo;
    }
    if (dpPic != null) {
      this._dpPic = dpPic;
    }
    if (userType != null) {
      this._userType = userType;
    }
  }

  String? get userId => _userId;
  set userId(String? userId) => _userId = userId;
  String? get userName => _userName;
  set userName(String? userName) => _userName = userName;
  String? get email => _email;
  set email(String? email) => _email = email;
  String? get mobileNo => _mobileNo;
  set mobileNo(String? mobileNo) => _mobileNo = mobileNo;
  String? get dpPic => _dpPic;
  set dpPic(String? dpPic) => _dpPic = dpPic;
  String? get userType => _userType;
  set userType(String? userType) => _userType = userType;

  WorkingTeam.fromJson(Map<String, dynamic> json) {
    _userId = json['user_id'];
    _userName = json['user_name'];
    _email = json['email'];
    _mobileNo = json['mobile_no'];
    _dpPic = json['dp_pic'];
    _userType = json['user_type'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this._userId;
    data['user_name'] = this._userName;
    data['email'] = this._email;
    data['mobile_no'] = this._mobileNo;
    data['dp_pic'] = this._dpPic;
    data['user_type'] = this._userType;
    return data;
  }
}

class Communication {
  String? _mailId;
  String? _title;
  String? _message;
  String? _attachFile;
  String? _ccEmail;
  String? _bccEmail;
  String? _createdOn;
  String? _sentBy;

  Communication(
      {String? mailId,
        String? title,
        String? message,
        String? attachFile,
        String? ccEmail,
        String? bccEmail,
        String? createdOn,
        String? sentBy}) {
    if (mailId != null) {
      this._mailId = mailId;
    }
    if (title != null) {
      this._title = title;
    }
    if (message != null) {
      this._message = message;
    }
    if (attachFile != null) {
      this._attachFile = attachFile;
    }
    if (ccEmail != null) {
      this._ccEmail = ccEmail;
    }
    if (bccEmail != null) {
      this._bccEmail = bccEmail;
    }
    if (createdOn != null) {
      this._createdOn = createdOn;
    }
    if (sentBy != null) {
      this._sentBy = sentBy;
    }
  }

  String? get mailId => _mailId;
  set mailId(String? mailId) => _mailId = mailId;
  String? get title => _title;
  set title(String? title) => _title = title;
  String? get message => _message;
  set message(String? message) => _message = message;
  String? get attachFile => _attachFile;
  set attachFile(String? attachFile) => _attachFile = attachFile;
  String? get ccEmail => _ccEmail;
  set ccEmail(String? ccEmail) => _ccEmail = ccEmail;
  String? get bccEmail => _bccEmail;
  set bccEmail(String? bccEmail) => _bccEmail = bccEmail;
  String? get createdOn => _createdOn;
  set createdOn(String? createdOn) => _createdOn = createdOn;
  String? get sentBy => _sentBy;
  set sentBy(String? sentBy) => _sentBy = sentBy;

  Communication.fromJson(Map<String, dynamic> json) {
    _mailId = json['mail_id'];
    _title = json['title'];
    _message = json['message'];
    _attachFile = json['attach_file'];
    _ccEmail = json['cc_email'];
    _bccEmail = json['bcc_email'];
    _createdOn = json['created_on'];
    _sentBy = json['sent_by'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['mail_id'] = this._mailId;
    data['title'] = this._title;
    data['message'] = this._message;
    data['attach_file'] = this._attachFile;
    data['cc_email'] = this._ccEmail;
    data['bcc_email'] = this._bccEmail;
    data['created_on'] = this._createdOn;
    data['sent_by'] = this._sentBy;
    return data;
  }
}

