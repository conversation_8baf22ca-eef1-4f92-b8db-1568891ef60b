import 'package:erpcacustomer/activity/LoginWithMobileNumberActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/CommonText.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_number/mobile_number.dart';
import 'package:get/get.dart';
import 'package:mobile_number/sim_card.dart';

class ChooseMobileNumberActivity extends StatefulWidget {
  ChooseMobileNumberActivity({Key? key}) : super(key: key);

  @override
  ChooseMobileNumberActivityState createState() {
    return new ChooseMobileNumberActivityState();
  }
}

class ChooseMobileNumberActivityState
    extends State<ChooseMobileNumberActivity> {
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  bool _saving = false;
  String myToken = "";
  String userName = "";
  String password = "";
  var checkedValue = false;
  bool _obscureText = true;
  bool selected = true;
  String _mobileNumber = '';
  List<SimCard> _simCard = <SimCard>[];
  int _radioValue1 = -1;
  String mobileNo = "";

  // ✅ OPTIMIZED: GetX reactive variables for state management
  var mobileNumberObs = ''.obs;
  var isLoadingObs = false.obs;
  var checkedValueObs = false.obs;
  var obscureTextObs = true.obs;
  var selectedObs = true.obs;
  var radioValue1Obs = (-1).obs;
  var mobileNoObs = ''.obs;
  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  var _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    MobileNumber.listenPhonePermission((isPermissionGranted) {
      if (isPermissionGranted) {
        initMobileNumberState();
      } else {}
    });

    initMobileNumberState();
  }

  // Platform messages are asynchronous, so we initialize in an async method.
  Future<void> initMobileNumberState() async {
    if (!await MobileNumber.hasPhonePermission) {
      await MobileNumber.requestPhonePermission;
      return;
    }
    String mobileNumber = '';
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      mobileNumber = (await MobileNumber.mobileNumber)!;
      _simCard = (await MobileNumber.getSimCards)!;
    } on PlatformException catch (e) {

    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) return;

    // GetX reactive update - NO setState needed!
    _mobileNumber = mobileNumber;
    mobileNumberObs.value = mobileNumber;  // GetX automatically updates UI
  }

  @override
  Widget build(BuildContext context) {

    _media = MediaQuery.of(context).size;

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: PrimaryColor,
        fontFamily: 'GlacialIndifference',
      ),
      home: Scaffold(
        appBar: AppBar(
          elevation: 0,
          /*  leading: IconButton(
              icon: Icon(Icons.arrow_back),
              onPressed: () {
                moveToLastScreen();
              }),*/
          title: Center(
            child: Text(
              "SIM Verification",
              style: new CSSStyle().poppinsLightBlackRegular16(context),
            ),
          ),
          backgroundColor: Color(new CommonColor().white_Color),
        ),
        body: _buildBackground(),
      ),
    );
  }

  Widget fillCards() {
    /*_simCard
        .map((SimCard sim) {
        })
        .toList();*/


    return Padding(
      padding: const EdgeInsets.only(bottom: 60.0),
      child: Container(
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Image(
              width: 100.0,
              height: 100.0,
              image: AssetImage('assets/images/security.png'),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 5.0, left: 20, right: 20),
              child: Text(
                "We detected below mobile number(s) in your mobile. Please choose/enter the number registered with us to continue",
                textAlign: TextAlign.center,
                style: new CSSStyle().poppinsLighterBlackRegular16(context),
              ),
            ),
            /*  Padding(
              padding: const EdgeInsets.only(top: 30.0),
              child: Text(
                "SIM Detected",
                style: new CSSStyle().poppinsLightBlackRegular20(context),
              ),
            ),*/
            if (_simCard.length > 0)
              if (_simCard[0].number != null)
                Padding(
                  padding: const EdgeInsets.only(
                    top: 20.0,
                    right: 8,
                    left: 8,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Image(
                        width: 40.0,
                        height: 40.0,
                        image: AssetImage('assets/images/simcard.png'),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(15.0),
                        child: new Text(
                          _simCard[0].number.toString(),
                          style: new CSSStyle()
                              .poppinsLighterBlackRegular16(context),
                        ),
                      ),
                      Radio(
                        value: 0,
                        groupValue: _radioValue1,
                        onChanged: _handleRadioValueChange1,
                      ),
                    ],
                  ),
                ),
            Padding(
              padding: const EdgeInsets.only(left: 8.0, right: 8),
              child: Divider(
                color: Color(new CommonColor().lighter_grey_Color),
              ),
            ),
            if (_simCard.length > 1)
              if (_simCard[1].number != null)
                Padding(
                  padding: const EdgeInsets.only(
                    right: 8,
                    left: 8,
                  ),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Image(
                        width: 40.0,
                        height: 40.0,
                        image: AssetImage('assets/images/simcard.png'),
                      ),
                      Padding(
                        padding: const EdgeInsets.all(15.0),
                        child: new Text(
                          _simCard[1].number.toString(),
                          style: new CSSStyle()
                              .poppinsLighterBlackRegular16(context),
                        ),
                      ),
                      Radio(
                        value: 1,
                        groupValue: _radioValue1,
                        onChanged: _handleRadioValueChange1,
                      ),
                    ],
                  ),
                ),
            if (_simCard.length > 1)
              Padding(
                padding: const EdgeInsets.only(left: 8.0, right: 8),
                child: Divider(
                  color: Color(new CommonColor().lighter_grey_Color),
                ),
              ),
            Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Image(
                    width: 40.0,
                    height: 40.0,
                    image: AssetImage('assets/images/simcard.png'),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(15.0),
                    child: new Text(
                      "   Others    ",
                      style:
                          new CSSStyle().poppinsLighterBlackRegular16(context),
                    ),
                  ),
                  Radio(
                    value: 2,
                    groupValue: _radioValue1,
                    onChanged: _handleRadioValueChange1,
                  ),
                ],
              ),
            ),
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => LoginWithMobileNumberActivity(
                            mobileNo: mobileNo,
                          )),
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(top: 20.0),
                child: Image(
                  width: 60.0,
                  height: 60.0,
                  image: AssetImage('assets/images/frontarrow.png'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handleRadioValueChange1(int? value) {  // ✅ FIXED: Changed return type to void
    // ✅ OPTIMIZED: GetX reactive state management
    radioValue1Obs.value = value!;
    _radioValue1 = value; // Keep for compatibility

    switch (_radioValue1) {
      case 0:
        mobileNoObs.value = _simCard[0].number!;
        mobileNo = _simCard[0].number!; // Keep for compatibility
          break;
        case 1:
          mobileNoObs.value = _simCard[1].number!;
          mobileNo = _simCard[1].number!; // Keep for compatibility

          break;
        case 2:
          mobileNoObs.value = "";
          mobileNo = ""; // Keep for compatibility

          break;
      }
  }

  Widget _buildBackground() {
    return Container(
      width: double.infinity,
      child: Stack(
        children: [
          Image.asset(
            'assets/images/login_background.png',
            width: double.infinity,
            fit: BoxFit.fill,
          ),
          Form(
            key: _formKey,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12.0),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  fillCards(),
                  // _brandingUi(),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  _addOrUi() {
    return Container(
      margin: const EdgeInsets.only(top: 15.0, left: 00, right: 20),
      child: Row(children: <Widget>[
        Expanded(
          child: new Container(
              margin: const EdgeInsets.only(left: 20.0, right: 10.0),
              child: Divider(
                color: Colors.grey,
                height: 30,
              )),
        ),
        Text(
          "OR",
          style: new CSSStyle().poppinsGreyRegular15(context),
        ),
        Expanded(
          child: new Container(
              margin: const EdgeInsets.only(left: 10.0, right: 0.0),
              child: Divider(
                color: Colors.grey,
                height: 30,
              )),
        ),
      ]),
    );
  }

  void _showAlertDialog(String title, String message) {
    AlertDialog alertDialog = AlertDialog(
      title: Text(title),
      content: Text(message),
    );
    showDialog(context: context, builder: (_) => alertDialog);
  }

  void _signInApiCall() async {
    /*await databaseHelper.deleteAllUser();
    var noteMapList = await databaseHelper.getUserMapList();
    if (noteMapList.length <= 0) {
      int result = await databaseHelper.insertUser(userModel);
      if (result != 0) {
        handleTimeout();
      } else {
        MyUtils.showOkDialog(context, "Error", responseJson['emsg'].toString());
      }
    }*/

    //new PreferenceManagerUtil().setIsLogin(true);
  }

  String? _validatePassword(String? password) {
    if (password!.isEmpty) {
      return CommonText().password;
    } else if (password.length < 6) {
      return CommonText().valid_password;
    } else {
      this.password = password;
    }
  }

  String? _validateUserName(String? userName) {
    if (userName!.isEmpty) {
      return CommonText().user_name;
    } else {
      userName = userName;
    }
  }

/*
  void verifyLogin(String userName, String password, BuildContext context) {
    if (userName.isEmpty) {
      _showAlertDialog("ERROR", "Enter Username");
    } else if (password.isEmpty) {
      _showAlertDialog("ERROR", "Enter Password");
    } else {
      // ✅ OPTIMIZED: GetX reactive state management
      isLoadingObs.value = true;
      _saving = true; // Keep for compatibility
      // setState(() { _saving = true; }); // Removed
      Future.delayed(Duration(seconds: 1), () async {
        //   bool isTrustFall = await TrustFall.isJailBroken;

        var connectivityResult =
            await (Connectivity().checkConnectivity()); // User defined class
        //if (!isTrustFall) {
        if (connectivityResult == ConnectivityResult.mobile ||
            connectivityResult == ConnectivityResult.wifi) {
          var responseJson = await callLoginApi(
              */
/*  userName,
              new MyUtils().generateSignatureExtraCharacter(password),*/ /*

              "4101200300000073-00",
              "156387eef71097d807fca7db18d3922bb6bd0d132bf522c4a88978241112fd518461775985",
              myToken,
              context);
          // ✅ OPTIMIZED: GetX reactive state management
          isLoadingObs.value = false;
          _saving = false; // Keep for compatibility
          // setState(() { _saving = false; }); // Removed
          String isError = responseJson['isError'].toString();

          if (isError == "1") {
            MyUtils.showOkDialog(
                context, "Error", responseJson['message'].toString());
          } else {
            LoginModel parsedResponse = LoginModel.fromJson(responseJson);
            new PreferenceManagerUtil()
                .setCorporateId(parsedResponse.result.corporateId);
            new PreferenceManagerUtil().setProposerDisplayName(
                parsedResponse.result.proposerDisplayName);
            new PreferenceManagerUtil()
                .setTokenId(parsedResponse.result.tokenID);
            new PreferenceManagerUtil()
                .setProposerPriPhone(parsedResponse.result.proposerPriPhone);
            new PreferenceManagerUtil()
                .setPolicyType(parsedResponse.result.policyType);
            new PreferenceManagerUtil()
                .setProposerPriMobile(parsedResponse.result.proposerPriMobile);
            new PreferenceManagerUtil()
                .setProposerPriEmail(parsedResponse.result.proposerPriEmail);
            new PreferenceManagerUtil()
                .setPolicyCode(parsedResponse.result.policyCode);
          */
/*  Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => DashBoardScreen()),
            );*/ /*

          }
        } else {
          // ✅ OPTIMIZED: GetX reactive state management
          isLoadingObs.value = false;
          _saving = false; // Keep for compatibility
          // setState(() { _saving = false; }); // Removed

          MyUtils.showOkDialog(context, "No Internet", "Check your connection");
          // MyUtils.showToast("check your connection");
        }

        */
/*      } else {
          // ✅ OPTIMIZED: GetX reactive state management
          isLoadingObs.value = false;
          _saving = false; // Keep for compatibility
          // setState(() { _saving = false; }); // Removed
          MyUtils.showOkDialog(
              context, "Sorry", "This Device is not secure");

        }*/ /*

      });
    }
  }
*/

  _addPasswordUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: TextFormField(
        keyboardType: TextInputType.text,
        //key: passKey,

        obscureText: _obscureText,

        validator: _validatePassword,
        //   controller: passwordController,
        style: new CSSStyle().ptSansBlackRegular15(context),
        //validator: _validatePassword,
        decoration: InputDecoration(
          labelText: 'Password',
          hintText: 'Minimum 6 character',
          labelStyle: new CSSStyle().poppinsGreyRegular15(context),
          errorStyle: TextStyle(color: Colors.red, fontSize: 15),
          suffixIcon: Padding(
            padding: EdgeInsets.all(0.0),
            child: IconButton(
              onPressed: _toggle,
              icon: (_obscureText == false)
                  ? Icon(Icons.remove_red_eye)
                  : Icon(Icons.visibility_off),
              iconSize: 25,
              color: Colors.black54,
            ),
          ),
        ),
        cursorColor: Colors.black,
      ),
    );
  }

  _addForgotPasswordUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              InkWell(
                child: Text(
                  "Forgot Password?",
                  style: new CSSStyle().poppinsblueTextRegular12(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _addLoginWithOtpUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 8),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              InkWell(
                child: Text(
                  "Login with OTP",
                  style: new CSSStyle().poppinsblueTextRegular14(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _addRegistrationUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                "New to ERPCA ? ",
                style: new CSSStyle().ptSansBlackRegular16(context),
              ),
              InkWell(
                child: Text(
                  "Activate account",
                  style: new CSSStyle().poppinsblueTextRegular14(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _brandingUi() {
    String assetNameMale = 'images/eoxegen_logo.svg';

    return Padding(
      padding: const EdgeInsets.only(top: 15.0, bottom: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Column(
            children: [
              Text(
                "",
                style: new CSSStyle().poppinsGreyRegular10(context),
              ),
              Text(
                "",
                style: new CSSStyle().poppinsGreyRegular10(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _addRememberMeUi() {
    return Expanded(
      flex: 1,
      child: Container(
        margin: EdgeInsets.fromLTRB(15, 0, 0, 0),
        child: Row(
          children: [
            Obx(() => Checkbox(
                value: selectedObs.value,  // ✅ OPTIMIZED: GetX reactive variable
                checkColor: Colors.white,
                activeColor: Colors.green,
                onChanged: (val) {
                  // ✅ OPTIMIZED: GetX reactive state management
                  selectedObs.value = !selectedObs.value;
                  selected = selectedObs.value; // Keep for compatibility
                  // this.setState(() { this.selected = !this.selected; }); // Removed
                })),
            Text(
              "Remember me ",
              style: new CSSStyle().poppinsGreyRegular10(context),
            )
          ],
        ),
      ),
    );
  }

  void _toggle() {
    // ✅ OPTIMIZED: GetX reactive state management
    obscureTextObs.value = !obscureTextObs.value;
    _obscureText = obscureTextObs.value; // Keep for compatibility
    // setState(() { _obscureText = !_obscureText; }); // Removed
  }
}
