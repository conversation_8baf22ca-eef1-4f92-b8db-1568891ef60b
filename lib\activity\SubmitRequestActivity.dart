import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/MobileInfoModel.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:get/get.dart';
import '../utils/navigation_helper.dart';

class SubmitRequestActivity extends StatefulWidget {
  String? service_request_id;

  SubmitRequestActivity({Key? key, this.service_request_id}) : super(key: key);
  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return SubmitRequestActivityState();
  }
}

class SubmitRequestActivityState extends State<SubmitRequestActivity> {
  // GetX reactive variables
  var selectPhoneNumberObs = ''.obs;
  var selectWhatsAppNumberObs = ''.obs;
  var selectEmailObs = ''.obs;

  // Original variables
  String selectPhoneNumber = "";
  String selectWhatsAppNumber = "";
  String selectEmail = "";

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Future<String> tokenPhoneNumber =
        new PreferenceManagerUtil().getContactPhone();
    tokenPhoneNumber.then((value) {
      // GetX reactive update - NO setState needed!
      if (value.length > 10) {
        selectPhoneNumber = value;
        selectPhoneNumberObs.value = value;  // GetX automatically updates UI
      } else {
        selectPhoneNumber = "+91" + value;
        selectPhoneNumberObs.value = "+91" + value;  // GetX automatically updates UI
      }
    });
    Future<String> tokenEmail = new PreferenceManagerUtil().getContPerEmail();
    tokenEmail.then((value) {
      // GetX reactive update - NO setState needed!
      selectEmail = value;
      selectEmailObs.value = value;  // GetX automatically updates UI
    });
  }

  getMobileAppAccountInfoCall(
      String acc_id, String user_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await getMobileAppAccountInfoApi(acc_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        MobileInfoModel currentParsedResponse =
            MobileInfoModel.fromJson(responseJson);

        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<MobileInfoModel> postGetMobileAppAccountInfoCall(
      String acc_id, String user_id, BuildContext context) async {
    return await getMobileAppAccountInfoCall(acc_id, user_id, context);
  }

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    var height = MediaQuery.of(context).size.height;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      bottomNavigationBar: _addAccessMyAccountButtonUi(),
      appBar: AppBar(
        elevation: 0,
        /*  leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              moveToLastScreen();
            }),*/
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: FutureBuilder<MobileInfoModel>(
        future: postGetMobileAppAccountInfoCall(
            Constants.ACC_ID, Constants.USER_ID, context),
        builder: (context, snapshot) {
          if (snapshot.hasData) {
            selectEmail = snapshot.data!.data![0].serviceRequetEmailSupport.toString();
            selectPhoneNumber = snapshot.data!.data![0].serviceRequetPhoneSupport.toString();
            selectWhatsAppNumber =
                snapshot.data!.data![0].serviceRequestWhatsappSupport.toString();
            return Stack(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Stack(
                      children: <Widget>[
                        Image.asset('assets/images/gp3.png'),
                        Positioned.fill(
                          top: 250,
                          left: 0,
                          right: 0,
                          child: Align(
                            alignment: FractionalOffset.bottomCenter,
                            child: Column(
                              children: [
                                Container(
                                    alignment: Alignment.bottomCenter,
                                    child: Text(
                                      'We have received your request.',
                                      textAlign: TextAlign.center,
                                      style: new CSSStyle()
                                          .poppinsWhiteRegular16(context),
                                    )),
                                Container(
                                    alignment: Alignment.bottomCenter,
                                    child: Text(
                                      'One of our expert will call you to take the work ahead.',
                                      textAlign: TextAlign.center,
                                      style: new CSSStyle()
                                          .poppinsWhiteRegular16(context),
                                    )),
                                Center(
                                  child: Padding(
                                    padding: const EdgeInsets.only(top: 8.0),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          "Request Number : ",
                                          style: new CSSStyle()
                                              .poppinsWhiteRegular16(context),
                                          textAlign: TextAlign.center,
                                        ),
                                        Text(
                                          widget.service_request_id.toString(),
                                          style: new CSSStyle()
                                              .poppinsWhiteBold20(context),
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Positioned(
                  top: 350,
                  left: 0,
                  right: 0,
                  child: Align(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Container(
                        height: 190,
                        child: Card(
                          elevation: 2,
                          child: SingleChildScrollView(
                            child: Column(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Text(
                                    "You can also reach us at : ",
                                    style: new CSSStyle()
                                        .poppinsBlackBold15(context),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 18.0, left: 9, right: 8),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        selectEmail,
                                        style: new CSSStyle()
                                            .poppinsGreyRegular15(context),
                                        textAlign: TextAlign.center,
                                      ),
                                      Row(
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              new MyUtils().sendMail(
                                                  selectEmail, "", "", context);
                                            },
                                            child: Icon(
                                              Icons.attach_email,
                                              color: Color(new CommonColor()
                                                  .green_light_Color),
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                                Divider(),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 8.0, left: 9, right: 8),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        selectPhoneNumber,
                                        style: new CSSStyle()
                                            .poppinsGreyRegular15(context),
                                        textAlign: TextAlign.center,
                                      ),
                                      Row(
                                        children: [
                                          GestureDetector(
                                            onTap: () {
                                              new MyUtils().callMe(
                                                  selectPhoneNumber, context);
                                            },
                                            child: Icon(
                                              Icons.local_phone_outlined,
                                              size: 25,
                                              color: Color(new CommonColor()
                                                  .green_light_Color),
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                                Divider(),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      top: 8.0, left: 9, right: 8),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        selectWhatsAppNumber,
                                        style: new CSSStyle()
                                            .poppinsGreyRegular15(context),
                                        textAlign: TextAlign.center,
                                      ),
                                      Row(
                                        children: [
/*
                                      GestureDetector(
                                        onTap: () {
                                          new MyUtils()
                                              .callMe(selectPhoneNumber, context);
                                        },
                                        child: Icon(
                                          Icons.local_phone_outlined,
                                          size: 25,
                                          color: Color(
                                              new CommonColor().green_light_Color),
                                        ),
                                      ),
*/
                                          Padding(
                                            padding:
                                                const EdgeInsets.only(left: 5.0),
                                            child: GestureDetector(
                                              onTap: () {
                                                new MyUtils().launchWhatsApp(
                                                    selectWhatsAppNumber);
                                              },
                                              child: Padding(
                                                padding:
                                                    const EdgeInsets.all(2.0),
                                                child: GestureDetector(
                                                  onTap: () {
                                                    new MyUtils().launchWhatsApp(
                                                        selectWhatsAppNumber);
                                                  },
                                                  child: FaIcon(
                                                    FontAwesomeIcons.whatsapp,
                                                    size: 20,
                                                    color: Colors.green[500],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            );
          } else {
            return Center(
                child: Container(
                    color: Colors.white,
                    child: new MyUtils().kLoadingWidget(context)));
          }
        },
      ),
    );
  }

  _addAccessMyAccountButtonUi() {
    return Container(height: 45,
      margin: EdgeInsets.all(15),
      constraints: BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().erpca_blue_color),
          Color(new CommonColor().erpca_blue_color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'RETURN TO HOME',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          // Use NavigationHelper for clean navigation
          NavigationHelper.toHome();
        },
        increaseWidthBy: 225.0,
        increaseHeightBy: 50.0,
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
