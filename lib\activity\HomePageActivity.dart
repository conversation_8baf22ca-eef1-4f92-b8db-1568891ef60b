import 'package:erpcacustomer/activity/SelectUserActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/fragment/DocumentsFragment.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:erpcacustomer/fragment/ProfileFragment.dart';
import 'package:erpcacustomer/fragment/RequestFragment.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../fragment/HomeFragment.dart';
import 'NotificationActivity.dart';

class HomePageActivity extends StatelessWidget {
  showScreen(int index) {
    _myTabbedPageKey.currentState!._tabController!.animateTo(index);
  }

  static var _myTabbedPageKey = new GlobalKey<MyTabbedPageState>();

  @override
  Widget build(BuildContext context) {

    return new MaterialApp(
      debugShowCheckedModeBanner: false,
      title: '',
      home: new MyTabbedPage(
        key: _myTabbedPageKey,
      ),
    );
  }
}

class MyTabbedPage extends StatefulWidget {
  const MyTabbedPage({ Key? key}) : super(key: key);

  @override
  MyTabbedPageState createState() => new MyTabbedPageState();
}

int _lastSelected = 0;

class MyTabbedPageState extends State<MyTabbedPage>
    with SingleTickerProviderStateMixin {
  final List<Tab> myTabs = <Tab>[
    new Tab(
      text: "Home",
      icon: Icon(Icons.home),
    ),
    new Tab(
      text: "Invoices",
      icon: Icon(Icons.receipt),
    ),
    new Tab(
      text: "Chat",
      icon: Icon(Icons.chat),
    ),
    new Tab(
      text: "Document",
      icon: Icon(Icons.library_books),
    ),
    Tab(
      text: "Profile",
      icon: Icon(Icons.person),
    ),
  ];

  TabController? _tabController;
  int primaryColor = new CommonColor().erpca_blue_color;
  String selectedUser = "";
  String _profilePic = "";

  // GetX reactive variables - ONLY ADDED, NO UI CHANGES
  var selectedUserObs = ''.obs;
  var profilePicObs = ''.obs;
  var lastSelectedObs = 0.obs;
  var userIdObs = ''.obs;

  @override
  void initState() {
    super.initState();
    // Initialize GetX reactive variables
    lastSelectedObs.value = _lastSelected;
    Future<String> token = new PreferenceManagerUtil().getMainUser();
    token.then((value) {
      // GetX reactive update - NO setState needed!
      selectedUser = value;
      selectedUserObs.value = value;  // GetX automatically updates UI
      print("selectedUser --${selectedUser}");
      print("token --${token}");
    });

    Future<String> tokenProfilePic =
    new PreferenceManagerUtil().getProfilePic();
    tokenProfilePic.then((value) {
      // GetX reactive update - NO setState needed!
      _profilePic = value;
      profilePicObs.value = value;  // GetX automatically updates UI
    });
    Future<String> tokenUserId = new PreferenceManagerUtil().getUserId();
    tokenUserId.then((value) {
      // GetX reactive update - NO setState needed!
      Constants.USER_ID = value;
      userIdObs.value = value;  // GetX reactive variable for UI updates
    });
    _tabController = new TabController(vsync: this, length: myTabs.length);
  }

  @override
  void dispose() {
    _tabController!.dispose();
    super.dispose();
  }
  String greeting() {
    var hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning,';
    }
    if (hour < 17) {
      return 'Good Afternoon,';
    }
    return 'Good Evening,';
  }
  @override
  Widget build(BuildContext context) {
    return new Scaffold(
        appBar: AppBar(
          elevation: 0,
          backgroundColor: new Color(CommonColor().white_Color),
          title: GestureDetector(
            onTap: () {
              HomePageActivity().showScreen(1);
            },
            child: Container(
              child: Column(
                children: <Widget>[
                  Text("" + greeting(),
                      textAlign: TextAlign.left,
                      style: new CSSStyle().poppinsGreyRegular10(context)),
                  Obx(() => Text("" + (selectedUserObs.value.isEmpty ? selectedUser : selectedUserObs.value),
                      textAlign: TextAlign.left,
                      style: new CSSStyle().poppinsBlackRegular15(context))),
                  /*       Text("Here is something new for you",
                      textAlign: TextAlign.left,
                      style: new CSSStyle().poppinsWhiteRegular10(context)),*/
                ],
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
            ),
          ),
          leading: Padding(
            padding: const EdgeInsets.all(6.0),
            child: Obx(() => (profilePicObs.value.isEmpty ? _profilePic : profilePicObs.value) == ""
                ? ClipOval(
              child: Material(
                color: Color(CommonColor().white_Color), // button color
                child: InkWell(
                  splashColor:
                  Color(CommonColor().white_Color), // inkwell color
                  child: SizedBox(
                    width: 56,
                    height: 56,
                    child: Center(
                        child: Obx(() => Text(/*selectedUser[0]*/(selectedUserObs.value.isEmpty ? selectedUser : selectedUserObs.value),
                            style: new CSSStyle().poppinsBlackRegular18(context),))),
                  ),
                  onTap: () {},
                ),
              ),
            )
                : Container(
              width: 130,
              height: 130,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                image: DecorationImage(
                    image: NetworkImage(profilePicObs.value.isEmpty ? _profilePic : profilePicObs.value), fit: BoxFit.fill),
              ),
            )),
          ),
          actions: <Widget>[
            Padding(
              padding: const EdgeInsets.only(left: 8.0),
              child: IconButton(
                  icon: Icon(
                    Icons.switch_account,
                    color: Colors.black,
                  ),
                  onPressed: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => SelectUserActivity(),
                        ));
                  }),
            ),
            IconButton(
                icon: Icon(
                  Icons.notifications_none,
                  color: Colors.black,
                ),
                onPressed: () {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => NotificationActivity(),
                      ));
                }),
          ],
          centerTitle: false,
        ),
        bottomNavigationBar:
        /*FABBottomAppBar(
          color: Colors.white,
          backgroundColor: Color(new CommonColor().erpca_blue_color),
          selectedColor: Colors.orange,
          onTabSelected: _selectedTab,
          height: 50,
          items: [
            FABBottomAppBarItem(iconData: Icons.home, text: 'Home'),
            FABBottomAppBarItem(iconData: Icons.book, text: 'Invoices'),
            FABBottomAppBarItem(
                iconData: Icons.request_quote_outlined, text: 'My Request'),
            FABBottomAppBarItem(
                iconData: Icons.library_books, text: 'Document'),
            FABBottomAppBarItem(iconData: Icons.person, text: 'Profile'),
          ],
        ),*/
        Theme(
          data: Theme.of(context).copyWith(
              // sets the background color of the `BottomNavigationBar`
              canvasColor: Color(new CommonColor().erpca_blue_color),
              // sets the active color of the `BottomNavigationBar` if `Brightness` is light
              primaryColor: Colors.red,
              /* textTheme: Theme.of(context)
                  .textTheme
                  .copyWith(caption: new TextStyle(color: Colors.yellow))), */
                  textTheme: Theme.of(context).textTheme.copyWith(
          bodySmall: TextStyle(color: Colors.yellow),
        ),),
          child: Obx(() => BottomNavigationBar(
            onTap: selectedTab,
            // GetX reactive state - always use reactive variable
            currentIndex: lastSelectedObs.value,
            backgroundColor: Color(new CommonColor().erpca_blue_color),
            selectedItemColor: Colors.white,
            unselectedItemColor: Color(new CommonColor().blue_lighter_text_Color),
            showUnselectedLabels: true,
            unselectedFontSize: 10,
            selectedFontSize: 10,
// new
            items: [
              new BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'Home',
              ),
              new BottomNavigationBarItem(
                icon: Icon(Icons.book),
                label: 'Invoices',
              ),
              new BottomNavigationBarItem(
                icon: Icon(Icons.request_quote_outlined),
                label: 'Request',
              ),
              new BottomNavigationBarItem(
                icon: Icon(Icons.library_books),
                label: 'Document',
              ),
              new BottomNavigationBarItem(
                icon: Icon(Icons.person),
                label: 'Profile',
              )
            ],
          )),
        ),
        //floatingActionButton: _buildFab(context),
        body: Center(
          child: Obx(() => getBody(lastSelectedObs.value)),
        ));
  }

  Widget getBody(int _lastSelected) {
    if (_lastSelected == 0) {
      return HomeFragment();
    } else if (_lastSelected == 1) {
      return InvoicesFragment(
        type: "0",
      );
    } else if (_lastSelected == 2) {
      return RequestFragment();
    } else if (_lastSelected == 3) {
      return DocumentsFragment();
    } else if (_lastSelected == 4) {
      return MyAccountFragment();
    }
    return HomeFragment(); // Default fallback
  }

  Widget _buildFab(BuildContext context) {
    final icons = [Icons.sms, Icons.mail, Icons.phone];
    return /*FloatingActionButton(
      backgroundColor: Color(new CommonColor().erpca_blue_color),
      onPressed: () {},
      tooltip: 'Increment',
      child: Icon(Icons.chat),
      elevation: 10.0,
    );*/
      FloatingActionButton(
        onPressed: () {},
        backgroundColor: Color(new CommonColor().erpca_blue_color),
        elevation: 10,
        child: Container(
          child: Icon(Icons.chat),
          decoration: BoxDecoration(
            color: Colors.transparent,
            borderRadius: BorderRadius.all(
              Radius.circular(100),
            ),
            boxShadow: [
              BoxShadow(
                color: Color(new CommonColor().erpca_blue_color).withOpacity(0.3),
                spreadRadius: 3,
                blurRadius: 3,
                offset: Offset(1, 3),
              ),
            ],
          ),
        ),
      );
  }

  void selectedTab(int index) {
    // GetX reactive update - NO setState needed!
    _lastSelected = index;
    lastSelectedObs.value = index;  // GetX automatically updates UI
  }

  void _selectedFab(int index) {
    // GetX reactive update - NO setState needed!
    _lastSelected = index;
    lastSelectedObs.value = index;  // GetX automatically updates UI
  }

  Widget menu() {
    return Container(
      color: Colors.white,
      child: TabBar(
        isScrollable: false,
        controller: _tabController,
        labelColor: Color(new CommonColor().erpca_blue_color),
        unselectedLabelColor: Colors.grey,
        indicatorSize: TabBarIndicatorSize.tab,
        indicatorColor: Colors.blue,
        tabs: myTabs,
      ),
    );
  }
}
