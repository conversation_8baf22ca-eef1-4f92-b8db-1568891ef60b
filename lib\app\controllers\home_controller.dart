// Home Controller
// Manages home screen state and navigation using GetX

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../services/task_service.dart';
import '../services/document_service.dart';
import '../services/invoice_service.dart';

class HomeController extends GetxController with GetSingleTickerProviderStateMixin {
  final ApiService _apiService = Get.find<ApiService>();
  final StorageService _storageService = Get.find<StorageService>();
  final TaskService _taskService = Get.find<TaskService>();
  final DocumentService _documentService = Get.find<DocumentService>();
  final InvoiceService _invoiceService = Get.find<InvoiceService>();

  // Tab controller for bottom navigation
  late TabController tabController;

  // Reactive variables
  final selectedIndex = 0.obs;
  final isLoading = false.obs;
  final userName = ''.obs;
  final userCompany = ''.obs;
  final notificationCount = 0.obs;

  // Dashboard data
  final totalTasks = 0.obs;
  final pendingTasks = 0.obs;
  final completedTasks = 0.obs;
  final totalDocuments = 0.obs;
  final totalInvoices = 0.obs;
  final recentActivities = <Map<String, dynamic>>[].obs;

  // Tab names
  final List<String> tabNames = [
    'Home',
    'Services',
    'Documents',
    'Invoices',
    'Profile'
  ];

  @override
  void onInit() {
    super.onInit();
    tabController = TabController(length: 5, vsync: this);
    loadUserData();
    loadDashboardData();
  }

  @override
  void onClose() {
    tabController.dispose();
    super.onClose();
  }

  // Load user data
  Future<void> loadUserData() async {
    try {
      userName.value = await _storageService.getUserName();
      userCompany.value = await _storageService.getSelectedCompany();
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  // Load dashboard data
  Future<void> loadDashboardData() async {
    try {
      isLoading.value = true;
      
      // Load tasks summary
      await loadTasksSummary();
      
      // Load documents count
      await loadDocumentsCount();
      
      // Load invoices count
      await loadInvoicesCount();
      
      // Load recent activities
      await loadRecentActivities();
      
      // Load notifications count
      await loadNotificationsCount();
      
    } catch (e) {
      print('Error loading dashboard data: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // Load tasks summary
  Future<void> loadTasksSummary() async {
    try {
      final tasks = await _taskService.getAllTasks();
      totalTasks.value = tasks.length;
      pendingTasks.value = tasks.where((task) => task['status'] == 'pending').length;
      completedTasks.value = tasks.where((task) => task['status'] == 'completed').length;
    } catch (e) {
      print('Error loading tasks summary: $e');
    }
  }

  // Load documents count
  Future<void> loadDocumentsCount() async {
    try {
      final documents = await _documentService.getAllDocuments();
      totalDocuments.value = documents.length;
    } catch (e) {
      print('Error loading documents count: $e');
    }
  }

  // Load invoices count
  Future<void> loadInvoicesCount() async {
    try {
      final invoices = await _invoiceService.getAllInvoices();
      totalInvoices.value = invoices.length;
    } catch (e) {
      print('Error loading invoices count: $e');
    }
  }

  // Load recent activities
  Future<void> loadRecentActivities() async {
    try {
      final activities = await _apiService.get('/recent-activities');
      if (activities['success']) {
        recentActivities.value = List<Map<String, dynamic>>.from(activities['data']);
      }
    } catch (e) {
      print('Error loading recent activities: $e');
    }
  }

  // Load notifications count
  Future<void> loadNotificationsCount() async {
    try {
      final notifications = await _apiService.get('/notifications/count');
      if (notifications['success']) {
        notificationCount.value = notifications['count'] ?? 0;
      }
    } catch (e) {
      print('Error loading notifications count: $e');
    }
  }

  // Change tab
  void changeTab(int index) {
    selectedIndex.value = index;
    tabController.animateTo(index);
  }

  // Refresh dashboard data
  Future<void> refreshDashboard() async {
    await loadDashboardData();
  }

  // Get greeting based on time
  String getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'Good Morning';
    } else if (hour < 17) {
      return 'Good Afternoon';
    } else {
      return 'Good Evening';
    }
  }

  // Get dashboard stats
  Map<String, int> getDashboardStats() {
    return {
      'totalTasks': totalTasks.value,
      'pendingTasks': pendingTasks.value,
      'completedTasks': completedTasks.value,
      'totalDocuments': totalDocuments.value,
      'totalInvoices': totalInvoices.value,
      'notifications': notificationCount.value,
    };
  }

  // Quick actions
  void navigateToNewService() {
    // Navigate to new service screen
    Get.toNamed('/new-services');
  }

  void navigateToTasks() {
    changeTab(1); // Switch to services tab
  }

  void navigateToDocuments() {
    changeTab(2); // Switch to documents tab
  }

  void navigateToInvoices() {
    changeTab(3); // Switch to invoices tab
  }

  void navigateToProfile() {
    changeTab(4); // Switch to profile tab
  }

  // Search functionality
  final searchQuery = ''.obs;
  final searchResults = <Map<String, dynamic>>[].obs;

  void updateSearchQuery(String query) {
    searchQuery.value = query;
    if (query.isNotEmpty) {
      performSearch(query);
    } else {
      searchResults.clear();
    }
  }

  Future<void> performSearch(String query) async {
    try {
      final results = await _apiService.get('/search?q=$query');
      if (results['success']) {
        searchResults.value = List<Map<String, dynamic>>.from(results['data']);
      }
    } catch (e) {
      print('Search error: $e');
    }
  }

  void clearSearch() {
    searchQuery.value = '';
    searchResults.clear();
  }
}
