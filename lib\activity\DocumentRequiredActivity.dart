
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/DocRequiredFragment.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DocumentRequiredActivity extends StatefulWidget {
  String? taskId = "";
  String? taskname = "";
  String? task_type = "";
  DocumentRequiredActivity({ Key? key,  this.taskId, this.task_type,  this.taskname})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return DocumentRequiredActivityState();
  }
}

class DocumentRequiredActivityState extends State<DocumentRequiredActivity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          widget.taskname.toString(),
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: DocRequiredFragment(
        taskId: widget.taskId.toString(),
        task_type: widget.task_type.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
