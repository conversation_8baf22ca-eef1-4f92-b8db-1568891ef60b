// Authentication Binding
// Manages dependency injection for authentication-related controllers and services

import 'package:get/get.dart';
import '../controllers/auth_controller.dart';
import '../services/auth_service.dart';
import '../services/storage_service.dart';
import '../services/api_service.dart';
import '../../controller/LoginController.dart';
import '../../controller/LoginUserNamePasswordController.dart';

class AuthBinding extends Bindings {
  @override
  void dependencies() {
    // Services
    Get.lazyPut<StorageService>(() => StorageService());
    Get.lazyPut<ApiService>(() => ApiService());
    Get.lazyPut<AuthService>(() => AuthService());
    
    // Controllers
    Get.lazyPut<AuthController>(() => AuthController());
  }
}
