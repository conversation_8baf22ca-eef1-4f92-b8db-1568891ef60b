import 'package:erpcacustomer/activity/InvoiceActivity.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class PaymentAcknowledgement extends StatefulWidget {
  //const PaymentAcknowledgement({Key? key}) : super(key: key);
  bool paymentStatus;
  String paymentId;
  String amount;
  PaymentAcknowledgement(this.paymentStatus,this.paymentId,this.amount);

  @override
  State<PaymentAcknowledgement> createState() => _PaymentAcknowledgementState();
}

class _PaymentAcknowledgementState extends State<PaymentAcknowledgement> {
  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.of(context).size.height;
    var width = MediaQuery.of(context).size.width;
    return Scaffold(
      body: SafeArea(
        child: Container(
          height: height,
          width: width,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [

            Container(
              margin: EdgeInsets.only(top: 0.1 * width),
              height: 0.2 * width,
              width: 0.2 * width,
              child: Image(
                image: AssetImage(widget.paymentStatus?'assets/images/dashboard/complete.png':'assets/images/payment_failed.jpg'
                    ''),
                fit: BoxFit.fill,
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 0.1 * width),
              child: Text(widget.paymentStatus?'Payment Successful!':'Payment Failed!',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 0.06 * width,
              ),
              ),
            ),
            if(widget.paymentStatus) Container(
              margin: EdgeInsets.only(top: 0.035 * width),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Payment Id: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 0.04 * width,
                  ),
                  ),
                  Text(widget.paymentId,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 0.04 * width,
                  ),
                  ),
                ],
              ),
            ),
            if(widget.paymentStatus) Container(
              margin: EdgeInsets.only(top: 0.035 * width),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Net Amount: ',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 0.04 * width,
                  ),
                  ),
                  Text( "₹ " + widget.amount,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 0.04 * width,
                  ),
                  ),
                ],
              ),
            ),
            if(!widget.paymentStatus) Container(
              margin: EdgeInsets.only(
                top: 0.037 * width,
                left: 0.2 * width,
                right: 0.2 * width,
              ),
              child:  Text('An error occured while processing your payment',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 0.05 * width,
                  fontFamily: 'GlacialIndifference'
                ),
              ),
            ),
            SizedBox(height: 0.2 * width,),
            InkWell(
              onTap: (){
              Navigator.pop(context,widget.paymentStatus?true:false);
              },
              child: Container(
                alignment: Alignment.center,
                height: 0.13 * width,
                width: 0.25 * width,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(0.02 * width),
                  color: Colors.blueAccent,
                ),
                child: Text('Back',
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 0.05 * width,
                    color: Colors.white,
                  ),
                ),
              ),
            ),


          ],
        ),
        ),
      ),
    );
  }
}
