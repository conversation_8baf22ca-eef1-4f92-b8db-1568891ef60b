import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:erpcacustomer/fragment/DocAssignFragment.dart';
import 'package:erpcacustomer/fragment/OtherDocumentFragment.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class OtherDocumentActivity extends StatefulWidget {
  String? taskId = "";
  String? taskname = "";
  OtherDocumentActivity({Key? key, this.taskId, this.taskname})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return OtherDocumentActivityState();
  }
}

class OtherDocumentActivityState extends State<OtherDocumentActivity> {
  // GetX reactive variables
  var taskIdObs = ''.obs;
  var taskNameObs = ''.obs;

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          widget.taskname.toString(),
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: OtherDocumentFragment(
        taskId: widget.taskId.toString(),
        taskName: widget.taskname.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
