import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:path/path.dart';

import '../model/ValidateCustomerMobileModel.dart';

String otp = '';
String mobile_no = '';
var responseJson;
//
//void verifyLogin(String mobile_Number, BuildContext context) {
//  if (mobile_Number.isEmpty) {
//    MyUtils.showToast("Please Enter Mobile Number.");
//  } else if (mobile_Number.length != 10) {
//    MyUtils.showToast("Please Enter Proper Mobile Number.");
//  } else {
////    MyUtils.showToast("Api Call");
//    callLoginApi(mobile_Number, context);
//  }
//}

Future<dynamic> getUserDetailsApi(
  String token,
  BuildContext context,
) async {
  Map<String, String> body = {};
  return getWithContent(Constants.GET_USER_DETAILS_URL, token, body, context);
}


Future<dynamic> callLoginApi1(
    String acc_id,
    String mobile_no,
    String fcm_id,
    String validate,
    BuildContext context,
    ) async {
  String deviceType = new MyUtils().checkAndroidOrIos();
  Map<String, String> body = {
    'acc_id': acc_id,
    'mobile_no': mobile_no,
    'fcm_id': fcm_id,
    'validate': validate,
    'device': deviceType,
  };

  print('=== API REQUEST DEBUG ===');
  print('URL: ${Constants.LOGIN_URL}');
  print('Body: $body');
  print('Headers: Id=erpca, Pass=e736258681ac6d7126d298cc93a732db1dad2996');

  return postWithContent(Constants.LOGIN_URL, body, context);
}

Future<dynamic> callLoginApi(
    String acc_id,
    String mobile_no,
    String fcm_id,
    String validate,
    BuildContext context,
    ) async {
  try {
    String deviceType = new MyUtils().checkAndroidOrIos();

    Map<String, dynamic> requestData = {
      "acc_id": acc_id,
      "mobile_no": mobile_no,
      "fcm_id": "",
      "imei_no": "",
      "device": deviceType,
      "validate": validate
    };

    // ✅ API Call Logging
    log('[URL] : ${Constants.LOGIN_URL}\n[PARAMS] : ${json.encode(requestData)}');
    log("[URL] : ${Constants.LOGIN_URL}");

    Dio _dio = Dio();
    final response = await _dio.post(
      Constants.LOGIN_URL,
      data: json.encode(requestData),
      options: Options(
        headers: {
          'id': 'erpca',
          'pass': 'e736258681ac6d7126d298cc93a732db1dad2996',
          'Content-Type': 'application/json',
        },
      ),
    );

    // ✅ API Response Logging
    print('Login Response Status: ${response.statusCode}');
    print('Login Response Data: ${response.data}');

    int flag = response.data['success'];
    if (flag == 1) {
      ValidateCustomerMobileModel currentParsedResponse =
      ValidateCustomerMobileModel.fromJson(response.data);
      print('Login Success: Parsed response created');
      return currentParsedResponse;
    } else {
      print('Login Failed: Success flag is $flag');
    }
  } on DioException catch (err) {  // ✅ FIXED: DioError renamed to DioException in dio 5.x
    final errorMessage = err;
    print('error msg $errorMessage');
    throw errorMessage;
  } catch (e) {
    print('e msg $e');
    throw e.toString();
  }
}








Future<dynamic> postResetPasswordApi(
  String email,
  BuildContext context,
) async {
  Map<String, String> body = {
    'email': email,
  };
  return postWithContent(Constants.RESET_PASSWORD_URL, body, context);
}

Future<dynamic> postChangePasswordApi(
  String old_password,
  String new_password,
  String userToken,
  BuildContext context,
) async {
  Map<String, String> body = {
    'old_password': old_password,
    'new_password': new_password,
  };

  return postWithContentToken(
      Constants.POST_CHANGE_PASSWORD_URL, body, userToken, context);
}

Future<dynamic> uploadProfilePic(
  String token,
  File visitorImg,
  BuildContext context,
) async {
  var dio = new Dio();
  // dio.options.baseUrl = Constants.ADD_MEMBER_URL;
  dio.options.connectTimeout = Duration(milliseconds: 15000); // ✅ FIXED: dio 5.x requires Duration
  dio.options.receiveTimeout = Duration(milliseconds: 15000);
  dio.options.headers['Authorization'] = "Bearer " + token;
  dio.options.headers['Content-Type'] = 'application/json';

  MultipartFile? deliveryInv;
  if (visitorImg != null &&
      visitorImg.path != null &&
      visitorImg.path.isNotEmpty) {
    // Create a FormData
    String fileName = basename(visitorImg.path);
    deliveryInv = await MultipartFile.fromFile(visitorImg.path,
        filename: basename(visitorImg.path));
  }

  FormData formData = FormData.fromMap({
    'avatar': deliveryInv,
  });

  var response = await dio.post(Constants.POST_UPDATE_PROFILE_PICTURE_URL,
      data: formData,
      options: Options(
        method: 'POST',
        responseType: ResponseType.json,
      ));
  responseJson = json.decode(response.toString());
  return responseJson;
}

Future<dynamic> callCheckOtpApi(
  String searchvalue,
  String OTP,
  BuildContext context,
) async {
  Map<String, String> body = {
    'searchvalue': searchvalue,
    'OTP': OTP,
  };
  return post(Constants.MEMBER_OTP_VERIFICATION_URL, body, context);
}

Future<dynamic> callForgotPasswordApi(
  String searchvalue,
  BuildContext context,
) async {
  Map<String, String> body = {
    'searchvalue': searchvalue,
  };
  return post(Constants.MEMBER_FORGOT_PASSWORD_URL, body, context);
}

Future<dynamic> callRegistrationApi(
  String memberShipNo,
  String memberEmail,
  String memberPassword,
  String memberConfirmPassword,
  String memberContactNo,
  BuildContext context,
) async {
  Map<String, String> body = {
    'memberShipNo': memberShipNo,
    'memberEmail': memberEmail,
    'memberPassword': memberPassword,
    'memberConfirmPassword': memberConfirmPassword,
    'memberContactNo': memberContactNo,
  };
  return postWithContent(
      Constants.MEMBER_REGISTRATION_URL, json.encode(body), context);
}

Future<dynamic> callDashboardApi(
  String memberNo,
  BuildContext context,
) async {
  Map<String, String> body = {
    'memberNo': memberNo,
  };

  return post(Constants.DASHBOARD_URL, body, context);
}

Future<dynamic> post(String url, var body, BuildContext context) async {
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http.post(Uri.parse(url), body: body, headers: {
    "Accept": "application/json",
    "Cache-Control": "false",
    "eO2-Secret-Code": Constants.ACCESS_TOKEN
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> postWithContent(
    String url, var body, BuildContext context) async {

  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");

  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http.post(Uri.parse(url), body: json.encode(body), headers: {
    "Id": "erpca",
    "Pass": "e736258681ac6d7126d298cc93a732db1dad2996",
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    final String responseBody = response.body;

    // ✅ API Response Logging
    log('[RESPONSE STATUS] : $statusCode');
    log('[RESPONSE BODY] : $responseBody');

    if (statusCode < 200 || statusCode > 500) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return json.decode(response.body);
  });
}

Future<dynamic> postWithContentToken(
    String url, var body, String token, BuildContext context) async {
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http.post(Uri.parse(url), body: json.encode(body), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}

Future<dynamic> getWithContent(
    String url, String token, var body, BuildContext context) async {
  // ✅ API Call Logging
  log('[URL] : $url\n[PARAMS] : ${json.encode(body)}');
  log("[URL] : $url");
  return await http.get(Uri.parse(url), headers: {
    "Authorization": "Bearer " + token,
    "Content-Type": "application/json",
  }).then((http.Response response) {
    final int statusCode = response.statusCode;
    if (statusCode < 200 || statusCode > 500 || json == null) {
      throw new Exception("Error while fetching data");
    }
    responseJson = json.decode(response.body);
    return responseJson;
  });
}
