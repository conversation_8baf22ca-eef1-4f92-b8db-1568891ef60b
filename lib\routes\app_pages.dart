// GetX App Pages
// Defines all page routes and their corresponding widgets

import 'package:get/get.dart';
import 'package:erpcacustomer/activity/SplashActivity.dart';
import 'package:erpcacustomer/activity/NewServicesActivity.dart';
import 'package:erpcacustomer/activity/LoginActivity.dart';
import 'package:erpcacustomer/activity/LoginWithMobileNumberActivity.dart';
import 'package:erpcacustomer/activity/SelectUserActivity.dart';
import 'package:erpcacustomer/activity/HomePageActivity.dart';
import 'package:erpcacustomer/activity/SubmitRequestActivity.dart';
import 'package:erpcacustomer/activity/NotificationActivity.dart';
import 'package:erpcacustomer/activity/CheckMPinActivity.dart';
import 'app_routes.dart';

class AppPages {
  AppPages._();

  static const INITIAL = Routes.SPLASH;

  static final routes = [
    // Initial/Splash Routes
    GetPage(
      name: Routes.INITIAL,
      page: () => NewServicesActivity(),
      transition: Transition.fade,
    ),
    GetPage(
      name: Routes.SPLASH,
      page: () => SplashActivity(),
      transition: Transition.fade,
    ),
    GetPage(
      name: Routes.NEW_SERVICES,
      page: () => NewServicesActivity(),
      transition: Transition.fade,
    ),

    // Authentication Routes
    GetPage(
      name: Routes.LOGIN,
      page: () => LoginActivity(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.LOGIN_MOBILE,
      page: () => LoginWithMobileNumberActivity(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.SELECT_USER,
      page: () => SelectUserActivity(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.CHECK_MPIN,
      page: () => CheckMPinActivity(),
      transition: Transition.rightToLeft,
    ),

    // Main App Routes
    GetPage(
      name: Routes.HOME,
      page: () => HomePageActivity(),
      transition: Transition.fadeIn,
    ),

    // Feature Routes
    GetPage(
      name: Routes.SUBMIT_REQUEST,
      page: () => SubmitRequestActivity(),
      transition: Transition.rightToLeft,
    ),
    GetPage(
      name: Routes.NOTIFICATIONS,
      page: () => NotificationActivity(),
      transition: Transition.rightToLeft,
    ),
  ];
}
