import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'CommonColor.dart';
import 'Dimens.dart';

class CSSStyle {
  TextStyle poppinsDarkBlueBold20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Color(new CommonColor().darkBlueText),
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsDarkBlueBold18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Color(new CommonColor().darkBlueText),
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsDarkBlueRegular20(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_20,
      color: Color(new CommonColor().darkBlueText),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsDarkBlueRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().darkBlueText),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsDarkBlueRegular16(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_16,
      color: Color(new CommonColor().darkBlueText),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlackRegular20(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_20,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlackRegular18(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_18,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsYellowRegular20(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_20,
      color: Color(new CommonColor().yellow_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsYellowRegular18(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_18,
      color: Color(new CommonColor().yellow_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsYellowRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().yellow_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsGreyRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsGreyRegular16(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_16,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsGreyRegular17(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_17,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsGreyRegular18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle verdanaGreyRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle verdanaGreyRegular13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansGreyRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'GlacialIndifference-Italic.otf',
    );
  }

  TextStyle poppinsDarkTextRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().oxygen_dark_text_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsDarkTextRegular14(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_14,
        color: Color(new CommonColor().oxygen_dark_text_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsDarkTextRegular13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Color(new CommonColor().oxygen_dark_text_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansDarkTextRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().oxygen_dark_text_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansDarkTextRegular14(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_14,
        color: Color(new CommonColor().oxygen_dark_text_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansDarkTextRegular13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Color(new CommonColor().oxygen_dark_text_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansDarkBlueRegular13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Color(new CommonColor().oxygen_dark_blue_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansDarkBlueRegular10(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_10,
        color: Color(new CommonColor().oxygen_dark_blue_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansDarkBlueRegular11(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_11,
        color: Color(new CommonColor().oxygen_dark_blue_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsGreyRegular20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsGreyRegular14(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_14,
        color: Color(new CommonColor().grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }
  TextStyle poppinsGreyRegular14Height(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_14,
        color: Color(new CommonColor().grey_Color),
        height: 1.5,

        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsLightGreyRegular14(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_14,
        color: Color(new CommonColor().light_grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsLightGreyRegular12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Color(new CommonColor().light_grey_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsBlackLight20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w300);
  }

  TextStyle poppinsYellowLight20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Color(new CommonColor().yellow_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w300);
  }

  TextStyle poppinsBlueLight20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Color(new CommonColor().lightBlue),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w300);
  }

  TextStyle poppinsBlueLight15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().lightBlue),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w300);
  }

  TextStyle poppinsBlueLightBold15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().lightBlue),
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlueLightestRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_lightest_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlueLightBold20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Color(new CommonColor().lightBlue),
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackLight15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w300);
  }

  TextStyle poppinsBlackRegular40(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_40,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlackRegular30(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_30,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlackRegular5(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_5,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlackRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }
  TextStyle poppinsBlackRegular11(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_11,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }  TextStyle poppinsBlackRegular9(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_9,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  } TextStyle poppinsGreyRegular9(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_9,
      color: Colors.grey,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsWhiteBold20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBold20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBold30(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_30,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBold40(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_40,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBold15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBoldUnderline15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        decoration: TextDecoration.underline,
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsOrangeRegularUnderline15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Colors.orange,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
      decoration: TextDecoration.underline,
    );
  }

  TextStyle poppinsBlackBold13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBold13W400(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w500);
  }

  TextStyle poppinsBlackBold12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsWhiteBold12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsWhiteBold13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBold18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackBoldUnderline18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        decoration: TextDecoration.underline,
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackRegular10(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_10,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlackRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlackItalic12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.black,
        fontFamily: 'GlacialIndifference-Italic',
        fontStyle: FontStyle.italic);
  }

  TextStyle poppinsBlackItalic10(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_10,
        color: Colors.black,
        fontFamily: 'GlacialIndifference-Italic',
        fontStyle: FontStyle.italic);
  }

  TextStyle poppinsBlackItalicBold12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontStyle: FontStyle.italic,
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsBlackItalicBold10(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_10,
        color: Colors.black,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontStyle: FontStyle.italic,
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsAmberItalic10(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_10,
        color: Colors.amber,
        fontFamily: 'GlacialIndifference-Italic',
        fontStyle: FontStyle.italic);
  }

  TextStyle poppinsAmberItalic12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.amber,
        fontFamily: 'GlacialIndifference-Italic',
        fontStyle: FontStyle.italic);
  }

  TextStyle poppinsAmberRegular12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.amber,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsAmberRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.amber,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsBlueRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.lightBlue,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsErpcaBlueRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().erpca_blue_color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsErpcaBlueRegular18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Color(new CommonColor().erpca_blue_color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsErpcaBlueBoldRegular18(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_18,
      fontWeight: FontWeight.w700,
      color: Color(new CommonColor().erpca_blue_color),
      fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
    );
  }

  TextStyle poppinsErpcaBlueBoldRegular20(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_21,
      fontWeight: FontWeight.w700,
      color: Color(new CommonColor().erpca_blue_color),
      fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
    );
  }

  TextStyle poppinsBlueRegular12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.lightBlue,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle ptSansBlueRegular12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.lightBlue,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsAmberRegular18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Colors.amber,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsAmberRegular20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.amber,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsGreenRegular12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.teal,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsGreenRegular8(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_8,
        color: Colors.teal,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsGreenRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.green,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsRedRegular12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.red,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsRedRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.red,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsOrangeRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.deepOrange[400],
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsGreenRegular20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.teal,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1');
  }

  TextStyle poppinsGreyRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().grey_Color),
      height: 1.5,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
      //fontWeight: FontWeight.w400
    );
  }

  TextStyle poppinsGreyRegular12Height5(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().grey_Color),

      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
      //fontWeight: FontWeight.w400
    );
  }

  TextStyle poppinsGreyRegular10(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_10,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
      //fontWeight: FontWeight.w400
    );
  }

  TextStyle poppinsGreyRegular8(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_8,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
      //fontWeight: FontWeight.w400
    );
  }

  TextStyle poppinsBlackRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsBlueRegular13(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_13,
      color: Color(new CommonColor().figma_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular16(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_16,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular17(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_17,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular18(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_18,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular22(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_22,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular25(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_25,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansDarkBlueRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansErpcaDarkBlueRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansDarkBlueRegular16(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_16,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansDarkBlueRegular18(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_18,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular13(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_13,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular11(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_11,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansBlackRegular10(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_10,
      color: Colors.black,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightGreyRegular11(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_11,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightBlueRegular11(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_11,
      color: Color(new CommonColor().oxygen_light_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightGreenRegular11(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_11,
      color: Color(new CommonColor().green_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightGreyRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightGreyRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightGreyRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLightBlackRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLightBlackRegular16(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_16,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLightBlackRegular17(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_17,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLightBlackRegular20(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_20,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightBlackRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightBlackRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansLightBlackRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLightBlackRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLighterBlackRegular16(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_16,
      color: Color(new CommonColor().oxygen_lighter_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLighterBlackRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_lighter_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLighterBlueRegular16(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_16,
      color: Color(new CommonColor().blue_lighter_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLighterBlueRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Color(new CommonColor().blue_lighter_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLightBlackRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsLightBlackRegular10(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_11,
      color: Color(new CommonColor().oxygen_light_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsMinidarkTextRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().oxygen_minidark_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsblueTextRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsblueTextRegular10(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_10,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsblueTextRegular16(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_16,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsblueTextRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Color(new CommonColor().oxygen_dark_blue_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansMinidarkTextRegular12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Color(new CommonColor().oxygen_minidark_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsMinidarkTextRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_minidark_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansMinidarkTextRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().oxygen_minidark_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansMinidarkTextRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Color(new CommonColor().oxygen_minidark_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansMinidarkTextRegular13(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_13,
      color: Color(new CommonColor().oxygen_minidark_text_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsPinkRegular18(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_18,
      color: Colors.pinkAccent,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsPinkRegular20(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_20,
      color: Colors.pinkAccent,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsPinkWeight400Regular20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.pinkAccent,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsPinkRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Colors.pinkAccent,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansPinkRegular13(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_13,
      color: Colors.pinkAccent,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansGrayRegular13(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_13,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansGrayRegular14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle ptSansGrayRegular15(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_15,
      color: Color(new CommonColor().grey_Color),
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsWhiteRegular20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular22(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_22,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular25(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_25,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular14(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_14,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular16(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_16,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansWhiteRegular18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansWhiteRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular40(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_40,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular30(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_30,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular17(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_17,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular11(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_11,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteRegular10(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_10,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansWhiteRegular10(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_10,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansWhiteRegular8(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_8,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsPinkLight20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Color(new CommonColor().oxygen_pink_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsPinkLight18(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_18,
        color: Color(new CommonColor().oxygen_pink_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsPinkLight15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Color(new CommonColor().oxygen_pink_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsPinkLight12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Color(new CommonColor().oxygen_pink_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsBlueLight12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Color(new CommonColor().oxygen_dark_blue_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsBlueLightest12(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_12,
        color: Color(new CommonColor().oxygen_lightest_blue_Color),
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansWhiteRegular7(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_7,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle ptSansWhiteRegular13(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_13,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w400);
  }

  TextStyle poppinsWhiteLight20(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_20,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Regular-1',
        fontWeight: FontWeight.w300);
  }

  TextStyle verdanaWhiteLight20(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_20,
      color: Colors.white,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle verdanaWhiteLight18(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_18,
      color: Colors.white,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle verdanaWhiteLight14(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_14,
      color: Colors.white,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle verdanaWhiteLight12(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_12,
      color: Colors.white,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle verdanaWhiteLight10(BuildContext context) {
    return TextStyle(
      fontSize: new Dimens().pixel_10,
      color: Colors.white,
      fontFamily: 'FontsFree-Net-SFProText-Regular-1',
    );
  }

  TextStyle poppinsWhiteBold40(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_40,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsDarkBlueBold40(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_40,
        color: Color(new CommonColor().darkBlue),
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsLightBlueBold40(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_40,
        color: Color(new CommonColor().lightBlue),
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsWhiteBold15(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_15,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }

  TextStyle poppinsWhiteBold30(BuildContext context) {
    return TextStyle(
        fontSize: new Dimens().pixel_30,
        color: Colors.white,
        fontFamily: 'FontsFree-Net-SFProText-Semibold-1',
        fontWeight: FontWeight.w700);
  }
}
