import 'dart:io';

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:get/get.dart';

class AddDocumentActivity extends StatefulWidget {
  String? taskId = "";
  String? taskname = "";

  AddDocumentActivity({ this.taskId,  this.taskname});

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return AddDocumentActivityState();
  }
}

class AddDocumentActivityState extends State<AddDocumentActivity>
    with ImagePickerListener, TickerProviderStateMixin {
  // GetX reactive variables
  var titleObs = ''.obs;
  var fileNameObs = ''.obs;
  var isLoading = false.obs;
  var isSaving = false.obs;

  // Original variables
  String title = "";
   ImagePickerHandler? imagePicker;
   AnimationController? _controller;
  List<PlatformFile>? _paths;
  FileType _pickingType = FileType.any;
  bool _multiPick = false;
  String _extension = "";
  String _fileName = "";
  bool _saving = false;
  bool _loadingPath = false;
  String _directoryPath = '';
  File? _image;
  final titleController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          widget.taskname!,
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            children: [
              Container(
                margin: EdgeInsets.only(left: 15, right: 15),
                child: Padding(
                  padding: EdgeInsets.only(
                    top: 20.0,
                  ),
                  child: TextFormField(
                    controller: titleController,
                    inputFormatters: <TextInputFormatter>[
                    FilteringTextInputFormatter.allow(RegExp("[a-z]")),
                  ],
                    validator: _validateDescription,
                    keyboardType: TextInputType.text,
                    maxLines: 1,
                    decoration: InputDecoration(
                      labelText: "Enter Your Title",
                    ),
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 15, right: 15),
                child: Padding(
                  padding: EdgeInsets.only(
                    top: 20.0,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Upload Files",
                        style: new CSSStyle().poppinsBlackRegular18(context),
                      ),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () async {
                              //   imagePicker.showDialog(context);
                              //  document_primary_key_id = allData.wcDocReqId;

                              _openFileExplorer();
                            },
                            child: Icon(
                              Icons.cloud_upload,
                              size: 20,
                              color: Color(new CommonColor().green_light_Color),
                            ),
                          ),

                        ],
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                margin: EdgeInsets.only(left: 15, right: 15),
                child: Padding(
                  padding: EdgeInsets.only(
                    top: 20.0,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Upload Image",
                        style: new CSSStyle().poppinsBlackRegular18(context),
                      ),
                      Row(
                        children: [
                          GestureDetector(
                            onTap: () async {
                              //   imagePicker.showDialog(context);
                              // document_primary_key_id = allData.wcDocReqId;

                              imagePicker!.showDialog(context);
                            },
                            child: Icon(
                              Icons.camera_alt,
                              size: 20,
                              color: Color(new CommonColor().green_light_Color),
                            ),
                          )

                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),

          _addAccessMyAccountButtonUi()
        ],
      ),
    );
  }

  _addAccessMyAccountButtonUi() {
    return Column(
      children: [
        Padding(
          padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            margin: EdgeInsets.only(left: 15, right: 15, bottom: 55),
            constraints: BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
            child: GradientButton(
              gradient: LinearGradient(colors: [
                Color(new CommonColor().erpca_blue_color),
                Color(new CommonColor().erpca_blue_color)
              ], begin: Alignment.centerLeft, end: Alignment.centerRight),
              //color: Colors.cyan,
              elevation: 5.0,
              shape: new RoundedRectangleBorder(
                  borderRadius: new BorderRadius.circular(10.0)),
              //splashColor: Colors.blueGrey,
              //color: Theme.of(context).accentColor,
              //textColor: Theme.of(context).primaryColorLight,
              child: Text(
                'SUBMIT ',
                style: new CSSStyle().verdanaWhiteLight14(context),
              ),
              callback: () {
                Navigator.of(context).popUntil((route) => route.isFirst);
              },
              increaseWidthBy: 500.0,
              increaseHeightBy: 80.0,
            ),
          ),
        ),
      ],
    );
  }
  @override
  void dispose() {
    // Clean up the controller when the widget is removed from the
    // widget tree.
    titleController.dispose();
    super.dispose();
  }
  @override
  void initState() {
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller!);
    imagePicker!.init();
  }

  String? _validateDescription(String? user_name) {
    if (user_name!.isEmpty) {
      return "Please enter title";
    } else {
      title = user_name;
    }
  }

  void _openFileExplorer() async {
    // GetX reactive update - NO setState needed!
    _loadingPath = true;
    try {
      _directoryPath = '';
      _paths = (await FilePicker.platform.pickFiles(
        type: _pickingType,
        allowMultiple: _multiPick,
        allowedExtensions: (_extension?.isNotEmpty ?? false)
            ? _extension?.replaceAll(' ', '').split(',')
            : null,
      ))
          ?.files;
    } on PlatformException catch (e) {
    } catch (ex) {

    }
    if (!mounted) return;
    // GetX reactive update - NO setState needed!
    _loadingPath = false;
    _saving = true;

    _fileName = _paths != null ? _paths!.map((e) => e.name).toString() : '...';
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // GetX reactive update - NO setState needed!
      this._image = _image;
      _loadingPath = false;
      _saving = true;
    }
  }
}
