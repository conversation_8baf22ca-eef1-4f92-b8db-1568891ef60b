class ReceiptModel {
  int? _status;
  String? _message;
  int? _success;
  String? _errorMsg;
  List<Data>? _data;
  String? _errorDev;

  ReceiptModel(
      {int? status,
        String? message,
        int? success,
        String? errorMsg,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (errorMsg != null) {
      this._errorMsg = errorMsg;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get errorMsg => _errorMsg;
  set errorMsg(String? errorMsg) => _errorMsg = errorMsg;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  ReceiptModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _errorMsg = json['error_msg'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['error_msg'] = this._errorMsg;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _paymentId;
  String? _paymentDate;
  String? _paymentMode;
  String? _insNo;
  String? _insDate;
  String? _insBankname;
  String? _insBankbranch;
  String? _amount;
  String? _remarks;
  String? _tdsDeducted;
  String? _waivedOff;

  Data(
      {String? paymentId,
        String? paymentDate,
        String? paymentMode,
        String? insNo,
        String? insDate,
        String? insBankname,
        String? insBankbranch,
        String? amount,
        String? remarks,
        String? tdsDeducted,
        String? waivedOff}) {
    if (paymentId != null) {
      this._paymentId = paymentId;
    }
    if (paymentDate != null) {
      this._paymentDate = paymentDate;
    }
    if (paymentMode != null) {
      this._paymentMode = paymentMode;
    }
    if (insNo != null) {
      this._insNo = insNo;
    }
    if (insDate != null) {
      this._insDate = insDate;
    }
    if (insBankname != null) {
      this._insBankname = insBankname;
    }
    if (insBankbranch != null) {
      this._insBankbranch = insBankbranch;
    }
    if (amount != null) {
      this._amount = amount;
    }
    if (remarks != null) {
      this._remarks = remarks;
    }
    if (tdsDeducted != null) {
      this._tdsDeducted = tdsDeducted;
    }
    if (waivedOff != null) {
      this._waivedOff = waivedOff;
    }
  }

  String? get paymentId => _paymentId;
  set paymentId(String? paymentId) => _paymentId = paymentId;
  String? get paymentDate => _paymentDate;
  set paymentDate(String? paymentDate) => _paymentDate = paymentDate;
  String? get paymentMode => _paymentMode;
  set paymentMode(String? paymentMode) => _paymentMode = paymentMode;
  String? get insNo => _insNo;
  set insNo(String? insNo) => _insNo = insNo;
  String? get insDate => _insDate;
  set insDate(String? insDate) => _insDate = insDate;
  String? get insBankname => _insBankname;
  set insBankname(String? insBankname) => _insBankname = insBankname;
  String? get insBankbranch => _insBankbranch;
  set insBankbranch(String? insBankbranch) => _insBankbranch = insBankbranch;
  String? get amount => _amount;
  set amount(String? amount) => _amount = amount;
  String? get remarks => _remarks;
  set remarks(String? remarks) => _remarks = remarks;
  String? get tdsDeducted => _tdsDeducted;
  set tdsDeducted(String? tdsDeducted) => _tdsDeducted = tdsDeducted;
  String? get waivedOff => _waivedOff;
  set waivedOff(String? waivedOff) => _waivedOff = waivedOff;

  Data.fromJson(Map<String, dynamic> json) {
    _paymentId = json['payment_id'];
    _paymentDate = json['payment_date'];
    _paymentMode = json['payment_mode'];
    _insNo = json['ins_no'];
    _insDate = json['ins_date'];
    _insBankname = json['ins_bankname'];
    _insBankbranch = json['ins_bankbranch'];
    _amount = json['amount'];
    _remarks = json['remarks'];
    _tdsDeducted = json['tds_deducted'];
    _waivedOff = json['waived_off'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['payment_id'] = this._paymentId;
    data['payment_date'] = this._paymentDate;
    data['payment_mode'] = this._paymentMode;
    data['ins_no'] = this._insNo;
    data['ins_date'] = this._insDate;
    data['ins_bankname'] = this._insBankname;
    data['ins_bankbranch'] = this._insBankbranch;
    data['amount'] = this._amount;
    data['remarks'] = this._remarks;
    data['tds_deducted'] = this._tdsDeducted;
    data['waived_off'] = this._waivedOff;
    return data;
  }
}
