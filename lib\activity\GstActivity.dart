import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/GstFragment.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsFragment.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class GstActivity extends StatefulWidget {
  String? taskId = "";
  String? taskName = "";
  String? task_type = "";
  GstActivity({Key? key, this.taskName, this.task_type, this.taskId})
      : super(key: key);
  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return GstActivityState();
  }
}

class GstActivityState extends State<GstActivity> {
  // GetX reactive variables
  var taskIdObs = ''.obs;
  var taskNameObs = ''.obs;
  var taskTypeObs = ''.obs;

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        actions: [
          //   IconButton(icon: Icon(Icons.notifications_none), onPressed: () {}),
        ],
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          widget.taskName.toString(),
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),

      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/

      body: GstFragment(
        taskId: widget.taskId.toString(),
        task_type: widget.task_type.toString(),
        taskName: widget.taskName.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
