name: erpcacustomer
description: A new Flutter application.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#development
version: 1.0.32+33

environment:
  sdk: ">=2.12.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  version: ^3.0.0
  package_info_plus:
  cupertino_icons: ^1.0.4
  shared_preferences: ^2.0.11
  flutter_local_notifications: ^19.2.1
  image_cropper: ^9.1.0
  image_picker: ^1.1.2  # ✅ UPDATED: Better security and performance
  url_launcher: ^6.0.17
  connectivity_plus: ^5.0.2
  flutter_spinkit: ^5.1.0
  sqflite: ^2.0.1
  modal_progress_hud_nsn: ^0.5.1
  path_provider: ^2.0.8
  gradient_widgets: ^0.6.0
  font_awesome_flutter: ^10.8.0  # ✅ UPDATED: Latest icons
  flutter_speed_dial: ^7.0.0  # ✅ UPDATED: Better performance
  crypto: ^3.0.1
  flutter_launcher_icons: ^0.9.2
  adobe_xd: ^2.0.0+1
  percent_indicator: ^3.4.0
  # ✅ REMOVED: datetime_picker_formfield (discontinued package)
  intl: ^0.18.1
  mobile_number: ^3.0.0
  background_app_bar: ^2.0.0
  flutter_downloader: ^1.10.4
  flutter_staggered_grid_view: ^0.4.1
  flutter_mailer: ^3.0.0
  file_picker:
  whatsapp_unilink: ^2.0.0
  flutter_mobx: ^2.0.2
  flutter_email_sender : ^7.0.0
  webview_flutter: ^4.13.0
  dio: ^5.8.0+1  # ✅ SECURITY FIX: Updated from 4.0.4 to fix vulnerabilities
  http: ^1.4.0
  overlay_support: ^2.1.0
  #share: ^2.0.4
  share_plus: ^11.0.0
  permission_handler: ^12.0.0+1
  fluttertoast: ^8.2.12
  #flutter_share: ^2.0.0
  #share_plus: ^4.5.3
  #share_extend: ^2.0.0
  webview_flutter_android: ^4.7.0
  table_calendar: ^3.0.0
  razorpay_flutter: 1.4.0
  autocomplete_textfield: ^2.0.1
  otpless_flutter: 2.2.4
  flutter_svg:
  get: ^4.6.6

dev_dependencies:
  flutter_test:
    sdk: flutter

flutter_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/atms_logo.jpg"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/images/dashboard/
    - assets/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
fonts:
  - family: FontsFree-Net-SFProText
    fonts:
      - asset: fonts/FontsFree-Net-SFProText-Regular-1.ttf
        weight : 400
      - asset: fonts/FontsFree-Net-SFProText-RegularItalic.ttf
        style: italic
        weight : 400
      - asset: fonts/FontsFree-Net-SFProText-Semibold-1.ttf
        style: bold
        weight : 700
      - asset: fonts/FontsFree-Net-SFProText-Medium-1.ttf
        weight : 500
      - asset: fonts/FontsFree-Net-SFProText-Light.ttf
        weight : 300
      - asset: fonts/FontsFree-Net-SFProText-LightItalic.ttf
        style: italic
        weight : 300

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
