import 'dart:async';
import 'dart:convert';
import 'dart:io' as Io;
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/controller/TaskController.dart';
import 'package:erpcacustomer/model/DocRequiredModel.dart';
import 'package:erpcacustomer/model/DocumentRequiredDeleteModel.dart';
import 'package:erpcacustomer/model/_TaskInfo.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:path_provider/path_provider.dart';
import 'package:get/get.dart';

class DocRequiredFragment extends StatefulWidget {
  String? taskId = "";
  String? task_type = "";

  DocRequiredFragment({Key? key, this.taskId, this.task_type}) : super(key: key);

  @override
  DocRequiredFragmentState createState() {
    return new DocRequiredFragmentState();
  }
}

class DocRequiredFragmentState extends State<DocRequiredFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var loadingPathObs = false.obs;
  var savingObs = false.obs;

  // Original variables (keeping for compatibility)
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  String _localPath = '';
  String document_primary_key_id = "";
  bool _loadingPath = false;
  String _directoryPath = '';
  List<PlatformFile>? _paths;
  FileType _pickingType = FileType.any;
  bool _multiPick = false;
  String _extension = '';
  String _fileName = '';
  bool _saving = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();
  var image;
  AnimationController? _controller;
  ImagePickerHandler? imagePicker;
  Io.File? _image;
  List<TaskInfo> _tasks = [];
  static bool debug = true;

  @override
   initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
    // Removed incorrect FlutterDownloader callback registration (should only be in main.dart)
    _prepareSaveDir();
  }

  static void downloadCallback(
      String id, int status, int progress) {
    if (debug) {

    }
    final SendPort? send =
        IsolateNameServer.lookupPortByName('downloader_send_port');
    send!.send([id, status, progress]);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    _media = MediaQuery.of(context).size;

    children.add(_buildBackground());
    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
          primaryColor: PrimaryColor, fontFamily: 'GlacialIndifference'),
      home: Stack(
        children: children,
      ),
    );
  }

  showAlertDialog(BuildContext context, String textName) {
    AlertDialog alert = AlertDialog(
      content: Container(
        height: 70,
        child: new Column(
          children: [
            CircularProgressIndicator(),
            Container(
                margin: EdgeInsets.only(left: 5, right: 5, top: 15),
                child: Text(
                  textName,
                  style: new CSSStyle().poppinsBlackRegular14(context),
                )),
          ],
        ),
      ),
    );
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  Widget _buildBackground() {
    return Scaffold(
      key: _scaffoldKey,
      body: Stack(children: <Widget>[
        //Above card
        NotificationListener<OverscrollIndicatorNotification>(
          onNotification: (overscroll) {
            overscroll.disallowIndicator();
            return true;
          },
          child: FutureBuilder<DocRequiredModel>(
            future: postDocRequiredCall(
                Constants.ACC_ID, Constants.USER_ID, widget.taskId!, context),
            builder: (context, snapshot) {
              if (snapshot.connectionState == ConnectionState.done && snapshot.hasData) {
                return Material(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Row(
                              children: [
                                Container(
                                  width: 5,
                                  height: 30,
                                  color: Colors.grey[500],
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(left: 8.0),
                                  child: Container(
                                    width: 320,
                                    child: Text(
                                      "Below are the list of documents we need from you to complete your project.",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12Height5(
                                          context),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        ListView.separated(
                          separatorBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13),
                              child: Divider(),
                            );
                          },
                          physics: ClampingScrollPhysics(),
                          shrinkWrap: true,
                          padding: EdgeInsets.zero,
                          itemCount: snapshot.data!.data!.length,
                          itemBuilder: (BuildContext context, int index) {
                            return _gstBottomValue(
                                index, snapshot.data!.data![index]);
                          },
                        ),
                        /*   ListView(
                              padding:
                                  const EdgeInsets.symmetric(vertical: 16.0),
                              children: snapshot.data.data.map((item) {
                                if (item.task == null) {
                                  return Center(
                                      child: Container(
                                          color: Colors.white,
                                          child: new MyUtils()
                                              .kLoadingWidget(context)));
                                } else {
                                  return DownloadItem(
                                    data: item,
                                    onItemClick: (task) {
                                      _openDownloadedFile(task).then((success) {
                                        if (!success) {
                                          Scaffold.of(context).showSnackBar(
                                              SnackBar(
                                                  content: Text(
                                                      'Cannot open this file')));
                                        }
                                      });
                                    },
                                    onActionClick: (task) {
                                      if (task.status ==
                                          DownloadTaskStatus.undefined) {
                                        _requestDownload(task);
                                      } else if (task.status ==
                                          DownloadTaskStatus.running) {
                                        _pauseDownload(task);
                                      } else if (task.status ==
                                          DownloadTaskStatus.paused) {
                                        _resumeDownload(task);
                                      } else if (task.status ==
                                          DownloadTaskStatus.complete) {
                                        //_delete(task);
                                      } else if (task.status ==
                                          DownloadTaskStatus.failed) {
                                        _retryDownload(task);
                                      }
                                    },
                                  );
                                }
                              }).toList(),
                            ),*/
                      ],
                    ),
                  ),
                );
              }
              if(snapshot.connectionState == ConnectionState.waiting){
                return Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
              }
              else  {
                return Center(
                  child: Container(
                    child: Text('No document(s) available!',
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: 'GlacialIndifference',
                      ),
                    ),
                  ),
                );
              }

            },
          ),
        ),
        // Positioned to take only AppBar size
      ]),
    );
  }

   DocRequiredCall(String acc_id, String user_id,
      String taskId, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await postDocRequiredApi(acc_id, user_id, taskId, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        DocRequiredModel currentParsedResponse =
            DocRequiredModel.fromJson(responseJson);
        return currentParsedResponse;
      } /*else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }*/
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<DocRequiredModel> postDocRequiredCall(String acc_id, String user_id,
      String taskId, BuildContext context) async {
    return await DocRequiredCall(acc_id, user_id, taskId, context);
  }

  /*Widget _gstBottomValue(Data data) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        color: Color(new CommonColor().white_Color),
        elevation: 3,
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 15, right: 13),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    (data.wcDocName != null)
                        ? Expanded(
                            flex: 3,
                            child: Text(
                              data.wcDocName,
                              style:
                                  new CSSStyle().poppinsBlackRegular15(context),
                            ),
                          )
                        : Expanded(
                            flex: 3,
                            child: Text(
                              "N/A",
                              style:
                                  new CSSStyle().poppinsBlackRegular20(context),
                            ),
                          ),
                    */ /*   Expanded(
                      flex: 2,
                      child: Container(
                          decoration: new BoxDecoration(
                            color: Colors.green,
                            shape: BoxShape.rectangle,
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.only(
                                left: 15.0, right: 15, top: 5, bottom: 5),
                            child: (data.docStatus != null)
                                ? Text(
                                    data.docStatus,
                                    overflow: TextOverflow.ellipsis,
                                    style: new CSSStyle()
                                        .poppinsWhiteRegular12(context),
                                  )
                                : Text(
                                    "N/A",
                                    style: new CSSStyle()
                                        .poppinsWhiteRegular12(context),
                                  ),
                          )),
                    )*/ /*
                  ],
                ),
              ),
              */ /*  Padding(
                padding: const EdgeInsets.only(left: 15.0, top: 15),
                child: Text(
                  " data.uploadedOn",
                  style: new CSSStyle().poppinsBlackRegular12(context),
                ),
              ),*/ /*
              */ /*    Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 15),
                child: Row(
                  children: [
                    Icon(
                      Icons.picture_as_pdf,
                      color: Colors.red,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text(
                        "Aadhar Card for the GST-3B filing",
                        style: new CSSStyle().poppinsBlackRegular12(context),
                      ),
                    ),
                  ],
                ),
              ),*/ /*
              Padding(
                padding: const EdgeInsets.only(
                    left: 13.0, top: 15, right: 13, bottom: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    (data.isComplete != "1")
                        ? Text(
                            "Upload",
                            style: new CSSStyle().poppinsBlueRegular12(context),
                          )
                        : Text(""),
                    (data.filePath != "")
                        ? Text(
                            "Email",
                            style: new CSSStyle().poppinsRedRegular12(context),
                          )
                        : Text(
                            "",
                            style: new CSSStyle().poppinsRedRegular12(context),
                          ),
                    (data.filePath != "")
                        ? Text(
                            "Whatsapp",
                            style:
                                new CSSStyle().poppinsGreenRegular12(context),
                          )
                        : Text(
                            "",
                            style:
                                new CSSStyle().poppinsGreenRegular12(context),
                          ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }*/

  void _openFileExplorer() async {
    // ✅ OPTIMIZED: GetX reactive state management
    _loadingPath = true;
    loadingPathObs.value = true;
    // setState(() => _loadingPath = true); // Removed
    try {
      _directoryPath = '';
      _paths = (await FilePicker.platform.pickFiles(
        type: _pickingType,
        allowMultiple: _multiPick,
        allowedExtensions: (_extension?.isNotEmpty ?? false)
            ? _extension?.replaceAll(' ', '').split(',')
            : null,
      ))
          !.files;
    } on PlatformException catch (e) {
    } catch (ex) {
    }
    if (!mounted) return;
    // ✅ OPTIMIZED: GetX reactive state management
    if (_paths!.isNotEmpty) {
      _loadingPath = false;
      _saving = true;
      loadingPathObs.value = false;
      savingObs.value = true;
      showAlertDialog(
          _scaffoldKey.currentContext!, "Please wait while uploading...");
    }
    workUploadRequiredCall(Constants.ACC_ID, Constants.USER_ID,
        document_primary_key_id, _paths!.first.path!, context);
    _fileName = _paths != null ? _paths!.map((e) => e.name).toString() : '...';
    // setState(() { ... }); // Removed
  }

  postWorkUploadRequiredCall(
      String acc_id,
      String user_id,
      String document_primary_key_id,
      String _image,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    final bytes = Io.File(_image).readAsBytesSync();

    String img64 = base64Encode(bytes);
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await workUploadRequiredApi(
          acc_id, user_id, document_primary_key_id, img64, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        DocRequiredModel currentParsedResponse =
            DocRequiredModel.fromJson(responseJson);
        // ✅ OPTIMIZED: GetX reactive state management
        Navigator.of(context, rootNavigator: true).pop();
        // setState(() { Navigator.of(context, rootNavigator: true).pop(); }); // Removed

        MyUtils.showOkDialog(
            context, "Success", responseJson['message'].toString());
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      // ✅ OPTIMIZED: GetX reactive state management
      _saving = false;
      savingObs.value = false;
      // setState(() { _saving = false; }); // Removed
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<DocRequiredModel> workUploadRequiredCall(
      String acc_id,
      String user_id,
      String document_primary_key_id,
      String _image,
      BuildContext context) async {
    return await postWorkUploadRequiredCall(acc_id, user_id, document_primary_key_id, _image, context);
  }

 _onLogoutPressed() {
    return showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text('Are you sure?'),
            content: new Text('Do you really want to delete file?'),
            actions: <Widget>[
              new GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("NO"),
                ),
              ),
              SizedBox(height: 16),
              new GestureDetector(
                onTap: () {
                  workDeleteRequiredCall(
                      Constants.ACC_ID,
                      Constants.USER_ID,
                      document_primary_key_id,
                      context,
                      _scaffoldKey.currentContext!);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("YES"),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  postWorkDeleteRequiredCall(
    String acc_id,
    String user_id,
    String document_primary_key_id,
    BuildContext context,
    BuildContext scaffolContext,
  ) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    // ✅ OPTIMIZED: GetX reactive state management
    Navigator.of(context).pop(true);
    showAlertDialog(scaffolContext, "Please wait while deleting...");
    // setState(() { ... }); // Removed


    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await workDeleteRequiredApi(
          acc_id, user_id, document_primary_key_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        DocumentRequiredDeleteModel currentParsedResponse =
            DocumentRequiredDeleteModel.fromJson(responseJson);
        // ✅ OPTIMIZED: GetX reactive state management
        Navigator.of(scaffolContext, rootNavigator: true).pop();
        // setState(() { Navigator.of(scaffolContext, rootNavigator: true).pop(); }); // Removed
        MyUtils.showOkDialog(
            context, "Success", responseJson['message'].toString());
        return currentParsedResponse;
      } else {
        // ✅ OPTIMIZED: GetX reactive state management
        Navigator.of(scaffolContext, rootNavigator: true).pop();
        // setState(() { Navigator.of(scaffolContext, rootNavigator: true).pop(); }); // Removed
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      // ✅ OPTIMIZED: GetX reactive state management
      _saving = false;
      savingObs.value = false;
      // setState(() { _saving = false; }); // Removed
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<DocumentRequiredDeleteModel> workDeleteRequiredCall(
      String acc_id,
      String user_id,
      String document_primary_key_id,
      BuildContext context,
      BuildContext scaffolContext,
      ) async {
    return await postWorkDeleteRequiredCall(acc_id, user_id, document_primary_key_id, context, scaffolContext);
  }

  Widget _gstBottomValue(int index, Data allData) {
    {
      return GestureDetector(
        onTap: () {
          /*  Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => DocumentDetailsSecondActivity(
                      asst_year: allData.asstYear,
                    )),
          );*/
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 13.0, right: 13, top: 5, bottom: 5),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Icon(
                        Icons.insert_drive_file,
                        color: Color(new CommonColor().green_light_Color),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 230,
                              child: Text(
                                allData.wcDocName.toString(),
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                            /*  Text(
                              allData.availableDocuments,
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),*/
                          ],
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      (allData.isComplete != "1" &&
                              allData.filePath == "" &&
                              (widget.task_type != "2"))
                          ? GestureDetector(
                              onTap: () async {
                                //   imagePicker.showDialog(context);
                                document_primary_key_id =
                                    allData.documentPrimaryKeyId.toString();
                                _openFileExplorer();
                              },
                              child: Icon(
                                Icons.cloud_upload,
                                size: 20,
                                color:
                                    Color(new CommonColor().green_light_Color),
                              ),
                            )
                          : Container(),
                      (allData.isComplete != "1" &&
                              allData.filePath == "" &&
                              (widget.task_type != "2"))
                          ? GestureDetector(
                              onTap: () async {
                                //   imagePicker.showDialog(context);
                                document_primary_key_id =
                                    allData.documentPrimaryKeyId.toString();
                                imagePicker!.showDialog(context);
                              },
                              child: Icon(
                                Icons.camera_alt,
                                size: 20,
                                color:
                                    Color(new CommonColor().green_light_Color),
                              ),
                            )
                          : Container(),
                      (allData.isComplete != "0")
                          ? Icon(
                              Icons.check,
                              size: 20,
                              color: Color(new CommonColor().green_Color),
                            )
                          : Container(),
                      (allData.filePath != "" &&
                              allData.isComplete != "1" &&
                              (widget.task_type != "2"))
                          ? GestureDetector(
                              onTap: () {
                                document_primary_key_id =
                                    allData.documentPrimaryKeyId.toString();
                                _onLogoutPressed();
                              },
                              child: Icon(
                                Icons.clear,
                                size: 20,
                                color: Color(new CommonColor().red_Color),
                              ),
                            )
                          : Container(),
                      (allData.filePath != "")
                          ? GestureDetector(
                              onTap: () async {
                                MyUtils().getDownloadUrl1(allData.filePath!);
                                },
                              child: Icon(
                                Icons.image_search_outlined,
                                size: 20,
                                color:
                                    Color(new CommonColor().blue_lighter_Color),
                              ),
                            )
                          : Container(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

  void _requestDownload(TaskInfo task) async {
    task.taskId = (await FlutterDownloader.enqueue(
            url: task.link!,
            headers: {"auth": "test_for_sql_encoding"},
            savedDir: _localPath,
            showNotification: true,
            openFileFromNotification: true)
        .whenComplete(() {
      _openDownloadedFile(task).then((success) {
        if (!success) {
          ScaffoldMessenger.of(context).showSnackBar(SnackBar(content:  Text('Cannot open this file')));
        }
      });
    }))!;
  }

  void _cancelDownload(TaskInfo task) async {
    await FlutterDownloader.cancel(taskId: task.taskId);
  }

  void _pauseDownload(TaskInfo task) async {
    await FlutterDownloader.pause(taskId: task.taskId);
  }

  void _resumeDownload(TaskInfo task) async {
    String? newTaskId = await FlutterDownloader.resume(taskId: task.taskId);
    task.taskId = newTaskId!;
  }

  void _retryDownload(TaskInfo task) async {
    String? newTaskId = await FlutterDownloader.retry(taskId: task.taskId);
    task.taskId = newTaskId!;
  }

  Future<bool> _openDownloadedFile(TaskInfo task) {
    if (task != null) {
      return FlutterDownloader.open(taskId: task.taskId);
    } else {
      return Future.value(false);
    }
  }

  Future<void> _prepareSaveDir() async {
    _localPath = (await _findLocalPath()).toString() + Platform.pathSeparator + 'Download';

    final savedDir = Directory(_localPath);
    bool hasExisted = await savedDir.exists();
    if (!hasExisted) {
      savedDir.create();
    }
  }

  Future<String?> _findLocalPath() async {
    final directory = (Platform.isAndroid)
        ? await getExternalStorageDirectory()
        : await getApplicationDocumentsDirectory();
    return directory?.path;
  }

  @override
  userImage(Io.File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // ✅ OPTIMIZED: GetX reactive state management
      this._image = _image;
      _loadingPath = false;
      _saving = true;
      loadingPathObs.value = false;
      savingObs.value = true;
      showAlertDialog(
          _scaffoldKey.currentContext!, "Please wait while uploading...");
      workUploadRequiredCall(Constants.ACC_ID, Constants.USER_ID,
          document_primary_key_id, _image.path, context);
      // setState(() { ... }); // Removed
    }
  }
}

class DownloadItem extends StatelessWidget {
  final Data? data;
  final Function(TaskInfo)? onItemClick;
  final Function(TaskInfo)? onActionClick;

  DownloadItem({this.data, this.onItemClick, this.onActionClick});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 16.0, right: 8.0),
      child: InkWell(
        onTap: data!.task!.status == DownloadTaskStatus.complete
            ? () {
                onItemClick!(data!.task!);
              }
            : null,
        child: Stack(
          children: <Widget>[
            Container(
              width: double.infinity,
              height: 64.0,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  Expanded(
                    child: Text(
                      data!.wcDocName.toString(),
                      maxLines: 1,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0),
                    child: _buildActionForTask(data!.task!),
                  ),
                ],
              ),
            ),
            data!.task!.status == DownloadTaskStatus.running ||
                    data!.task!.status == DownloadTaskStatus.paused
                ? Positioned(
                    left: 0.0,
                    right: 0.0,
                    bottom: 0.0,
                    child: LinearProgressIndicator(
                      value: data!.task!.progress / 100,
                    ),
                  )
                : Container()
          ].toList(),
        ),
      ),
    );
  }

  Widget? _buildActionForTask(TaskInfo task) {
    if (task.status == DownloadTaskStatus.undefined) {
      return RawMaterialButton(
        onPressed: () {
          onActionClick!(task);
        },
        child: Icon(Icons.file_download),
        shape: CircleBorder(),
        constraints: BoxConstraints(minHeight: 32.0, minWidth: 32.0),
      );
    } else if (task.status == DownloadTaskStatus.running) {
      return RawMaterialButton(
        onPressed: () {
          onActionClick!(task);
        },
        child: Icon(
          Icons.pause,
          color: Colors.red,
        ),
        shape: CircleBorder(),
        constraints: BoxConstraints(minHeight: 32.0, minWidth: 32.0),
      );
    } else if (task.status == DownloadTaskStatus.paused) {
      return RawMaterialButton(
        onPressed: () {
          onActionClick!(task);
        },
        child: Icon(
          Icons.play_arrow,
          color: Colors.green,
        ),
        shape: CircleBorder(),
        constraints: BoxConstraints(minHeight: 32.0, minWidth: 32.0),
      );
    } else if (task.status == DownloadTaskStatus.complete) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text(
            'Ready',
            style: TextStyle(color: Colors.green),
          ),
          RawMaterialButton(
            onPressed: () {
              onActionClick!(task);
            },
            child: Icon(
              Icons.delete_forever,
              color: Colors.red,
            ),
            shape: CircleBorder(),
            constraints: BoxConstraints(minHeight: 32.0, minWidth: 32.0),
          )
        ],
      );
    } else if (task.status == DownloadTaskStatus.canceled) {
      return Text('Canceled', style: TextStyle(color: Colors.red));
    } else if (task.status == DownloadTaskStatus.failed) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Text('Failed', style: TextStyle(color: Colors.red)),
          RawMaterialButton(
            onPressed: () {
              onActionClick!(task);
            },
            child: Icon(
              Icons.refresh,
              color: Colors.green,
            ),
            shape: CircleBorder(),
            constraints: BoxConstraints(minHeight: 32.0, minWidth: 32.0),
          )
        ],
      );
    } else if (task.status == DownloadTaskStatus.enqueued) {
      return Text('Pending', style: TextStyle(color: Colors.orange));
    } else {
      return null;
    }
  }
}
