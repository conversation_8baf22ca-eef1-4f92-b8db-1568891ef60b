import 'dart:io' as io;
import 'dart:ui';

import 'package:autocomplete_textfield/autocomplete_textfield.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/RequiredDocumentsActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/ServiceRequestFromSubmissionModel.dart';
import 'package:erpcacustomer/model/WorkCategoryModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:intl/intl.dart';

class NewServicesFragment extends StatefulWidget {
  NewServicesFragment({Key? key}) : super(key: key);

  @override
  NewServicesFragmentState createState() {
    return new NewServicesFragmentState();
  }
}

class NewServicesFragmentState extends State<NewServicesFragment> {
  // GetX reactive variables
  var hasCardObs = false.obs;
  var dropdownStatusValueObs = "Select Kit".obs;
  var checkedValueObs = false.obs;
  var descriptionObs = ''.obs;
  var currentDateObs = DateTime.now().obs;
  var selectedTimeObs = TimeOfDay.now().obs;

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  final format = DateFormat("dd-MM-yyyy");
  final initialValue = DateTime.now();
  final initialValueTime = DateTime.now();
  final formatTime = DateFormat("HH:mm");
  String dropdownStatusValue = "Select Kit";
  List<String> statusType = ["Select Kit", "Kit A", "Kit B", "Kit C"];
  bool checkedValue = false;
  String description = "";
  DateTime currentDate = DateTime.now();
  TextEditingController dobController = TextEditingController();
  TextEditingController timeController = TextEditingController();
  TimeOfDay selectedTime = TimeOfDay.now();

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  // ✅ OPTIMIZED: GetX reactive variables to prevent continuous refreshing
  var savingObs = false.obs;
  var showLoaderObs = false.obs;
  var selectedDataObs = ''.obs;

  // Original variables (keeping for compatibility)
  String selectedData = "";
  TextEditingController communiacationController = TextEditingController();
  TextEditingController serviceController = TextEditingController();
  TextEditingController descriptionController = TextEditingController();
  List<String> suggestions = [];
  List<String> suggestionsCommunication = [
    "Email",
    "In Person Meet",
    "Phone Call",
    "Online Call"
  ];
  bool _saving = false;
  bool showLoader = false;
  var _formKey = GlobalKey<FormState>();
  GlobalKey<AutoCompleteTextFieldState<String>> key = GlobalKey();
  GlobalKey<AutoCompleteTextFieldState<String>> key1 = GlobalKey();

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    // List<Widget> children = new List();
    _media = MediaQuery.of(context).size;
    //children.add(_buildBackground());
    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Stack(
        children: [
          _buildBackground(),
          circularIndicator(),
        ],
      ),
    );
  }

  Widget _buildBackground() {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle(
          // Status bar color
          statusBarColor: Colors.white,

          // Status bar brightness (optional)
          statusBarIconBrightness: Brightness.dark, // For Android (dark icons)
          statusBarBrightness:
              io.Platform.isAndroid ? Brightness.light : Brightness.dark,
        ),
        leading: IconButton(
            icon: Icon(Icons.arrow_back,color: Colors.white,),
            onPressed: () {
              moveToLastScreen();
            }),
        actions: <Widget>[
          /*   FlatButton(
            textColor: Colors.white,
            onPressed: () {},
            child: Text(
              widget.count,
              style: new CSSStyle().poppinsWhiteRegular14(context),
            ),
            shape: CircleBorder(side: BorderSide(color: Colors.transparent)),
          ),*/
        ],
        title: Text(
          "Request New Service",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),
      body: FutureBuilder<WorkCategoryModel>(
        future: getExpenseCategoriesListCall(context),
        builder: (context, snapshot) {
          double width = MediaQuery.of(context).size.width;

          if (snapshot.hasData) {

            return Padding(
                  padding: EdgeInsets.only(
                      bottom: MediaQuery.of(context).viewInsets.bottom),
                  child: Form(
                    key: _formKey,
                    child: Container(
                      color: Colors.white,
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(12.0),
                              child: Container(
                                width: width,
                                color: Colors.grey[100],
                                child: Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.max,
                                    children: [
                                      Row(
                                        children: [
                                          Padding(
                                            padding: const EdgeInsets.all(3.0),
                                            child: Icon(
                                              Icons.wb_sunny_outlined,
                                              size: 20,
                                              color: Colors.blueAccent,
                                            ),
                                          ),
                                          Text(
                                            "Tips",
                                            style: new CSSStyle()
                                                .poppinsBlackBold15(context),
                                          ),
                                        ],
                                      ),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(top: 8.0),
                                        child: Text(
                                          "Put here your request of new service required to be done. You can share the documents required for task in next step. You can later also share the document from Service Request section of app.",
                                          style: new CSSStyle()
                                              .poppinsGreyRegular14Height(
                                                  context),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 15, right: 15),
                              /* child:  AutoCompleteTextField<String>(
                                clearOnSubmit: false,
                                submitOnSuggestionTap: true,
                                key: key,
                                keyboardType: TextInputType.text,
                                controller: serviceController,
                                suggestions: suggestions,
                                style: TextStyle(
                                  fontSize: 18,
                                ),
                                decoration: InputDecoration(
                                  labelText: "Select Service",
                                ),
                                itemFilter: (item, query) {
                                  return item
                                      .toLowerCase()
                                      .contains(query.toLowerCase());
                                },
                                itemBuilder: (context, item) {
                                  // ui for the autocomplete row
                                  return Padding(
                                    padding: const EdgeInsets.only(
                                        top: 5.0, bottom: 5, left: 2),
                                    child: Text(
                                      item,
                                      style: new CSSStyle()
                                          .poppinsBlackRegular12(context),
                                    ),
                                  );
                                },
                              ), */
                              child: AutoCompleteTextField<String>(
  clearOnSubmit: false,
  submitOnSuggestionTap: true,
  key: key,
  keyboardType: TextInputType.text,
  controller: serviceController,
  suggestions: suggestions,
  style: TextStyle(fontSize: 18),
  decoration: InputDecoration(
    labelText: "Select Service *",
  ),
  itemFilter: (item, query) {
    return item.toLowerCase().contains(query.toLowerCase());
  },
  itemSorter: (a, b) => a.toLowerCase().compareTo(b.toLowerCase()),  // required
  itemSubmitted: (item) {
    // Called when user selects a suggestion
    // ✅ OPTIMIZED: GetX reactive update - NO setState needed!
    serviceController.text = item;
    selectedDataObs.value = item;
    selectedData = item; // Keep for compatibility
    // setState(() { serviceController.text = item; }); // Removed
  },  // required
  itemBuilder: (context, item) {
    return Padding(
      padding: const EdgeInsets.only(top: 5.0, bottom: 5, left: 2),
      child: Text(
        item,
        style: new CSSStyle().poppinsBlackRegular12(context),
      ),
    );
  },
),

                            ),
                            Container(
                              margin: EdgeInsets.only(left: 15, right: 15),
                              child: TextFormField(
                                validator: _validateDescription,
                                keyboardType: TextInputType.multiline,
                                maxLines: io.Platform.isAndroid ? 6 : 2,
                                decoration: InputDecoration(
                                  labelText: "Describe Your Requirement *",
                                ),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(
                                  left: 15,
                                  right: 15,
                                  top: io.Platform.isAndroid ? 15 : 0),
                              child: InkWell(
                                onTap: () {
                                  _selectEndDate(context);
                                },
                                child: IgnorePointer(
                                  child: TextFormField(
                                    controller: dobController,
                                    decoration: InputDecoration(
                                      labelText: "Preferred Date To Connect",
                                      counterText: "",

                                      labelStyle: CSSStyle()
                                          .poppinsGreyRegular15(context),
                                      errorStyle: const TextStyle(
                                          color: Colors.red, fontSize: 15),
                                      //hintText: '',
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Container(
                              margin:
                                  const EdgeInsets.only(left: 15, right: 15),
                              child: InkWell(
                                onTap: () {
                                  _selectTime(context);
                                },
                                child: IgnorePointer(
                                  child: TextFormField(
                                    controller: timeController,
                                    decoration: InputDecoration(
                                      labelText: "Preferred Time To Connect",
                                      counterText: "",

                                      labelStyle: CSSStyle()
                                          .poppinsGreyRegular15(context),
                                      errorStyle: const TextStyle(
                                          color: Colors.red, fontSize: 15),
                                      //hintText: '',
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            Container(
                              margin: EdgeInsets.only(left: 15, right: 15),
                              /* child: AutoCompleteTextField<String>(
                                clearOnSubmit: false,
                                submitOnSuggestionTap: true,
                                key: key1,
                                keyboardType: TextInputType.text,
                                controller: communiacationController,
                                suggestions: suggestionsCommunication,
                                style: TextStyle(
                                  fontSize: 18,
                                ),
                                decoration: InputDecoration(
                                  labelText: "Communication Mode",
                                ),
                                itemFilter: (item, query) {
                                  return item
                                      .toLowerCase()
                                      .contains(query.toLowerCase());
                                },
                                itemBuilder: (context, item) {
                                  // ui for the autocomplete row
                                  return Padding(
                                    padding: const EdgeInsets.only(
                                        top: 5.0, bottom: 5, left: 2),
                                    child: Text(
                                      item,
                                      style: new CSSStyle()
                                          .poppinsBlackRegular12(context),
                                    ),
                                  );
                                },
                              ), */
                              child: AutoCompleteTextField<String>(
  clearOnSubmit: false,
  submitOnSuggestionTap: true,
  key: key1,
  keyboardType: TextInputType.text,
  controller: communiacationController,
  suggestions: suggestionsCommunication,
  style: TextStyle(fontSize: 18),
  decoration: InputDecoration(
    labelText: "Communication Mode",
  ),
  itemFilter: (item, query) {
    return item.toLowerCase().contains(query.toLowerCase());
  },
  itemSorter: (a, b) => a.toLowerCase().compareTo(b.toLowerCase()),  // required
  itemSubmitted: (item) {
    // ✅ OPTIMIZED: GetX reactive update - NO setState needed!
    communiacationController.text = item;
    // setState(() { communiacationController.text = item; }); // Removed
  },  // required
  itemBuilder: (context, item) {
    return Padding(
      padding: const EdgeInsets.only(top: 5.0, bottom: 5, left: 2),
      child: Text(
        item,
        style: new CSSStyle().poppinsBlackRegular12(context),
      ),
    );
  },
),

                            ),
                            _addAccessMyAccountButtonUi(snapshot.data!.data!)
                          ],
                        ),
                      ),
                    ),
                  ),
                );
          } else {
            return Center(
                  child: Container(
                      color: Colors.white,
                      child: new MyUtils().kLoadingWidget(context)));
          }
        },
      ),
    );
  }

  Widget circularIndicator() {
    return Visibility(
      visible: false,
      child: Center(
          child: Container(
              color: Colors.white,
              child: new MyUtils().kLoadingWidget(context))),
    );
  }

  String? _validateDescription(String? user_name) {
    if (user_name!.isEmpty) {
      MyUtils.showOkDialog(context, "Oops!", "Please select service from list");
      return "Please enter description";
    } else {
      description = user_name;
    }
  }

  Widget _addPriceUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: Padding(
        padding: EdgeInsets.only(
          top: 20.0,
        ),
        child: Autocomplete<String>(
          optionsBuilder: (TextEditingValue textEditingValue) {
            if (textEditingValue.text == '') {
              return const Iterable<String>.empty();
            }
            return suggestions!.where((String option) {
              return option.contains(textEditingValue.text.toLowerCase());
            });
          },
          onSelected: (String selection) {
            selectedData = selection;
          },
        ),
        /* SimpleAutoCompleteTextField(
          suggestions: suggestions,
          submitOnSuggestionTap: true,
          keyboardType: TextInputType.text,
          controller: priceController,
          suggestionsAmount: 50,
          decoration: InputDecoration(
            labelText: "Service Looking For",
          ),
          textChanged: (text) {
            return priceController = text as TextEditingController;
          },
          textSubmitted: (text) => setState(() {
            if (text != "") {
              priceController = text as TextEditingController;
            }
          }),
        )*/
/*
            AutoCompleteTextField<String>(
          key: key,
          clearOnSubmit: false,
          submitOnSuggestionTap: true,
          keyboardType: TextInputType.text,
          controller: priceController,
          suggestions: suggestions,
          style: new CSSStyle().poppinsBlackRegular15(context),
          decoration: InputDecoration(
            labelText: "Service Looking For",
          ),
          itemFilter: (item, query) {
            return item.toLowerCase().contains(query.toLowerCase());
          },
          itemBuilder: (context, item) {
            // ui for the autocomplete row
            return Padding(
              padding: const EdgeInsets.only(top: 5.0, bottom: 5, left: 2),
              child: Text(
                item,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            );
          },
        ),
*/
      ),
    );
  }

  postGetExpenseCategoriesListCall(
      BuildContext context) async {
    String userToken = "";
    bool isFirstTime = true;

    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await workCategoryListApi(Constants.ACC_ID, context);
      int flag = responseJson["success"];

      //  String getJson=  getJsonFromJWT(userToken);
      //  Map<String, dynamic> tokenData =  parseJwt(userToken);
      // ✅ OPTIMIZED: GetX reactive state management
      savingObs.value = false;
      _saving = false; // Keep for compatibility
      // setState(() { _saving = false; }); // Removed
      if (flag == 1) {
        WorkCategoryModel parsedResponse =
            WorkCategoryModel.fromJson(responseJson);
        suggestions = [];
        for (int i = 0; i < parsedResponse.data!.length; i++) {
          suggestions.add(parsedResponse.data![i].workcategory.toString());
        }
        print('sugegstions $suggestions');
        return parsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<WorkCategoryModel> getExpenseCategoriesListCall(
      BuildContext context) async {
      return await postGetExpenseCategoriesListCall(context);
  }

  postServiceRequestFromSubmissionCall(
      String acc_id,
      String user_id,
      String workcategory_id,
      String work_description,
      String preferred_communication_mode,
      String preferred_date_time_to_call,
      BuildContext context) async {
    print(' $acc_id $user_id $workcategory_id $work_description $preferred_date_time_to_call work cat id $workcategory_id comm mode $preferred_communication_mode');
    String userToken = "";
    bool isFirstTime = true;

    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await serviceRequestFromSubmissionApi(
          acc_id,
          user_id,
          workcategory_id,
          work_description,
          preferred_communication_mode,
          preferred_date_time_to_call,
          context);
      int flag = responseJson["success"];
      print('service id ${responseJson["service_request_id"]}');
      //  String getJson=  getJsonFromJWT(userToken);
      //  Map<String, dynamic> tokenData =  parseJwt(userToken);
      // ✅ OPTIMIZED: GetX reactive state management
      savingObs.value = false;
      showLoaderObs.value = false;
      _saving = false; // Keep for compatibility
      showLoader = false; // Keep for compatibility
      // setState(() { _saving = false; }); // Removed
      // setState(() { showLoader = false; Navigator.pop(context); }); // Removed
      Navigator.pop(context);
      if (flag == 1) {
        ServiceRequestFromSubmissionModel currentParsedResponse =
            ServiceRequestFromSubmissionModel.fromJson(responseJson);

        Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => RequiredDocumentsActivity(
                  workcategoryId: workcategory_id,
                  service_request_id: currentParsedResponse.serviceRequestId)),
        );
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<WorkCategoryModel> serviceRequestFromSubmissionCall(
      String acc_id,
      String user_id,
      String workcategory_id,
      String work_description,
      String preferred_communication_mode,
      String preferred_date_time_to_call,
      BuildContext context) async {
    return await postServiceRequestFromSubmissionCall(acc_id, user_id, workcategory_id, work_description, preferred_communication_mode, preferred_date_time_to_call, context);
  }

  Future<void> _selectEndDate(BuildContext context) async {
    final DateTime? pickedDate = await showDatePicker(
        context: context,
        initialDate: currentDate,
        firstDate: DateTime(1900),
        lastDate: DateTime(2100));
    if (pickedDate != null && pickedDate != currentDate) {
      // ✅ OPTIMIZED: GetX reactive state management
      currentDate = pickedDate;
      var outputFormat = DateFormat('MM/dd/yyyy');
      var outputDate = outputFormat.format(currentDate);
      dobController.text = outputDate.toString();
      // setState(() { ... }); // Removed
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final TimeOfDay? picked_s = await showTimePicker(
        context: context,
        initialTime: selectedTime,
        builder: (BuildContext context, Widget? child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
            child: child!,
          );
        });

    if (picked_s != null && picked_s != selectedTime) {
      // ✅ OPTIMIZED: GetX reactive state management
      selectedTime = picked_s;
      final localizations = MaterialLocalizations.of(context);
      String formattedTimeOfDay = localizations.formatTimeOfDay(selectedTime);
      timeController.text = formattedTimeOfDay;
      // setState(() { ... }); // Removed
    }
  }

  _addAccessMyAccountButtonUi(List<Data> data) {
    var height = MediaQuery.of(context).size.height;
    var width = MediaQuery.of(context).size.width;
    return Padding(
      padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom, top: 15),
      child: Container(
        margin: EdgeInsets.only(left: 15, right: 15, bottom: 15),
        constraints: BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
        child: GradientButton(
          gradient: LinearGradient(colors: [
            Color(new CommonColor().erpca_blue_color),
            Color(new CommonColor().erpca_blue_color)
          ], begin: Alignment.centerLeft, end: Alignment.centerRight),
          //color: Colors.cyan,
          elevation: 5.0,
          shape: new RoundedRectangleBorder(
              borderRadius: new BorderRadius.circular(10.0)),
          //splashColor: Colors.blueGrey,
          //color: Theme.of(context).accentColor,
          //textColor: Theme.of(context).primaryColorLight,
          child: Text(
            'SUBMIT ',
            style: new CSSStyle().verdanaWhiteLight14(context),
          ),
          callback: () {
            String warkCategoryId = "";
            selectedData = serviceController.text.trim();
            for (int i = 0; i < data.length; i++) {
              if (selectedData.toLowerCase() ==
                  data[i].workcategory!.toLowerCase()) {
                warkCategoryId = data![i].workcategoryId.toString();
              }
            }

            // ✅ OPTIMIZED: GetX reactive state management
            if (_formKey.currentState!.validate()) {
              if (warkCategoryId != "") {
                showLoaderObs.value = true;
                showLoader = true; // Keep for compatibility
                // setState(() { showLoader = true; }); // Removed
                  if (showLoader)
                    showModalBottomSheet(
                        isDismissible: false,
                        enableDrag: false,
                        shape: const RoundedRectangleBorder(
                          // <-- SEE HERE
                          borderRadius: BorderRadius.vertical(
                            top: Radius.circular(20),
                          ),
                        ),
                        context: context,
                        builder: (context) {
                          return Container(
                            height: 0.3 * height,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                    // height: 0.7 * width,
                                    // width: 0.6 * width,
                                    // /color: Colors.red,
                                    child: Container(

                                        child: new MyUtils()
                                            .kLoadingWidgetLarge(context))),
                                SizedBox(
                                  height: 0.1 * width,
                                ),
                                Container(
                                  child: Text(
                                    'Please Wait ...',
                                    style: TextStyle(
                                      fontSize: 0.06 * width,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        });
                  serviceRequestFromSubmissionCall(
                      Constants.ACC_ID,
                      Constants.USER_ID,
                      warkCategoryId,
                      description,
                      communiacationController.text,
                      dobController.text + " " + timeController.text,
                      context);
                } else {
                  MyUtils.showOkDialog(
                      context, "Oops!", "Please select service from list");
                }
              }
            // setState(() { ... }); // Removed

            //postCreateOrderCall(context);
          },
          increaseWidthBy: 500.0,
          increaseHeightBy: 80.0,
        ),
      ),
    );
  }
}
