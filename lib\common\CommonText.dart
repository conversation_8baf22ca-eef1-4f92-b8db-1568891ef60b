class CommonText {
  String app_name = "Medilink";
  String app_subheading =
      "Best app for society security visitor management system";

  String mob_no = "Please enter mobile number";
  String mob_no2 = "Please enter mobile/landline number";
  String valid_mob_no = "Please enter valid mobile number";
  String password = "Please enter password";
  String currentPassword = "Please enter current password";
  String newPassword = "Please enter new password";
  String cnfPassword = "Please enter confirm password";
  String valid_password = "Please enter password of 8 or more characters";
  String match_password = "Confirm Password should match password";
  String user_name = "Please enter username";
  String user_Membership = "Please enter Membership";
  String user_Email = "Please enter Email";
  String message = "Please enter message";
  String first_name = "Please enter first name";
  String last_name = "Please enter last name";
  String email = "Please enter email id ";
  String valid_email = "Please enter valid email id";
  String birthdate = "Please enter birth date";
  String lang = "Please enter language";
  String city = "Please enter city";
  String building_name = "Please enter building name";
  String wing = "Please enter wing";
  String address = "Please enter address";
  String landmark = "Please enter landmark";
  String state = "Please enter state";
  String pincode = "Please enter pincode";
  String valid_pincode = "Please enter valid pincode";
  String name_of_cp = "Please enter contact person's name";
  String mob_no_of_cp = "Please enter contact person's mobile number";
  String property_count = "Please enter count of total properties";
  String room_type = "Please select type of room";
  String room_no = "Please enter room number";
  String valid_room_no = "Please enter valid room number";
  String assign_building = "Please select building";
  String room_meter_no = "Please enter room meter number";
  String water_meter_no = "Please enter water meter number";
  String rent = "Please enter rent";
  String deposit = "Please enter deposit";
  String alert_dialog_delete_icon = "Are you sure you want to delete this ?";
  String alert_dialog_back_button = "Are you sure you want to exit this app ?";
  String rent_start_date = "Please enter rent start date";
  String rent_end_date = "Please enter rent end date";
  String error_Mode = "Please Select Mode";
  String error_parking_slot = "Please select parking slot";
  String resend_otp = "Resend OTP";
  String enter_otp = "ENTER OTP";
  String waiting_otp = "Waiting for the OTP (00:90s)";
}
