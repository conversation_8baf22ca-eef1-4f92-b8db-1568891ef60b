import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../common/MyUtils.dart';
import '../common/PreferenceManagerUtil.dart';
import '../common/Constants.dart';
import '../model/customer_login_pass_model.dart';
import '../controller/LoginUserNamePasswordController.dart' as LoginController;
import 'SetMPinActivity.dart';

class UserNameVerificationScreen extends StatefulWidget {
  const UserNameVerificationScreen({
    Key? key,
  }) : super(key: key);

  @override
  State<UserNameVerificationScreen> createState() => _UserNameVerificationScreenState();
}

class _UserNameVerificationScreenState extends State<UserNameVerificationScreen> {
  // ✅ OPTIMIZED: GetX reactive variables for state management
  var isLoadingObs = false.obs;
  var passwordVisibleObs = false.obs;
  var userNameObs = ''.obs;
  var userPasswordObs = ''.obs;

  // Original variables (keeping for compatibility)
  bool isLoading = false;
  bool passwordVisible = false;

  // Form controllers
  TextEditingController userNameController = TextEditingController();
  TextEditingController userPasswordController = TextEditingController();

  // ✅ FIXED: SHA1 encryption method (server requirement confirmed)
  String generatePasswordHash(String dataIn) {
    var digest = sha1.convert(utf8.encode(dataIn));
    String hashedValue = digest.toString();
    print("🔐 SHA1 Encryption:");
    print("🔐 Input: '$dataIn'");
    print("🔐 SHA1 Output: '$hashedValue'");
    return hashedValue;
  }

  Future<CustomerLoginPassModel> validateUsingUserName(String userName, String userPassword, BuildContext context) async {
    print("username:$userName userPassword:$userPassword");
    return await validateUserNameApi(userName, userPassword, context);
  }

  // API call for username validation
  Future<CustomerLoginPassModel> validateUserNameApi(String userName, String userPassword, BuildContext context) async {
    try {
      // ✅ OPTIMIZED: GetX reactive state management
      isLoadingObs.value = true;
      isLoading = true; // Keep for compatibility
      // setState(() { isLoading = true; }); // Removed

      // ✅ FIXED: Call real API with proper connectivity check
      var connectivityResult = await Connectivity().checkConnectivity();
      if (connectivityResult == ConnectivityResult.mobile ||
          connectivityResult == ConnectivityResult.wifi) {

        print("🌐 API Call - Username: $userName");
        print("🌐 API Call - Password (encrypted): $userPassword");

        // Call the real API from LoginUserNamePasswordController
        CustomerLoginPassModel response = await LoginController.validateUserNameApi(userName, userPassword, context);

        // ✅ OPTIMIZED: GetX reactive state management
        isLoadingObs.value = false;
        isLoading = false; // Keep for compatibility

        return response;
      } else {
        // ✅ OPTIMIZED: GetX reactive state management
        isLoadingObs.value = false;
        isLoading = false; // Keep for compatibility

        return CustomerLoginPassModel(
          success: 0,
          message: 'No internet connection',
          data: [],
        );
      }
    } catch (e) {
      // ✅ OPTIMIZED: GetX reactive state management
      isLoadingObs.value = false;
      isLoading = false; // Keep for compatibility
      // setState(() { isLoading = false; }); // Removed
      print('Login error: $e');

      // Return error response
      return CustomerLoginPassModel(
        success: 0,
        message: 'Network error occurred',
        data: null,
      );
    }
  }




  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: InkWell(
          onTap: (){
            // ✅ OPTIMIZED: GetX navigation (faster and more efficient)
            Get.back();
            // Navigator.pop(context); // Removed
          },
            child:
            Icon(Icons.arrow_back,color: Colors.black87,)),
        backgroundColor: Colors.white,
      ),
      body: SafeArea(
        top: false,
        child: Container(
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(left: 20, right: 20),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Center(
                        child: Container(
                          alignment: Alignment.center,
                          margin: EdgeInsets.only(top: 0.2 * MediaQuery.of(context).size.height),
                          height: MediaQuery.of(context).size.height * 0.1,
                          width: MediaQuery.of(context).size.width * 0.15,
                          child: SvgPicture.asset('assets/images/Login with Email.svg'),
                        ),
                      ),
                      Center(
                        child: Container(
                          margin: EdgeInsets.only(top: 10),
                          alignment: Alignment.bottomCenter,
                          child: Text(
                              "Login with Username & Password",
                              style: TextStyle(
                                fontSize: 15,
                                letterSpacing: 0.4,
                                color: Colors.black,
                                fontWeight: FontWeight.w600,
                              ),
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 10),
                        child: Text(
                            "Login username",
                            style: TextStyle(
                              fontSize: 10,
                              letterSpacing: 0.4,
                              color: Colors.black,
                              fontWeight: FontWeight.w400,
                            ),
                        ),
                      ),
                      TextFormField(
                        controller: userNameController,
                        decoration: const InputDecoration(
                          hintText: "Enter your login username",
                          hintStyle: TextStyle(fontFamily: 'schyler', fontSize: 10, color: Colors.grey),
                          fillColor: Colors.white,
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.only(top: 20),
                        child: Text(
                            "Password",
                           style: TextStyle(
                              fontSize: 10,
                              letterSpacing: 0.4,
                              color: Colors.black,
                              fontWeight: FontWeight.w400,
                            ),
                        ),
                      ),
                      Obx(() => TextFormField(
                        controller: userPasswordController,
                        obscureText: !passwordVisibleObs.value,  // ✅ OPTIMIZED: GetX reactive variable
                        decoration:  InputDecoration(
                          hintText: "Enter your password",
                          hintStyle: TextStyle(fontFamily: 'schyler', fontSize: 10, color: Colors.grey),
                          fillColor: Colors.white,
                          suffixIcon: IconButton(
                            icon: Icon(
                              // ✅ OPTIMIZED: Based on GetX reactive state choose the icon
                              passwordVisibleObs.value
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                              color: Theme.of(context).primaryColorDark,
                            ),
                            onPressed: () {
                              // ✅ OPTIMIZED: GetX reactive state management
                              passwordVisibleObs.value = !passwordVisibleObs.value;
                              passwordVisible = passwordVisibleObs.value; // Keep for compatibility
                              // setState(() { passwordVisible = !passwordVisible; }); // Removed
                            },
                          ),
                        ),
                      )),  // ✅ FIXED: Close Obx widget properly
                      Obx(() => InkWell(
                        onTap: isLoadingObs.value ? null : () async {
                          Future.delayed(Duration.zero, () async {
                            if (userPasswordController.text.toString().isNotEmpty && userNameController.text.toString().isNotEmpty) {
                              String plainPassword = userPasswordController.text.toString();
                              String encryptedPassword = generatePasswordHash(plainPassword);

                              print("🔐 Username: ${userNameController.text.toString()}");
                              print("🔐 Plain Password: $plainPassword");
                              print("🔐 SHA1 Encrypted: $encryptedPassword");

                              // ✅ FIXED: Use SHA1 encryption (confirmed working method)
                              CustomerLoginPassModel? response = await validateUsingUserName(userNameController.text, encryptedPassword, context);
                              //verifyLogin(userNameController.text, userPasswordController.text, context);
                              if(response.success == 1){
                                new PreferenceManagerUtil().setMobile(response.data![0].mobileNo.toString());
                                // Use GetX navigation
                                Get.to(() => SetMPinActivity());
                              }else{
                                MyUtils.showOkDialog(context, "Error", response.message.toString());
                              }
                            }
                            else{
                              MyUtils.showOkDialog(context, "Error", "Please fill username & password");
                            }
                          });
                        },
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          margin: const EdgeInsets.only(top: 30),
                          padding: const EdgeInsets.only(top: 13, bottom: 13),
                          decoration: BoxDecoration(
                            color: isLoadingObs.value ? Colors.grey : Colors.blue,
                            borderRadius: BorderRadius.circular(5),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (isLoadingObs.value)
                                SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                ),
                              if (isLoadingObs.value) SizedBox(width: 10),
                              Text(
                                isLoadingObs.value ? 'Logging in...' : 'Login',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        ),
                      )),

                    ],
                  ),
                ),

              ],
            ),
          ),
        ),
      ),
    );
  }


}
