class CustomerFileUploadModel {
  int? _status;
  String? _message;
  int? _success;
  String? _data;
  String? _imageID;
  String? _errorDev;

  CustomerFileUploadModel(
      {int? status,
        String? message,
        int? success,
        String? data,
        String? imageID,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (imageID != null) {
      this._imageID = imageID;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get data => _data;
  set data(String? data) => _data = data;
  String? get imageID => _imageID;
  set imageID(String? imageID) => _imageID = imageID;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CustomerFileUploadModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _data = json['data'];
    _imageID = json['imageID'];
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['data'] = this._data;
    data['imageID'] = this._imageID;
    data['error_dev'] = this._errorDev;
    return data;
  }
}
