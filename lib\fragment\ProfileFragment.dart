import 'dart:async';
import 'dart:io';
import 'dart:ui';

import 'package:erpcacustomer/activity/LoginActivity.dart';
import 'package:erpcacustomer/activity/AppInfoActivity.dart';
import 'package:erpcacustomer/activity/SplashActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
import 'package:erpcacustomer/common/image_picker_handler.dart';
import 'package:erpcacustomer/controller/InvoiceController.dart';
import 'package:erpcacustomer/model/UserDetailsModel.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

class MyAccountFragment extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return MyAccountFragmentState();
  }
}

class MyAccountFragmentState extends State<MyAccountFragment>
    with ImagePickerListener, TickerProviderStateMixin {
  // GetX reactive variables
  var actualNameObs = ''.obs;
  var actualEmailObs = ''.obs;
  var profilePicObs = ''.obs;
  var departmentNameObs = ''.obs;
  var designationNameObs = ''.obs;
  var actualContactObs = ''.obs;
  var isSavingObs = false.obs;
  var selectedIndexObs = 0.obs;
  var selectedUserObs = ''.obs;
  var selectedBusinessObs = ''.obs;

  // Original variables
  String actualName = "";
  String actualEmail = "";
  String profilePic = "";
  String departmentName = "";
  String designationName = "";
  String dateOfBirth = "";
  DateTime? dateOfBirthDate;
  String actualContact = "";
  ImagePickerHandler? imagePicker;
  AnimationController? _controller;
  bool _saving = false;
  TabController? _tabController;
  int _selectedIndex = 0;
  File? _image;
  String selectedUser = "";
  String selectedBuisness = "";

  @override
  Widget build(BuildContext context) {
    return ModalProgressHUD(
      inAsyncCall: _saving,
      child: Scaffold(
        body: FutureBuilder<UserDetailsModel>(
          future: selectedCustomerCall(
              Constants.ACC_ID, Constants.USER_ID, context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? SingleChildScrollView(
                    child: Column(children: <Widget>[
                      GestureDetector(
                          onTap: () {
                            // imagePicker.showDialog(context);
                          },
                          child: (snapshot.data!.data![0].photoThumb == "")
                              ? Padding(
                                  padding: const EdgeInsets.only(top: 25.0),
                                  child: Center(
                                    child: Container(
                                      child: Icon(
                                        Icons.account_circle,
                                        color: Color(new CommonColor()
                                            .lightest_grey_Color),
                                        size: 80,
                                      ),
                                    ),
                                  ),
                                )
                              : Padding(
                                  padding: const EdgeInsets.only(top: 15.0),
                                  child: Padding(
                                    padding: EdgeInsets.only(right: 8, left: 0),
                                    child: Container(
                                      height: 60,
                                      width: 60,
                                      decoration: new BoxDecoration(
                                          shape: BoxShape.circle,
                                          image: new DecorationImage(
                                              fit: BoxFit.fill,
                                              image: new NetworkImage(snapshot
                                                  .data!.data![0].photoThumb.toString()))),
                                    ),
                                  ),
                                )),
                      /*  Padding(
                        padding: const EdgeInsets.only(top: 10.0),
                        child: Center(
                          child: Text(
                            selectedUser,
                            style: new CSSStyle().ptSansBlackRegular22(context),
                          ),
                        ),
                      ),*/
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0, bottom: 20),
                        child: Center(
                          child: Text(
                            snapshot.data!.data![0].companyName.toString(),
                            style: new CSSStyle().poppinsGreyRegular15(context),
                          ),
                        ),
                      ),
                      Padding(
                        padding:
                            const EdgeInsets.only(left: 15, top: 30, right: 15),
                        child: Column(
                          children: <Widget>[
                            /*    Row(
                            mainAxisSize: MainAxisSize.max,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Text(
                                    "Username",
                                    style: new CSSStyle()
                                        .poppinsGreyRegular12(context),
                                  ),
                                  Text(
                                    snapshot.data.data[0].firstName,
                                    style: new CSSStyle()
                                        .poppinsLightBlackRegular15(context),
                                  ),
                                ],
                              ),
                              Text(
                                "Edit",
                                style: new CSSStyle()
                                    .poppinsBlueLightBold15(context),
                              )
                            ],
                          ),*/
                            /*  Padding(
                            padding: const EdgeInsets.only(top: 30),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Email",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    Text(
                                      snapshot.data.data[0].userEmail,
                                      style: new CSSStyle()
                                          .poppinsLightBlackRegular15(context),
                                    ),
                                  ],
                                ),
                                Text(
                                  "Edit",
                                  style: new CSSStyle()
                                      .poppinsBlueLightBold15(context),
                                )
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(top: 30),
                            child: Row(
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Column(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Location",
                                      style: new CSSStyle()
                                          .poppinsGreyRegular12(context),
                                    ),
                                    Container(
                                      width: 250,
                                      child: Text(
                                        snapshot.data.data[0].address,
                                        style: new CSSStyle()
                                            .poppinsLightBlackRegular15(context),
                                      ),
                                    ),
                                  ],
                                ),
                                Text(
                                  "Edit",
                                  style: new CSSStyle()
                                      .poppinsBlueLightBold15(context),
                                )
                              ],
                            ),
                          ),*/
                            /* Padding(
                  padding: const EdgeInsets.only(top: 30),
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.max,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(
                            "Receives Notification",
                            style: new CSSStyle().poppinsGreyRegular12(context),
                          ),
                          Text(
                            widget.notificaiton_Enabled,
                            style: new CSSStyle()
                                .poppinsLightBlackRegular15(context),
                          ),
                        ],
                      ),
                      Switch(
                        activeTrackColor:
                            Color(new CommonColor().erpca_blue_color),
                        activeColor: Color(new CommonColor().white_Color),
                        value: isSwitched,
                        onChanged: (value) {
                          // GetX reactive update - NO setState needed!
                          isSwitched = value;
                          // Add GetX reactive variable if needed: isSwitchedObs.value = value;
                        },
                      )
                    ],
                  ),
              ),*/
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13, top: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Email",
                                        style: new CSSStyle()
                                            .poppinsBlackBold13W400(context),
                                      ),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(top: 5.0),
                                        child: Text(
                                          snapshot.data!.data![0].userEmail.toString(),
                                          style: new CSSStyle()
                                              .poppinsGreyRegular12(context),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13, top: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Address",
                                        style: new CSSStyle()
                                            .poppinsBlackBold13W400(context),
                                      ),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(top: 5.0),
                                        child: Container(
                                          width: 300,
                                          child: Text(
                                            snapshot.data!.data![0].address.toString(),
                                            style: new CSSStyle()
                                                .poppinsGreyRegular12(context),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13, top: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Mobile",
                                        style: new CSSStyle()
                                            .poppinsBlackBold13W400(context),
                                      ),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(top: 5.0),
                                        child: Text(
                                          snapshot.data!.data![0].mobileNo.toString(),
                                          style: new CSSStyle()
                                              .poppinsGreyRegular12(context),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13, top: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "GST No",
                                        style: new CSSStyle()
                                            .poppinsBlackBold13W400(context),
                                      ),
                                      Padding(
                                        padding:
                                            const EdgeInsets.only(top: 5.0),
                                        child: Text(
                                          snapshot.data!.data![0].gstNo.toString(),
                                          style: new CSSStyle()
                                              .poppinsGreyRegular12(context),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13, top: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "PAN No.",
                                        style: new CSSStyle()
                                            .poppinsBlackBold13W400(context),
                                      ),
                                      (snapshot.data!.data![0].panNo != null)
                                          ? Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 5.0),
                                              child: Text(
                                                snapshot.data!.data![0].panNo.toString(),
                                                style: new CSSStyle()
                                                    .poppinsGreyRegular12(
                                                        context),
                                              ),
                                            )
                                          : Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 5.0),
                                              child: Text(
                                                "N/A",
                                                style: new CSSStyle()
                                                    .poppinsGreyRegular12(
                                                        context),
                                              ),
                                            )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13, top: 5),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "Business PAN No",
                                        style: new CSSStyle()
                                            .poppinsBlackBold13W400(context),
                                      ),
                                      (snapshot.data!.data![0].businessPanNo !=
                                              null)
                                          ? Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 5.0),
                                              child: Text(
                                                snapshot
                                                    .data!.data![0].businessPanNo.toString(),
                                                style: new CSSStyle()
                                                    .poppinsGreyRegular12(
                                                        context),
                                              ),
                                            )
                                          : Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 5.0),
                                              child: Text(
                                                "N/A",
                                                style: new CSSStyle()
                                                    .poppinsGreyRegular12(
                                                        context),
                                              ),
                                            )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            Divider(),
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: 13.0, right: 13, top: 5),
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) =>
                                              AppInfoActivity()));
                                },
                                child: Row(
                                  mainAxisSize: MainAxisSize.max,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Column(
                                      mainAxisSize: MainAxisSize.max,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      children: [
                                        Text(
                                          "App Info",
                                          style: new CSSStyle()
                                              .poppinsBlackBold13W400(context),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Divider(),
                            Padding(
                              padding: const EdgeInsets.only(top: 10),
                              child: Row(
                                mainAxisSize: MainAxisSize.max,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(
                                        "",
                                        style: new CSSStyle()
                                            .poppinsGreyRegular12(context),
                                      ),
                                      Text(
                                        "",
                                        style: new CSSStyle()
                                            .poppinsLightBlackRegular15(
                                                context),
                                      ),
                                    ],
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      onLogoutPressed();
                                    },
                                    child: Text(
                                      "Logout",
                                      style: new CSSStyle()
                                          .poppinsBlueLightestRegular15(
                                              context),
                                    ),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                      )
                      /* TabBar(
                        onTap: (index) {
                          // Should not used it as it only called when tab options are clicked,
                          // not when user swapped
                        },
                        indicatorWeight: 1,
                        isScrollable: true,
                        controller: _tabController,
                        tabs: [
                          Tab(
                            child: Text(
                              "ABOUT",
                              style:
                                  new CSSStyle().poppinsBlackRegular12(context),
                            ),
                          ),
                          Tab(
                            child: Text(
                              "CONTACT PERSON",
                              style:
                                  new CSSStyle().poppinsBlackRegular12(context),
                            ),
                          ),
                          Tab(
                            child: Text(
                              "SERVICES",
                              style:
                                  new CSSStyle().poppinsBlackRegular12(context),
                            ),
                          ),
                          */ /*  Tab(
                    child: Text(
                      "NEW FILES",
                      style: new CSSStyle().poppinsBlackRegular12(context),
                    ),
                  ),*/ /*
                        ],
                      ),
                      Container(
                        height: 400,
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            Center(
                                child: AboutFragment(
                              emailId: snapshot.data.data[0].userEmail,
                              location: snapshot.data.data[0].address,
                              notificaiton_Enabled: "",
                              userName: snapshot.data.data[0].firstName,
                            )),
                            Center(
                                child: ContactPersonFragment(
                              name: snapshot.data.data[0].companyName,
                              email: snapshot.data.data[0].userEmail,
                              mobileNo: snapshot.data.data[0].mobileNo,
                              location: snapshot.data.data[0].address,
                            )),
                            Center(child: ServicesFragment()),
                            // Center(child: NewFilesFragment()),
                          ],
                        ),
                      ),*/
                    ]),
                  )
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
      ),
    );
  }

  postSelectedCustomerCall(
      String acc_id, String user_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await selectedCustomerApi(acc_id, user_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        UserDetailsModel currentParsedResponse =
            UserDetailsModel.fromJson(responseJson);

        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<UserDetailsModel> selectedCustomerCall(
      String acc_id, String user_id, BuildContext context) async {
    return await postSelectedCustomerCall(acc_id, user_id, context);
  }

  _signoutMethodCall() async {
    /* Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => LoginActivity()),
    );*/

    new PreferenceManagerUtil().setMobile("");
    new PreferenceManagerUtil().setMPin("");
    new PreferenceManagerUtil().setProposerDisplayName("");
    new PreferenceManagerUtil().setListOfCompany("");
    new PreferenceManagerUtil().setBuisnessName("");
    new PreferenceManagerUtil().setProfilePic("");
    new PreferenceManagerUtil().setUserId("");
    new PreferenceManagerUtil().setProposerDisplayName("");

    Navigator.pushAndRemoveUntil<dynamic>(
      context,
      MaterialPageRoute<dynamic>(
        builder: (BuildContext context) => SplashActivity(),
      ),
      (route) => false, //if you want to disable back feature set to false
    );
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _controller = new AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    Future<String> token = new PreferenceManagerUtil().getMainUser();
    token.then((value) {
      // GetX reactive update - NO setState needed!
      selectedUser = value;
      selectedUserObs.value = value;  // GetX automatically updates UI
    });
    Future<String> tokenBuisness =
        new PreferenceManagerUtil().getBuisnessName();
    tokenBuisness.then((value) {
      // GetX reactive update - NO setState needed!
      selectedBuisness = value;
      selectedBusinessObs.value = value;  // GetX automatically updates UI
    });
    imagePicker = new ImagePickerHandler(this, _controller);
    imagePicker!.init();
    _tabController = TabController(length: 3, vsync: this);
    _controller!.addListener(() {
      // GetX reactive update - NO setState needed!
      _selectedIndex = _tabController!.index;
      // Add GetX reactive variable if needed: selectedIndexObs.value = _tabController!.index;
    });
  }

 onLogoutPressed() {
    return showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text('Are you sure?'),
            content: new Text('Do you want to logout from App'),
            actions: <Widget>[
              new GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("NO"),
                ),
              ),
              SizedBox(height: 16),
              new GestureDetector(
                onTap: () {
                  _signoutMethodCall();

                  return Navigator.of(context).pop(true);
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("YES"),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  _addForgotPasswordButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 0),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 10.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_blue_Color),
          Color(new CommonColor().oxygen_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'Reset Password',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => LoginActivity(),
              ));
        },
        increaseWidthBy: 100.0,
        increaseHeightBy: 10.0,
      ),
    );
  }

  _addAccessMyAccountButtonUi() {
    return Container(
      margin: EdgeInsets.fromLTRB(20, 20, 20, 20),
      constraints: BoxConstraints(maxHeight: 40.0, minHeight: 40.0),
      child: GradientButton(
        gradient: LinearGradient(colors: [
          Color(new CommonColor().oxygen_dark_blue_Color),
          Color(new CommonColor().oxygen_dark_blue_Color)
        ], begin: Alignment.centerLeft, end: Alignment.centerRight),
        //color: Colors.cyan,
        elevation: 5.0,
        shape: new RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(10.0)),
        //splashColor: Colors.blueGrey,
        //color: Theme.of(context).accentColor,
        //textColor: Theme.of(context).primaryColorLight,
        child: Text(
          'LOGOUT',
          style: new CSSStyle().verdanaWhiteLight14(context),
        ),
        callback: () {
          // GetX reactive update - NO setState needed!
          onLogoutPressed();
        },
        increaseWidthBy: 225.0,
        increaseHeightBy: 50.0,
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }

  @override
  userImage(File _image) {
    // TODO: implement userImage
    if (_image != null) {
      // GetX reactive update - NO setState needed!
      this._image = _image;
      // Add GetX reactive variable if needed: profileImageObs.value = _image;
    }
  }

  DateTime convertDateFromString(String strDate) {
    DateTime todayDate = DateTime.parse(strDate);
    ;
    return todayDate;
  }
}
