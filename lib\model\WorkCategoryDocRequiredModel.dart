class WorkCategoryDocRequiredModel {
  int? _status;
  String? _message;
  int? _success;
  String? _errorMsg;
  List<Data>? _data;
  String? _errorDev;

  WorkCategoryDocRequiredModel(
      {int? status,
        String? message,
        int? success,
        String? errorMsg,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (errorMsg != null) {
      this._errorMsg = errorMsg;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get errorMsg => _errorMsg;
  set errorMsg(String? errorMsg) => _errorMsg = errorMsg;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  WorkCategoryDocRequiredModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _errorMsg = json['error_msg'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['error_msg'] = this._errorMsg;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _wcDocReqId;
  String? _docRequiredName;
  String? _filePath;

  Data({String? wcDocReqId, String? docRequiredName, String? filePath}) {
    if (wcDocReqId != null) {
      this._wcDocReqId = wcDocReqId;
    }
    if (docRequiredName != null) {
      this._docRequiredName = docRequiredName;
    }
    if (filePath != null) {
      this._filePath = filePath;
    }
  }

  String? get wcDocReqId => _wcDocReqId;
  set wcDocReqId(String? wcDocReqId) => _wcDocReqId = wcDocReqId;
  String? get docRequiredName => _docRequiredName;
  set docRequiredName(String? docRequiredName) =>
      _docRequiredName = docRequiredName;
  String? get filePath => _filePath;
  set filePath(String? filePath) => _filePath = filePath;

  Data.fromJson(Map<String, dynamic> json) {
    _wcDocReqId = json['wc_doc_req_id'];
    _docRequiredName = json['doc_required_name'];
    _filePath = json['file_path'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['wc_doc_req_id'] = this._wcDocReqId;
    data['doc_required_name'] = this._docRequiredName;
    data['file_path'] = this._filePath;
    return data;
  }
}
