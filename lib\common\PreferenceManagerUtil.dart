import 'package:shared_preferences/shared_preferences.dart';
import 'package:get/get.dart';

class PreferenceManagerUtil {
  String _proposerId = "_proposerId";
  String _proposerNo = "_proposerNo";
  String _proposerNoDate = "_proposerNoDate";
  String _applicationNo = "_applicationNo";
  String _insuranceCode = "_insuranceCode";
  String _insuranceId = "_insuranceId";
  String _policyType = "_policyType";
  String _grpIndv = "_grpIndv";
  String _planId = "_planId";
  String _corporateId = "_corporateId";
  String _tpaId = "_tpaId";
  String _proposerDisplayName = "_proposerDisplayName";
  String _buisnessName = "_buisnessName";
  String _contPhone = "_contPhone";
  String _appVersion = "_appVersion";
  String _status = "_status";
  String _contPerEmail = "_contPerEmail";
  String _profilePic = "_profilePic";

  String _userId = "_userId";
  String _proposerPriAddLine1 = "_proposerPriAddLine1";
  String _proposerPriAddLine2 = "_proposerPriAddLine2";
  String _proposerPriCityId = "_proposerPriCityId";
  String _proposerPriStateId = "_proposerPriStateId";
  String _proposerPriCountryId = "_proposerPriCountryId";
  String _proposerPriPin = "_proposerPriPin";
  String _proposerPriPoliceStn = "_proposerPriPoliceStn";
  String _proposerPriPhone = "_proposerPriPhone";
  String _proposerPriMobile = "_proposerPriMobile";
  String _mobile = "_mobile";
  String _mainUser = "_mainUser";
  String _proposerPriEmail = "_proposerPriEmail";
  String _agencyCode = "_agencyCode";
  String _logindetails = "_logindetails";
  String _ListOfCompany = "ListOfCompany";
  String _policyCode = "_policyCode";
  String _prevPolicyCode = "_prevPolicyCode";
  String _periodFrom = "_periodFrom";
  String _periodTo = "_periodTo";
  double _premiumAmount = 0.0;
  String _modeOfPayment = "_modeOfPayment";
  String _currency = "_currency";
  String _proposerPriCityDescription = "_proposerPriCityDescription";
  String _proposerPriStateDescription = "_proposerPriStateDescription";
  String _proposerPriCountryDescription = "_proposerPriCountryDescription";
  String _tokenID = "_tokenID";
  final String _mPin = "_mPin";

  Future<bool> setMPin(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    print("Values Sete for mpin $value");
    return prefs.setString(_mPin, value);
  }

  Future<String> getMPin() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_mPin) ?? '';
  }
  Future<String> getAppVersion() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_appVersion) ?? '';
  }

  Future<String> getLoginDetails() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_logindetails) ?? '';
  }

  Future<bool> setLoginDetails(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_logindetails, value);
  }

  Future<String> getListOfCompany() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_ListOfCompany) ?? '';
  }

  Future<bool> setListOfCompany(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_ListOfCompany, value);
  }

  Future<String> getProposerId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerId) ?? '';
  }

  Future<bool> setProposerId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerId, value);
  }

  Future<String> getProposerNo() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerNo) ?? '';
  }

  Future<bool> setProposerNo(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerNo, value);
  }

  Future<String> getProposerPriPhone() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerPriPhone) ?? '';
  }

  Future<bool> setProposerPriPhone(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerPriPhone, value);
  }

  Future<String> getProposerPriEmail() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerPriEmail) ?? '';
  }

  Future<bool> setProposerPriEmail(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerPriEmail, value);
  }

  Future<String> getPolicyCode() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_policyCode) ?? '';
  }

  Future<bool> setPolicyCode(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_policyCode, value);
  }

  Future<String> getProposerPriMobile() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerPriMobile) ?? '';
  }

  Future<bool> setProposerPriMobile(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerPriMobile, value);
  }

  Future<String> getMobile() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    print('mobile number from shared prefs ${prefs.getString(_mobile)}');
    return prefs.getString(_mobile) ?? '';
  }

  Future<bool> setMobile(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    print('mobile set to $value');
    return prefs.setString(_mobile, value);
  }

  Future<String> getMainUser() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_mainUser) ?? '';
  }

  Future<bool> setMainUser(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_mainUser, value);
  }

  Future<String> getProposerNoDate() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerNoDate) ?? '';
  }

  Future<bool> setProposerNoDate(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerNoDate, value);
  }

  Future<String> getApplicationNo() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_applicationNo) ?? '';
  }

  Future<bool> setApplicationNo(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_applicationNo, value);
  }

  Future<String> getInsuranceCode() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_insuranceCode) ?? '';
  }

  Future<bool> setInsuranceCode(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_insuranceCode, value);
  }

  Future<String> getInsuranceId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_insuranceId) ?? '';
  }

  Future<bool> setInsuranceId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_insuranceId, value);
  }

  Future<String> getPolicyType() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_policyType) ?? '';
  }

  Future<bool> setPolicyType(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_policyType, value);
  }

  Future<String> getGrpIndv() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_grpIndv) ?? '';
  }

  Future<bool> setGrpIndv(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_grpIndv, value);
  }

  Future<String> getPlanId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_planId) ?? '';
  }

  Future<bool> setPlanId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_planId, value);
  }

  Future<String> getCorporateId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_corporateId) ?? '';
  }

  Future<bool> setCorporateId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_corporateId, value);
  }

  Future<String> getTpaId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_tpaId) ?? '';
  }

  Future<bool> setTpaId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_tpaId, value);
  }

  Future<String> getProposerDisplayName() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerDisplayName) ?? '';
  }

  Future<bool> setProposerDisplayName(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerDisplayName, value);
  }

  Future<String> getBuisnessName() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_buisnessName) ?? '';
  }

  Future<bool> setBuisnessName(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_buisnessName, value);
  }

  Future<String> getContactPhone() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_contPhone) ?? '';
  }

  Future<bool> setContactPhone(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_contPhone, value);
  }

  Future<bool> setAppVersion(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_appVersion, value);
  }
  Future<bool> setStatus(int value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.setInt(_status, value);
  }
  Future<int> getStatus() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();
    print('status get :  ${prefs.getInt(_status)}');
    return prefs.getInt(_status) ?? 0;
  }

  Future<String> getContPerEmail() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_contPerEmail) ?? '';
  }

  Future<bool> setContPerEmail(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_contPerEmail, value);
  }

  Future<String> getProfilePic() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_profilePic) ?? '';
  }

  Future<bool> setProfilePic(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_profilePic, value);
  }

  Future<String> getUserId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_userId) ?? '';
  }

  Future<bool> setUserId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_userId, value);
  }

  Future<String> getProposerPriAddLine1() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerPriAddLine1) ?? '';
  }

  Future<bool> setProposerPriAddLine1(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerPriAddLine1, value);
  }

  Future<String> getProposerPriAddLine2() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerPriAddLine2) ?? '';
  }

  Future<bool> setProposerPriAddLine2(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerPriAddLine2, value);
  }

  Future<String> getProposerPriCityId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerPriCityId) ?? '';
  }

  Future<bool> setProposerPriCityId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerPriCityId, value);
  }

  Future<String> getProposerPriStateId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_proposerPriStateId) ?? '';
  }

  Future<bool> setProposerPriStateId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_proposerPriStateId, value);
  }

  Future<String> getTokenId() async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.getString(_tokenID) ?? '';
  }

  Future<bool> setTokenId(String value) async {
    final SharedPreferences prefs = await SharedPreferences.getInstance();

    return prefs.setString(_tokenID, value);
  }
}
