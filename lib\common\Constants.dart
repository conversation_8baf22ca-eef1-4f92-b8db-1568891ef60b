class Constants {

  static Map<String, String>? headers = {
    "Id": "erpca",
    "Pass": "e736258681ac6d7126d298cc93a732db1dad2996",
    "Content-Type": "application/json",
  };
  static final String ACTUAL_URL = "https://erpca.in/dev/api-customer-app/";

  static String URL = ACTUAL_URL;
  static String APP_URL = 'https://play.google.com/store/apps/details?id=com.erpca.capp.woodapples';

  static String ACCESS_TOKEN = "8djyCodQy23kVBp2nW63hLdZEV4QRb4m9r5eEXZRA6U";
  static String ACC_ID = "25";
  static String USER_ID = "";
  static String FINGERPRIINT = "06 6d 78 e0 be c1 b5 01 fe 37 34 14 76 47 9c d8";
  static String SHA_KEY = "8djyCodQy23kVBp2nW63hLdZEV4QRb4m9r5eEXZRA6U";
  static String LOGIN_URL = URL + "validate-customer-mobile.php";
  static String CUSTOMER_LOGIN_PASS_URL = URL+"customer-login-pass.php";
  static String POST_TASK_LIST_URL = URL + "get-customer-task-list.php";
  static String POST_DASHBOARD_URL = URL + "customer-dashboard.php";
  static String POST_INVOICE_LIST_URL = URL + "get-customer-bill-list.php";
  static String GET_ORDER_ID_RAZOR_PAY = "https://api.razorpay.com/v1/orders";
  static String RAZOR_PAY_ACTION = URL + "razorpay_payment_action.php";
  static String STORE_ORDER = URL + "store_oder_api.php";
  static String CHECK_PAYMENT_GATEWAY = URL + "check-payment-gateway.php";
  static String GET_NET_AMOUNT = URL + "get-razorpay-amount.php";
  static String POST_WORK_CATEGORY_LIST_URL = URL + "get-workcategory-list.php";
  static String POST_NOTIFICATION_LIST_URL = URL + "getNotificationsList.php";
  static String POST_SELECTED_CUSTOMER_DETAILS_URL =
      URL + "get-selected-customer-details.php";
  static String POST_NOTIFICATION_DETAILS_URL =
      URL + "getNotificationContent.php";
  static String POST_DOC_REQUIRED_URL =
      URL + "get-customer-task-details-document-required-for-task.php";
  static String POST_DOC_ASSIGN_URL =
      URL + "get-customer-task-details-document-uploaded-by-assignee.php";
  static String POST_CUSTOMER_TASK_DETAILS_URL =
      URL + "get-customer-task-details.php";
  static String POST_SERVICE_REQUEST_FROM_SUBMISSION_URL =
      URL + "service-request-form-submission.php";
  static String POST_SERVICE_REQ_UPLOAD_DOCUMENT_URL =
      URL + "service-request-upload-required-documents.php";
  static String POST_UPLOAD_REQUIRED_FILE_URL =
      URL + "upload-required-file.php";
  static String POST_DELETE_REQUIRED_FILE_URL =
      URL + "unlink-required-file.php";
  static String POST_SERVICE_REQ_DELETE_DOCUMENT_URL =
      URL + "service-request-unlink-uploaded-document.php";
  static String POST_WORK_CATEGORY_DOC_REQUIRED_LIST_URL =
      URL + "get-workcategory-doc-required-list.php";
  static String POST_MOBILE_APP_ACCOUNT_INFO_URL =
      URL + "get-mobile-app-account-info.php";
  static String POST_BILL_RECEIPT_URL = URL + "get-bill-receipt.php";
 static String VERSION_CHECK =
      URL + "version_check.php";
  static String POST_CORE_DOCUMENT_LIST_URL =
      URL + "get-customer-core-documents-list.php";
  static String POST_CUSTOMER_DOCUMENT_SCREEN_1_URL =
      URL + "get-customer-document-screen-1.php";
  static String POST_SERVICE_REQUEST_LIST_URL =
      URL + "service-request-list.php";
  static String POST_CUSTOMER_DOCUMENT_SCREEN_2_URL =
      URL + "get-customer-document-screen-2.php";
  static String POST_CUSTOMER_DOCUMENT_SCREEN_3_URL =
      URL + "get-customer-document-screen-3.php";
  static String POST_CUSTOMER_OTHER_DOCUMENT_SCREEN_2_URL =
      URL + "get-customer-document-screen-integrated.php";
  static String POST_DECRYPTED_URL = URL + "getDecrypted";
  static String POST_POLICY_LIST_URL = URL + "policies";
  static String POST_EMPLOYEE_LIST_URL = URL + "corporateemploies";

  static String MEMBER_FAMILY_URL = URL + "memberfamily";
  static String MEMBER_POLICY_URL = URL + "memberpolicy";
  static String MEMBER_RECEIPT_URL = URL + "memberreceipts";
  static String MEMBER_REGISTRATION_URL = URL + "memberregistration";
  static String MEMBER_OTP_VERIFICATION_URL = URL + "validatedmemberotp";
  static String MEMBER_FORGOT_PASSWORD_URL = URL + "forwardMemberPassword";
  static String TICKET_LIST_URL = URL + "list-ticket";
  static String GET_STATES_URL = URL + "states";
  static String GET_ATTENDANCE_URL = URL + "attendance/listing";
  static String GET_CURRENT_ATTENDANCE_URL = URL + "attendance/current-day";
  static String LEAVE_LIST_URL = URL + "employee/leave/list";
  static String POST_LEAVE_APPLY_URL = URL + "employee/leave";
  static String ATTENDANCE_INOUT_URL = URL + "attendance";
  static String ATTENDANCE_BREAK_URL = URL + "employee/break";
  static String GET_CITIES_URL = URL + "cities";
  static String GET_HOSPITALS_URL = URL + "hospitals";
  static String DASHBOARD_URL = URL + "claimcount";
  static String ACCEPT_REJECT_LEAVE_URL = URL + "employee/leave-status-update";
  static String TPA_DETAILS_URL = URL + "tpadetails";
  static String GET_SUB_USER_URL = URL + "sub-users";
  static String GET_KRA_LIST_URL = URL + "kra";
  static String POST_UPDATE_PROFILE_PICTURE_URL = URL + "user-profile/avatar";
  static String POST_EXPENSE_REPORT_URL = URL + "expense-report";
  static String POST_EXPENSE_LIST_URL = URL + "expense-list";
  static String GET_USER_DETAILS_URL = URL + "user-details";
  static String POST_EXPENSE_REPORT_LIST_URL = URL + "expense-report/list";
  static String POST_SELF_TARGET_LIST_URL = URL + "self-sales-targets";
  static String POST_PRODUCT_LIST_URL = URL + "list-product";
  static String POST_ORDER_LIST_URL = URL + "orders";
  static String POST_CREATE_ORDER_URL = URL + "create-order";
  static String GET_WEIGHT_VARIENT_URL = URL + "weight-variant-values";
  static String GET_THICKNESS_VARIENT_URL = URL + "thickness-variant-values";
  static String GET_SALE_DASHBOARD_URL = URL + "distributor-order-statistics";
  static String POST_ORDER_DETAILS_URL = URL + "order-details";
  static String POST_DEALER_ORDER_STATUS_URL = URL + "dealer-order-status";
  static String POST_REPORT_DAMAGE_GOODS_URL = URL + "report-damaged-goods";
  static String POST_EXPENSE_DELETE_URL = URL + "delete-expense";
  static String POST_UPDATE_TASK_URL = URL + "update-task";
  static String POST_MANAGER_EXPENSE_REPORT_APPROVAL_URL =
      URL + "manager-expense-report-approval";
  static String POST_ADD_TASK_URL = URL + "create-task";
  static String POST_EXPENSE_REPORT_DETAILS_LIST_URL =
      URL + "expense-report/expenses";
  static String POST_TICKET_DETAILS_URL = URL + "ticket/details";
  static String POST_GENERATE_KRA_URL = URL + "generate-kra-quarter";
  static String POST_KRA_DETAILS_URL = URL + "quarter-kra";
  static String POST_KRA_RATING_UPDATE_URL = URL + "kra-rating-update";
  static String GET_EXPENSE_CATEGORIES_URL = URL + "expense-categories";
  static String GET_MERCHANT_CATEGORIES_URL = URL + "merchants";
  static String RESET_PASSWORD_URL = URL + "auth/reset-password";
  static String POST_CHANGE_PASSWORD_URL = URL + "change-password";
  static String CHANNEL_NAME = "eOxegen";

  static String NOTICE_BOARD_URL = URL + "notice_board/get_notice";
  static String ADD_NOTICE_BOARD_URL = URL + "notice_board/add_notice";
  static String REPORT_ISSUE_URL = URL + "resident/add_helpdesk";
  static String ADD_VEHICLE_URL = URL + "resident/add_vehicle";
  static String VEHICLE_TYPE_URL = URL + "masters/vehicle_types";
  static String DELETE_VEHICLE_URL = URL + "resident/delete_vehicle";
  static String DELETE_MEMBER_URL = URL + "resident/delete_family_member";
  static String DELETE_STAFF_URL = URL + "local_service_map/remove_staff";
  static String DELETE_COMPLAINT_URL = URL + "complaints/delete_complaint";
  static String ADD_SERVICE_URL = URL + "local_service/add_local_service";
  static String ADD_COMPLAINT_URL = URL + "complaints/add_complaints";
  static String ADD_EXPENSE_URL = URL + "expense";
  static String PROFILE_DETAILS_URL = URL + "resident/profile";
  static String REGISTRATION_URL = URL + "resident/resident_registration";
  static String ADD_NEW_FLAT_URL = URL + "resident/add_new_flat";
  static String CUSTOMER_CREATE_FOLDER_URL = URL + "customer-create-folder.php";
  static String CUSTOMER_FILE_UPLOAD_URL = URL + "customer-file-upload.php";

  static String GET_ALL_NOTIFICATION_URL =
      URL + "resident/resident_registration";
}

