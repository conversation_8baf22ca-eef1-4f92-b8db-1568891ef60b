class TaskListModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  TaskListModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  TaskListModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _taskId;
  String? _taskTitle;
  String? _endDate;
  String? _workcategory;
  String? _workingResource;
  String? _photoPath;
  String? _tStatus;
  String? _taskProgress;
  String? _dataDependency;
  String? _asessmentPeriod;
  String? _contactNumber;
  String? _mailTo;

  Data(
      {String? taskId,
        String? taskTitle,
        String? endDate,
        String? workcategory,
        String? workingResource,
        String? photoPath,
        String? tStatus,
        String? taskProgress,
        String? dataDependency,
        String? asessmentPeriod,
        String? contactNumber,
        String? mailTo}) {
    if (taskId != null) {
      this._taskId = taskId;
    }
    if (taskTitle != null) {
      this._taskTitle = taskTitle;
    }
    if (endDate != null) {
      this._endDate = endDate;
    }
    if (workcategory != null) {
      this._workcategory = workcategory;
    }
    if (workingResource != null) {
      this._workingResource = workingResource;
    }
    if (photoPath != null) {
      this._photoPath = photoPath;
    }
    if (tStatus != null) {
      this._tStatus = tStatus;
    }
    if (taskProgress != null) {
      this._taskProgress = taskProgress;
    }
    if (dataDependency != null) {
      this._dataDependency = dataDependency;
    }
    if (asessmentPeriod != null) {
      this._asessmentPeriod = asessmentPeriod;
    }
    if (contactNumber != null) {
      this._contactNumber = contactNumber;
    }
    if (mailTo != null) {
      this._mailTo = mailTo;
    }
  }

  String? get taskId => _taskId;
  set taskId(String? taskId) => _taskId = taskId;
  String? get taskTitle => _taskTitle;
  set taskTitle(String? taskTitle) => _taskTitle = taskTitle;
  String? get endDate => _endDate;
  set endDate(String? endDate) => _endDate = endDate;
  String? get workcategory => _workcategory;
  set workcategory(String? workcategory) => _workcategory = workcategory;
  String? get workingResource => _workingResource;
  set workingResource(String? workingResource) =>
      _workingResource = workingResource;
  String? get photoPath => _photoPath;
  set photoPath(String? photoPath) => _photoPath = photoPath;
  String? get tStatus => _tStatus;
  set tStatus(String? tStatus) => _tStatus = tStatus;
  String? get taskProgress => _taskProgress;
  set taskProgress(String? taskProgress) => _taskProgress = taskProgress;
  String? get dataDependency => _dataDependency;
  set dataDependency(String? dataDependency) =>
      _dataDependency = dataDependency;
  String? get asessmentPeriod => _asessmentPeriod;
  set asessmentPeriod(String? asessmentPeriod) =>
      _asessmentPeriod = asessmentPeriod;
  String? get contactNumber => _contactNumber;
  set contactNumber(String? contactNumber) => _contactNumber = contactNumber;
  String? get mailTo => _mailTo;
  set mailTo(String? mailTo) => _mailTo = mailTo;

  Data.fromJson(Map<String, dynamic> json) {
    _taskId = json['task_id'];
    _taskTitle = json['task_title'];
    _endDate = json['end_date'];
    _workcategory = json['workcategory'];
    _workingResource = json['working_resource'];
    _photoPath = json['photo_path'];
    _tStatus = json['t_status'];
    _taskProgress = json['task_progress'];
    _dataDependency = json['data_dependency'];
    _asessmentPeriod = json['asessment_period'];
    _contactNumber = json['contact_number'];
    _mailTo = json['mail_to'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['task_id'] = this._taskId;
    data['task_title'] = this._taskTitle;
    data['end_date'] = this._endDate;
    data['workcategory'] = this._workcategory;
    data['working_resource'] = this._workingResource;
    data['photo_path'] = this._photoPath;
    data['t_status'] = this._tStatus;
    data['task_progress'] = this._taskProgress;
    data['data_dependency'] = this._dataDependency;
    data['asessment_period'] = this._asessmentPeriod;
    data['contact_number'] = this._contactNumber;
    data['mail_to'] = this._mailTo;
    return data;
  }
}
