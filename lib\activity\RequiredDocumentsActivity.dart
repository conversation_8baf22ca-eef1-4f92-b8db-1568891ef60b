import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/fragment/InvoicesFragment.dart';
import 'package:erpcacustomer/fragment/DocumentDetailsFragment.dart';
import 'package:erpcacustomer/fragment/RequiredDocumentsFragment.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';

class RequiredDocumentsActivity extends StatefulWidget {
  String? workcategoryId;
  String? service_request_id;

  RequiredDocumentsActivity(
      {Key? key, this.workcategoryId, this.service_request_id})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    // TODO: implement createState
    return RequiredDocumentsActivityState();
  }
}

class RequiredDocumentsActivityState extends State<RequiredDocumentsActivity> {
  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Scaffold(
      resizeToAvoidBottomInset: false,
      /*   appBar: AppBar(
        leading: IconButton(
            icon: Icon(Icons.arrow_back),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          "List Of Documents",
          style: new CSSStyle().poppinsWhiteRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().erpca_blue_color),
      ),*/
      /*    floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pushNamedAndRemoveUntil(
              '/SelectMemberActivity', (Route<dynamic> route) => true);
        },
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
        backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
      ),*/
      body: RequiredDocumentsFragment(
        workcategoryId: widget.workcategoryId.toString(),
        service_request_id: widget.service_request_id.toString(),
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
