class CoreDocumentListModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  CoreDocumentListModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CoreDocumentListModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _docTitle;
  String? _filePath;
  String? _shortFilePath;
  String? _uploadedOn;
  String? _createdBy;
  String? _kycId;

  Data(
      {String? docTitle,
        String? filePath,
        String? shortFilePath,
        String? uploadedOn,
        String? createdBy,
        String? kycId}) {
    if (docTitle != null) {
      this._docTitle = docTitle;
    }
    if (filePath != null) {
      this._filePath = filePath;
    }
    if (shortFilePath != null) {
      this._shortFilePath = shortFilePath;
    }
    if (uploadedOn != null) {
      this._uploadedOn = uploadedOn;
    }
    if (createdBy != null) {
      this._createdBy = createdBy;
    }
    if (kycId != null) {
      this._kycId = kycId;
    }
  }

  String? get docTitle => _docTitle;
  set docTitle(String? docTitle) => _docTitle = docTitle;
  String? get filePath => _filePath;
  set filePath(String? filePath) => _filePath = filePath;
  String? get shortFilePath => _shortFilePath;
  set shortFilePath(String? shortFilePath) => _shortFilePath = shortFilePath;
  String? get uploadedOn => _uploadedOn;
  set uploadedOn(String? uploadedOn) => _uploadedOn = uploadedOn;
  String? get createdBy => _createdBy;
  set createdBy(String? createdBy) => _createdBy = createdBy;
  String? get kycId => _kycId;
  set kycId(String? kycId) => _kycId = kycId;

  Data.fromJson(Map<String, dynamic> json) {
    _docTitle = json['doc_title'];
    _filePath = json['file_path'];
    _shortFilePath = json['short_file_path'];
    _uploadedOn = json['uploaded_on'];
    _createdBy = json['created_by'];
    _kycId = json['kyc_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['doc_title'] = this._docTitle;
    data['file_path'] = this._filePath;
    data['short_file_path'] = this._shortFilePath;
    data['uploaded_on'] = this._uploadedOn;
    data['created_by'] = this._createdBy;
    data['kyc_id'] = this._kycId;
    return data;
  }
}
