class User {
  String clientBusinessName;
  String clientName;
  String userId;
  String customerGroup;

  User(this.clientBusinessName, this.clientName, this.customerGroup,
      this.userId);

/*static List<User> getUsers() {
    return <User>[
      User(userId: 1, firstName: "<PERSON>", lastName: "<PERSON>"),
      User(userId: 2, firstName: "<PERSON>", lastName: "<PERSON>"),
      User(userId: 3, firstName: "<PERSON>", lastName: "<PERSON>"),
      User(userId: 4, firstName: "Deep", lastName: "<PERSON>"),
      User(userId: 5, firstName: "<PERSON>", lastName: "<PERSON>"),
    ];
  }*/
}
