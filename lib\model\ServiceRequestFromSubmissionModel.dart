class ServiceRequestFromSubmissionModel {
  int? _status;
  String? _message;
  int? _success;
  String? _errorMsg;
  String? _serviceRequestId;
  String? _errorDev;

  ServiceRequestFromSubmissionModel(
      {int? status,
        String? message,
        int? success,
        String? errorMsg,
        String? serviceRequestId,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (errorMsg != null) {
      this._errorMsg = errorMsg;
    }
    if (serviceRequestId != null) {
      this._serviceRequestId = serviceRequestId;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  String? get errorMsg => _errorMsg;
  set errorMsg(String? errorMsg) => _errorMsg = errorMsg;
  String? get serviceRequestId => _serviceRequestId;
  set serviceRequestId(String? serviceRequestId) =>
      _serviceRequestId = serviceRequestId;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  ServiceRequestFromSubmissionModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    _errorMsg = json['error_msg'];
    _serviceRequestId = json['service_request_id'];
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    data['error_msg'] = this._errorMsg;
    data['service_request_id'] = this._serviceRequestId;
    data['error_dev'] = this._errorDev;
    return data;
  }
}
