import 'dart:isolate';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/controller/DocumentController.dart';
import 'package:erpcacustomer/model/CustomerDocumentScreen3Model.dart';
import 'package:flutter/material.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:get/get.dart';

class CustomerDocumentScreen3Fragment extends StatefulWidget {
  String asst_year;
  String workcategory_id;

  CustomerDocumentScreen3Fragment(
      {Key? key, required this.asst_year, required this.workcategory_id})
      : super(key: key);
  @override
  CustomerDocumentScreen3FragmentState createState() {
    return new CustomerDocumentScreen3FragmentState();
  }
}

class CustomerDocumentScreen3FragmentState
    extends State<CustomerDocumentScreen3Fragment> {
  bool ? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    FlutterDownloader.registerCallback(downloadCallback);
  }

  static void downloadCallback(
      String id, int status, int progress) {
    if (true) {

    }
    final SendPort? send =
        IsolateNameServer.lookupPortByName('downloader_send_port');
    send!.send([id, status, progress]);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    _media = MediaQuery.of(context).size;

    children.add(_buildBackground());
    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
          primaryColor: PrimaryColor, fontFamily: 'GlacialIndifference'),
      home: Stack(
        children: children,
      ),
    );
  }

  Widget _buildBackground() {
    return Stack(children: <Widget>[
      //Above card
      NotificationListener<OverscrollIndicatorNotification>(
        onNotification: (overscroll) {
          overscroll.disallowIndicator();
          return true;
        },
        child: FutureBuilder<CustomerDocumentScreen3Model>(
          future: postCustomerDocumentScreen3Call(
              Constants.ACC_ID,
              Constants.USER_ID,
              widget.asst_year,
              widget.workcategory_id,
              context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? ListView.separated(
                    physics: ClampingScrollPhysics(),
                    shrinkWrap: true,
                    separatorBuilder: (context, index) {
                      return Padding(
                        padding: const EdgeInsets.only(left: 85.0),
                        child: Divider(),
                      );
                    },
                    padding: EdgeInsets.zero,
                    itemCount: snapshot.data!.data!.length,
                    itemBuilder: (BuildContext context, int index) {
                      return _gstBottomValue(snapshot.data!.data![index]);
                    },
                  )
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
      ),
      // Positioned to take only AppBar size
    ]);
  }

   customerDocumentScreen3Call(
      String acc_id,
      String user_id,
      String asst_year,
      String workcategory_id,
      BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await customerDocumentScreen3Api(
          acc_id, user_id, asst_year, workcategory_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        CustomerDocumentScreen3Model currentParsedResponse =
            CustomerDocumentScreen3Model.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<CustomerDocumentScreen3Model> postCustomerDocumentScreen3Call(
      String acc_id,
      String user_id,
      String asst_year,
      String workcategory_id,
      BuildContext context) async {
    return await customerDocumentScreen3Call(acc_id, user_id, asst_year, workcategory_id, context);
  }

  Widget _gstBottomValue(Data data){
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        color: Color(new CommonColor().white_Color),
        elevation: 3,
        child: Container(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 15, right: 13),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      flex: 1,
                      child: Text(
                        data.wcDocName.toString(),
                        style: new CSSStyle().poppinsBlackRegular18(context),
                      ),
                    ),
                  ],
                ),
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  (data.dueDate != "")
                      ? Padding(
                          padding: const EdgeInsets.only(left: 13.0, top: 15),
                          child: Text(
                            data.dueDate.toString(),
                            style:
                                new CSSStyle().poppinsBlackRegular12(context),
                          ),
                        )
                      : Container(),
                  (data.asessmentPeriod != "")
                      ? Padding(
                          padding: const EdgeInsets.only(
                              left: 10.0, top: 15, bottom: 1),
                          child: Card(
                            elevation: 2,
                            child: Container(
                                decoration: new BoxDecoration(
                                  color: Colors.green,
                                  shape: BoxShape.rectangle,
                                  borderRadius: BorderRadius.circular(3),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      left: 5.0, right: 5, top: 5, bottom: 5),
                                  child: Text(
                                    data.asessmentPeriod.toString(),
                                    textAlign: TextAlign.center,
                                    style: new CSSStyle()
                                        .poppinsWhiteRegular12(context),
                                  ),
                                )),
                          ),
                        )
                      : Container(),
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(left: 13.0, top: 15),
                child: Row(
                  children: [
                    Icon(
                      Icons.insert_drive_file,
                      color: Color(new CommonColor().green_light_Color),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(left: 8.0),
                      child: Text(
                        data.taskTitle.toString(),
                        style: new CSSStyle().poppinsBlackRegular12(context),
                      ),
                    ),
                  ],
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                    left: 13.0, top: 15, right: 13, bottom: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    (data.documentPath!= null)
                        ? GestureDetector(
                            onTap: () async {
                              MyUtils().getDownloadUrl1(data.documentPath!);
                            },
                            child: Text(
                              "Download",
                              style:
                                  new CSSStyle().poppinsBlueRegular12(context),
                            ),
                          )
                        : Text(
                            "",
                      style: new CSSStyle().poppinsBlueRegular12(context),
                          ),
                    GestureDetector(
                      onTap: () {
                        new MyUtils().getDownloadUrl1(data.documentPath!);
                      },
                      child: Text(
                        "",
                        style: new CSSStyle().poppinsRedRegular12(context),
                      ),
                    ),

                /*    GestureDetector(
                      onTap: () async {
                        final tasks = await FlutterDownloader.loadTasks();


                        if (tasks.length > 0) {
                          int urlMatchCount = -1;
                          for (int i = 0; i < tasks.length; i++) {
                            if (data.documentPath == tasks[i].url) {
                              urlMatchCount = i;
                              break;
                            }
                          }

                          if (urlMatchCount != -1) {
                            MyUtils().shareFile(
                                "/sdcard/download/erpcacustomer" +
                                    "/" +
                                    tasks[urlMatchCount].filename,
                                data.taskTitle.toString());

                            *//* if (data.documentPath == tasks[urlMatchCount].url) {
                              if (tasks[urlMatchCount].status ==
                                  DownloadTaskStatus.undefined) {
                                new MyUtils().getDownloadUrl(
                                    context, data.documentPath, false);
                              } else if (tasks[urlMatchCount].status ==
                                  DownloadTaskStatus.complete) {
                                FlutterDownloader.open(
                                    taskId: tasks[urlMatchCount].taskId);
                              }
                            } else {
                              new MyUtils().getDownloadUrl(
                                  context, data.documentPath, false);
                            }

                          *//*
                          } else {
                            new MyUtils().getDownloadUrl(
                                context, data.documentPath, false);
                          }
                        } else {
                          new MyUtils().getDownloadUrl(
                              context, data.documentPath, false);
                        }
                      },
                      child: Text(
                        "Share",
                        style: new CSSStyle().poppinsGreenRegular12(context),
                      ),
                    ),*/
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
