import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/CommonText.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/common/PreferenceManagerUtil.dart';
//import 'package:firebase_core/firebase_core.dart';
//import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sqflite/sqflite.dart';
import 'package:get/get.dart';

import 'HomePageActivity.dart';
import 'SetConfirmMPinActivity.dart';
import 'SelectUserActivity.dart';

class SetConfirmMPinActivity extends StatefulWidget {
  final String? mPin;

  SetConfirmMPinActivity({Key? key, @required this.mPin}) : super(key: key);

  @override
  SetConfirmMPinActivityState createState() =>
      SetConfirmMPinActivityState(mPin: mPin.toString());
}

class SetConfirmMPinActivityState extends State<SetConfirmMPinActivity> {
  // GetX reactive variables
  var confirmMPinObs = ''.obs;
  var isLoading = false.obs;

  // Original variables
  bool _obscureText = true;
  bool _saving = false;
  bool _savingForgotPassword = false;
  String myToken = "";
  String mPin;

  var checkedValue = false;
  TextEditingController controller1 = new TextEditingController();
  TextEditingController controllerConfirm1 = new TextEditingController();
  TextEditingController controller2 = new TextEditingController();
  TextEditingController controllerConfirm2 = new TextEditingController();
  TextEditingController controller3 = new TextEditingController();
  TextEditingController controllerConfirm3 = new TextEditingController();
  TextEditingController controller4 = new TextEditingController();
  TextEditingController controllerConfirm4 = new TextEditingController();
  TextEditingController controller5 = new TextEditingController();
  TextEditingController controllerConfirm5 = new TextEditingController();
  TextEditingController currController = new TextEditingController();
  TextEditingController currConfirmController = new TextEditingController();

  var _form2Key = GlobalKey<FormState>();
  String userName = "";
  String password = "";
 // FirebaseMessaging firebaseMessaging = new FirebaseMessaging();
  FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
      new FlutterLocalNotificationsPlugin();
  Map<String, String> headerHttp = new Map();
  bool hasPinError = false;
  bool hasConfirmPinError = false;

  // TextEditingController userNameController = TextEditingController();
  // TextEditingController passwordController = TextEditingController();
 // FirebaseApp firebaseApp;
  var _formKey = GlobalKey<FormState>();
  bool _isLoggedIn = false;
  String googleuserName = "";
  TextEditingController mPincontroller = TextEditingController();
  TextEditingController mConfirmPincontroller = TextEditingController();
  String confirmMPin = "";

  SetConfirmMPinActivityState({
    Key? key,
    required this.mPin,
  });
  update(String token) {
    myToken = token;
    // GetX reactive update - NO setState needed!
  }

  @override
  void dispose() {
    super.dispose();
    controller1.dispose();
    controllerConfirm1.dispose();
    controller2.dispose();
    controllerConfirm2.dispose();
    controller3.dispose();
    controllerConfirm3.dispose();
    controller4.dispose();
    controllerConfirm4.dispose();
    controller5.dispose();
    controllerConfirm5.dispose();
    // controller6.dispose();
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    print("Passed mPin is: ${widget.mPin}");
 /*   firebaseApp = FirebaseApp.instance;
    firebaseMessaging.getToken().then((token) {
      update(token);
    });
 */   currController = controller1;
    currConfirmController = controllerConfirm1;
  }

  void inputTextToField(String str) {
    //Edit first textField
    if (currController == controller1) {
      controller1.text = str;
      currController = controller2;
    }

    //Edit second textField
    else if (currController == controller2) {
      controller2.text = str;
      currController = controller3;
    }

    //Edit third textField
    else if (currController == controller3) {
      controller3.text = str;
      currController = controller4;
    }

    //Edit fourth textField
    else if (currController == controller4) {
      controller4.text = str;
      currController = controller5;
    }

    //Edit fifth textField
/*    else if (currController == controller5) {
      controller5.text = str;
      currController = controller6;
    }

    //Edit sixth textField
    else if (currController == controller6) {
      controller6.text = str;
      currController = controller6;
    }*/
  }

  void inputTextToFieldConfirm(String str) {
    //Edit first textField
    if (currController == controllerConfirm1) {
      controllerConfirm1.text = str;
      currConfirmController = controllerConfirm2;
    }

    //Edit second textField
    else if (currConfirmController == controllerConfirm2) {
      controllerConfirm2.text = str;
      currConfirmController = controllerConfirm3;
    }

    //Edit third textField
    else if (currConfirmController == controllerConfirm3) {
      controllerConfirm3.text = str;
      currConfirmController = controllerConfirm4;
    }

    //Edit fourth textField
    else if (currConfirmController == controllerConfirm4) {
      controllerConfirm4.text = str;
      currConfirmController = controllerConfirm5;
    }

    //Edit fifth textField
/*    else if (currController == controller5) {
      controller5.text = str;
      currController = controller6;
    }

    //Edit sixth textField
    else if (currController == controller6) {
      controller6.text = str;
      currController = controller6;
    }*/
  }

  void deleteText() {
    if (currController.text.length == 0) {
    } else {
      currController.text = "";
      currController = controller4;
      return;
    }

    if (currController == controller1) {
      controller1.text = "";
    } else if (currController == controller2) {
      controller1.text = "";
      currController = controller1;
    } else if (currController == controller3) {
      controller2.text = "";
      currController = controller2;
    } else if (currController == controller4) {
      controller3.text = "";
      currController = controller3;
    } else if (currController == controller5) {
      controller4.text = "";
      currController = controller4;
    }
    /* else if (currController == controller6) {
      controller5.text = "";
      currController = controller5;
    }*/
  }

  void deleteConfirmText() {
    if (currConfirmController.text.length == 0) {
    } else {
      currConfirmController.text = "";
      currConfirmController = controllerConfirm4;
      return;
    }

    if (currConfirmController == controllerConfirm1) {
      controllerConfirm1.text = "";
    } else if (currConfirmController == controllerConfirm2) {
      controllerConfirm1.text = "";
      currConfirmController = controllerConfirm1;
    } else if (currConfirmController == controllerConfirm3) {
      controllerConfirm2.text = "";
      currConfirmController = controllerConfirm2;
    } else if (currConfirmController == controllerConfirm4) {
      controllerConfirm3.text = "";
      currConfirmController = controllerConfirm3;
    } else if (currConfirmController == controllerConfirm5) {
      controllerConfirm4.text = "";
      currConfirmController = controllerConfirm4;
    }
    /* else if (currController == controller6) {
      controller5.text = "";
      currController = controller5;
    }*/
  }

  String? _validateUserName(String? user_name) {
    if (user_name!.isEmpty) {
      return CommonText().user_name;
    } else {
      userName = user_name;
    }
  }

  void verifyLogin(String confirmMPin, BuildContext context) {
    print("Passed conirmPin is: ${confirmMPin}");
    print("Passed mPin is: ${widget.mPin}");
    if (confirmMPin.isEmpty) {
      MyUtils.showOkDialog(context, "Error", "Please Enter Confirm M-Pin");
    } else if (confirmMPin.length != 4) {
      MyUtils.showOkDialog(
          context, "Error", "Please Enter Proper Confirm M-Pin");
    } else if (confirmMPin != widget.mPin) {
      MyUtils.showOkDialog(
          context, "Error", "M-Pin and Confirm M-Pin should be same");
    } else {
      new PreferenceManagerUtil()
          .setMPin(/* new MyUtils().generateSignature(mPin) */mPin);
          print("con"+mPin);
      /*  Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => HomePageActivity()),
      );
  */
      /*  Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(builder: (context) => HomePageActivity()),
          (Route<dynamic> route) => false);*/

      Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => SelectUserActivity(),
          ));
    }
  }

  void _showAlertDialog(String title, String message) {
    AlertDialog alertDialog = AlertDialog(
      title: Text(title),
      content: Text(message),
    );
    showDialog(context: context, builder: (_) => alertDialog);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> widgetList = [
      Padding(
        padding: EdgeInsets.only(left: 0.0, right: 2.0),
        child: new Container(
          color: Colors.transparent,
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              // border: Border(
              //   bottom: BorderSide(width: 1.0, color: Colors.black),
              // ),
            ),
            child: new TextField(
              inputFormatters: [
                LengthLimitingTextInputFormatter(1),
              ],
              enabled: false,
              controller: controller1,
              obscureText: true,
              autofocus: false,
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 24.0, color: Colors.black),
            )),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(width: 1.0, color: Colors.black),
            // ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            controller: controller2,
            obscureText: true,
            autofocus: false,
            enabled: false,
            keyboardType: TextInputType.number,
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 24.0, color: Colors.black),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(width: 1.0, color: Colors.black),
            // ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            keyboardType: TextInputType.number,
            controller: controller3,
            obscureText: true,
            textAlign: TextAlign.center,
            autofocus: false,
            enabled: false,
            style: TextStyle(fontSize: 24.0, color: Colors.black),
          ),
        ),
      ),
      Padding(
        padding: const EdgeInsets.only(right: 2.0, left: 2.0),
        child: new Container(
          alignment: Alignment.center,
          decoration: BoxDecoration(
            // border: Border(
            //   bottom: BorderSide(width: 1.0, color: Colors.black),
            // ),
          ),
          child: new TextField(
            inputFormatters: [
              LengthLimitingTextInputFormatter(1),
            ],
            textAlign: TextAlign.center,
            controller: controller4,
            obscureText: true,
            autofocus: false,
            enabled: false,
            style: TextStyle(fontSize: 24.0, color: Colors.black),
          ),
        ),
      ),
    ];
    return new Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Color(new CommonColor().black_Color),
      appBar: AppBar(
        leading: IconButton(
            icon: Icon(
              Icons.arrow_back,
              color: Colors.black,
            ),
            onPressed: () {
              moveToLastScreen();
            }),
        title: Text(
          "Confirm you M-PIN",
          style: new CSSStyle().poppinsLightBlackRegular16(context),
        ),
        backgroundColor: Color(new CommonColor().white_Color),
      ),
      body: Stack(
        children: [
          Image.asset(
            'assets/images/login_background.png',
            width: double.infinity,
            fit: BoxFit.fill,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 50.0),
            child: Column(
              children: <Widget>[
                Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Image.asset('assets/images/mpinlogo.png',
                          width: 100.0, height: 100.0)
                    ]),
                Column(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(left: 15, right: 15),
                      child: Text(
                        "Confirm you M-PIN by entering again the same",
                        textAlign: TextAlign.center,
                        style: new CSSStyle()
                            .poppinsLighterBlackRegular16(context),
                      ),
                    ),
                    GridView.count(
                        crossAxisCount: 6,
                        mainAxisSpacing: 10.0,
                        shrinkWrap: true,
                        primary: true,
                        scrollDirection: Axis.vertical,
                        children: List<Container>.generate(
                            5,
                            (int index) =>
                                Container(child: widgetList[index]))),
                    new Container(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 30.0, bottom: 0.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("1");
                              },
                              child: Text("1",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("2");
                              },
                              child: Text("2",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("3");
                              },
                              child: Text("3",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                          ],
                        ),
                      ),
                    ),
                    new Container(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: 8.0, top: 4.0, right: 8.0, bottom: 0.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("4");
                              },
                              child: Text("4",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("5");
                              },
                              child: Text("5",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("6");
                              },
                              child: Text("6",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                          ],
                        ),
                      ),
                    ),
                    new Container(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: 8.0, top: 4.0, right: 8.0, bottom: 0.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("7");
                              },
                              child: Text("7",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("8");
                              },
                              child: Text("8",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("9");
                              },
                              child: Text("9",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                          ],
                        ),
                      ),
                    ),
                    new Container(
                      child: Padding(
                        padding: const EdgeInsets.only(
                            left: 8.0, top: 4.0, right: 8.0, bottom: 0.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            MaterialButton(
                                onPressed: () {
                                  deleteText();
                                },
                                child: Image.asset('assets/images/delete.png',
                                    width: 25.0, height: 25.0)),
                            MaterialButton(
                              onPressed: () {
                                inputTextToField("0");
                              },
                              child: Text("0",
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 25.0,
                                      fontWeight: FontWeight.w400),
                                  textAlign: TextAlign.center),
                            ),
                            MaterialButton(
                                onPressed: () {
                                  mPin = controller1.text +
                                      controller2.text +
                                      controller3.text +
                                      controller4.text;
                                  confirmMPinObs.value = mPin;  // Update GetX reactive variable
                                  verifyLogin(mPin, context);
                                },
                                child: Image.asset('assets/images/success.png',
                                    width: 25.0, height: 25.0)),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  void moveToLastScreen() {
    Navigator.pop(context, true);
    //Navigator.of(context).pushNamed('/DashboardActivity');
  }
}
