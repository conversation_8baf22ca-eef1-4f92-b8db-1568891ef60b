import 'dart:async';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/activity/AddDocumentActivity.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/Constants.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/controller/TaskController.dart';
import 'package:erpcacustomer/model/DocAssignedModel.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/services.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:path_provider/path_provider.dart';

class OtherDocumentFragment extends StatefulWidget {
  String? taskId = "";
  String? taskName = "";

  OtherDocumentFragment({Key? key, this.taskId, this.taskName}) : super(key: key);

  @override
  OtherDocumentFragmentState createState() {
    return new OtherDocumentFragmentState();
  }
}

class OtherDocumentFragmentState extends State<OtherDocumentFragment> {
  // GetX reactive variables
  var hasCardObs = false.obs;
  var localPathObs = ''.obs;
  var documentPrimaryKeyIdObs = ''.obs;
  var isLoadingPathObs = false.obs;
  var directoryPathObs = ''.obs;
  var fileNameObs = ''.obs;
  var isSavingObs = false.obs;

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;
  String _localPath = '';
  String document_primary_key_id = "";
  bool _loadingPath = false;
  String _directoryPath = '';
  List<PlatformFile>? _paths;
  FileType _pickingType = FileType.any;
  bool _multiPick = false;
  String _extension = '';
  String _fileName = '';
  bool _saving = false;
  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    FlutterDownloader.registerCallback(downloadCallback);
  }

  static void downloadCallback(
      String id, int status, int progress) {
    if (true) {

    }
    final SendPort? send =
        IsolateNameServer.lookupPortByName('downloader_send_port');
    send!.send([id, status, progress]);
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];
    _media = MediaQuery.of(context).size;

    children.add(_buildBackground());
    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
          primaryColor: PrimaryColor, fontFamily: 'GlacialIndifference'),
      home: Scaffold(
        floatingActionButton: FloatingActionButton(
          onPressed: () {

            Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => AddDocumentActivity(
                    taskId: widget.taskId,
                    taskname: widget.taskName,
                  )),
            );

          },
          child: Icon(
            Icons.add,
            color: Colors.white,
          ),
          backgroundColor: Color(new CommonColor().oxygen_dark_blue_Color),
        ),
        body: Stack(
          children: children,
        ),
      ),
    );
  }

  Widget _buildBackground() {
    return Stack(children: <Widget>[
      //Above card
      NotificationListener<OverscrollIndicatorNotification>(
        onNotification: (overscroll) {
          overscroll.disallowIndicator();
          return true;
        },
        child: FutureBuilder<DocAssignedModel>(
          future: postDocAssignCall(
              Constants.ACC_ID, Constants.USER_ID, widget.taskId!, context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? Material(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Container(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                children: [
                                  Container(
                                    width: 5,
                                    height: 30,
                                    color: Colors.grey[500],
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(left: 8.0),
                                    child: Container(
                                      width: 320,
                                      child: Text(
                                        "Below are the list of documents shared by our team members for this task",
                                        style: new CSSStyle()
                                            .poppinsGreyRegular12Height5(
                                                context),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          ListView.separated(
                            separatorBuilder: (context, index) {
                              return Padding(
                                padding: const EdgeInsets.only(
                                    left: 13.0, right: 13),
                                child: Divider(),
                              );
                            },
                            physics: ClampingScrollPhysics(),
                            shrinkWrap: true,
                            padding: EdgeInsets.zero,
                            itemCount: snapshot.data!.data!.length,
                            itemBuilder: (BuildContext context, int index) {
                              return _gstBottomValue(
                                  index, snapshot.data!.data![index]);
                            },
                          ),
                        ],
                      ),
                    ),
                  )
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
      ),
      // Positioned to take only AppBar size
    ]);
  }

  DocAssignCall(String acc_id, String user_id,
      String taskId, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson =
          await postDocAssignApi(acc_id, user_id, taskId, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        DocAssignedModel currentParsedResponse =
            DocAssignedModel.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<DocAssignedModel> postDocAssignCall(String acc_id, String user_id,
      String taskId, BuildContext context) async {
    return await DocAssignCall(acc_id, user_id, taskId, context);
  }

  Future<String> _findLocalPath() async {
    final directory = Theme.of(context).platform == TargetPlatform.android
        ? await (getExternalStorageDirectory() as FutureOr<Directory>)
        : await getApplicationDocumentsDirectory();
    return directory.path;
  }

  Widget _gstBottomValue(int index, Data allData) {
    {
      return GestureDetector(
        onTap: () {
          /*  Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => DocumentDetailsSecondActivity(
                      asst_year: allData.asstYear,
                    )),
          );*/
        },
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 13.0, right: 13, top: 5, bottom: 5),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Icon(
                        Icons.insert_drive_file,
                        color: Color(new CommonColor().green_light_Color),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(left: 8.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              width: 230,
                              child: Text(
                                allData.wcDocName.toString(),
                                style: new CSSStyle()
                                    .poppinsGreyRegular12(context),
                              ),
                            ),
                            /*  Text(
                              allData.availableDocuments,
                              style: new CSSStyle()
                                  .poppinsLightBlackRegular16(context),
                            ),*/
                          ],
                        ),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      /*    (allData.isComplete != "1" && allData.filePath == "")
                          ? GestureDetector(
                              onTap: () async {
                                //   imagePicker.showDialog(context);
                                document_primary_key_id =
                                    allData.documentPrimaryKeyId;
                                _openFileExplorer();
                              },
                              child: Icon(
                                Icons.cloud_upload,
                                size: 20,
                                color:
                                    Color(new CommonColor().green_light_Color),
                              ),
                            )
                          : Container(),*/
                      /*  (allData.isComplete != "0")
                          ? Icon(
                              Icons.check,
                              size: 20,
                              color: Color(new CommonColor().green_Color),
                            )
                          : Container(),*/
                      /* (allData.filePath != "" && allData.isComplete != "1")
                          ? GestureDetector(
                              onTap: () {
                                document_primary_key_id =
                                    allData.documentPrimaryKeyId;
                                _onLogoutPressed();
                              },
                              child: Icon(
                                Icons.clear,
                                size: 20,
                                color: Color(new CommonColor().red_Color),
                              ),
                            )
                          : Container(),*/
                      (allData.documentPath != "")
                          ? GestureDetector(
                              onTap: () async {
                                /*   await _dialogCall(allData.filePath, context);
                                new MyUtils().launchURL(allData.documentPath);*/
                                MyUtils().getDownloadUrl1(allData.documentPath!);
                              },
                              child: Icon(
                                Icons.image_search_outlined,
                                size: 20,
                                color:
                                    Color(new CommonColor().blue_lighter_Color),
                              ),
                            )
                          : Container(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }

   _onLogoutPressed() {
    return showDialog(
          context: context,
          builder: (context) => new AlertDialog(
            title: new Text('Are you sure?'),
            content: new Text('Do you really want to delete file?'),
            actions: <Widget>[
              new GestureDetector(
                onTap: () => Navigator.of(context).pop(false),
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("NO"),
                ),
              ),
              SizedBox(height: 16),
              new GestureDetector(
                onTap: () {
                  /* workDeleteRequiredCall(
                  Constants.ACC_ID,
                  Constants.USER_ID,
                  document_primary_key_id,
                  context,
                  _scaffoldKey.currentContext);*/
                },
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text("YES"),
                ),
              ),
            ],
          ),
        ) ??
        false;
  }

  void _openFileExplorer() async {
    // ✅ OPTIMIZED: GetX reactive state management
    _loadingPath = true;
    isLoadingPathObs.value = true;
    // setState(() => _loadingPath = true); // Removed
    try {
      _directoryPath = '';
      _paths = (await FilePicker.platform.pickFiles(
        type: _pickingType,
        allowMultiple: _multiPick,
        allowedExtensions: (_extension?.isNotEmpty ?? false)
            ? _extension?.replaceAll(' ', '').split(',')
            : null,
      ))
          ?.files;
    } on PlatformException catch (e) {
    } catch (ex) {
    }
    if (!mounted) return;
    // ✅ OPTIMIZED: GetX reactive state management
    _loadingPath = false;
    _saving = true;
    isLoadingPathObs.value = false;
    isSavingObs.value = true;
    showAlertDialog(
        _scaffoldKey.currentContext!, "Please wait while uploading...");
    /*    workUploadRequiredCall(Constants.ACC_ID, Constants.USER_ID, document_primary_key_id,
        _paths.first.path, context);*/
    _fileName = _paths != null ? _paths!.map((e) => e.name).toString() : '...';
    fileNameObs.value = _fileName;
    // setState(() { ... }); // Removed
  }

  showAlertDialog(BuildContext context, String textName) {
    AlertDialog alert = AlertDialog(
      content: Container(
        height: 70,
        child: new Column(
          children: [
            CircularProgressIndicator(),
            Container(
                margin: EdgeInsets.only(left: 5, right: 5, top: 15),
                child: Text(
                  textName,
                  style: new CSSStyle().poppinsBlackRegular14(context),
                )),
          ],
        ),
      ),
    );
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }
}
