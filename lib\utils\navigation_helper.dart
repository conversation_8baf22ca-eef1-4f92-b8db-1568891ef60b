// GetX Navigation Helper
// Provides easy-to-use navigation methods for the ERPC Customer App

import 'package:get/get.dart';
import '../routes/app_routes.dart';

class NavigationHelper {
  // Private constructor to prevent instantiation
  NavigationHelper._();

  // Navigation to specific screens
  static void toSplash() {
    Get.offAllNamed(Routes.SPLASH);
  }

  static void toHome() {
    Get.offAllNamed(Routes.HOME);
  }

  static void toLogin() {
    Get.toNamed(Routes.LOGIN);
  }

  static void toLoginMobile() {
    Get.toNamed(Routes.LOGIN_MOBILE);
  }

  static void toSelectUser() {
    Get.toNamed(Routes.SELECT_USER);
  }

  static void toCheckMPin() {
    Get.offAllNamed(Routes.CHECK_MPIN);
  }

  static void toSubmitRequest() {
    Get.toNamed(Routes.SUBMIT_REQUEST);
  }

  static void toNotifications() {
    Get.toNamed(Routes.NOTIFICATIONS);
  }

  static void toNewServices() {
    Get.offAllNamed(Routes.NEW_SERVICES);
  }

  // Navigation with parameters
  static void toSubmitRequestWithId(String serviceRequestId) {
    Get.toNamed(Routes.SUBMIT_REQUEST, arguments: {'service_request_id': serviceRequestId});
  }

  static void toSelectUserWithOtp(String mobileOtp) {
    Get.toNamed(Routes.SELECT_USER, arguments: {'mobile_otp': mobileOtp});
  }

  static void toLoginMobileWithNumber(String mobileNo) {
    Get.toNamed(Routes.LOGIN_MOBILE, arguments: {'mobile_no': mobileNo});
  }

  // Back navigation
  static void back() {
    Get.back();
  }

  // Close all dialogs and go back
  static void backUntil(String routeName) {
    Get.until((route) => route.settings.name == routeName);
  }

  // Replace current screen
  static void offNamed(String routeName) {
    Get.offNamed(routeName);
  }

  // Replace all screens and go to new one
  static void offAllNamed(String routeName) {
    Get.offAllNamed(routeName);
  }

  // Show snackbar
  static void showSnackbar(String title, String message, {bool isError = false}) {
    Get.snackbar(
      title,
      message,
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: isError ? Get.theme.colorScheme.error : Get.theme.primaryColor,
      colorText: Get.theme.colorScheme.onPrimary,
      duration: Duration(seconds: 3),
    );
  }

  // Show success message
  static void showSuccess(String message) {
    showSnackbar("Success", message, isError: false);
  }

  // Show error message
  static void showError(String message) {
    showSnackbar("Error", message, isError: true);
  }

  // Get current route name
  static String? getCurrentRoute() {
    return Get.currentRoute;
  }

  // Check if we can go back
  static bool canGoBack() {
    return Get.key.currentState?.canPop() ?? false;
  }
}
