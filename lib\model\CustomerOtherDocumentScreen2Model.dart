class CustomerOtherDocumentScreen2Model {
  int? _status;
  String? _message;
  int? _success;
  List<SubFolders>? _subFolders;
  List<FileList>? _fileList;
  String? _errorDev;

  CustomerOtherDocumentScreen2Model(
      {int? status,
        String? message,
        int? success,
        List<SubFolders>? subFolders,
        List<FileList>? fileList,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (subFolders != null) {
      this._subFolders = subFolders;
    }
    if (fileList != null) {
      this._fileList = fileList;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<SubFolders>? get subFolders => _subFolders;
  set subFolders(List<SubFolders>? subFolders) => _subFolders = subFolders;
  List<FileList>? get fileList => _fileList;
  set fileList(List<FileList>? fileList) => _fileList = fileList;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CustomerOtherDocumentScreen2Model.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['sub_folders'] != null) {
      _subFolders = <SubFolders>[];
      json['sub_folders'].forEach((v) {
        _subFolders!.add(new SubFolders.fromJson(v));
      });
    }
    if (json['file_list'] != null) {
      _fileList = <FileList>[];
      json['file_list'].forEach((v) {
        _fileList!.add(new FileList.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._subFolders != null) {
      data['sub_folders'] = this._subFolders!.map((v) => v.toJson()).toList();
    }
    if (this._fileList != null) {
      data['file_list'] = this._fileList!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class FileList {
  String? _fileId;
  String? _filepath;
  String? _filename;
  String? _createdOn;
  String? _createdByName;

  FileList(
      {String? fileId,
        String? filepath,
        String? filename,
        String? createdOn,
        String? createdByName}) {
    if (fileId != null) {
      this._fileId = fileId;
    }
    if (filepath != null) {
      this._filepath = filepath;
    }
    if (filename != null) {
      this._filename = filename;
    }
    if (createdOn != null) {
      this._createdOn = createdOn;
    }
    if (createdByName != null) {
      this._createdByName = createdByName;
    }
  }

  String? get fileId => _fileId;
  set fileId(String? fileId) => _fileId = fileId;
  String? get filepath => _filepath;
  set filepath(String? filepath) => _filepath = filepath;
  String? get filename => _filename;
  set filename(String? filename) => _filename = filename;
  String? get createdOn => _createdOn;
  set createdOn(String? createdOn) => _createdOn = createdOn;
  String? get createdByName => _createdByName;
  set createdByName(String? createdByName) => _createdByName = createdByName;

  FileList.fromJson(Map<String, dynamic> json) {
    _fileId = json['file_id'];
    _filepath = json['filepath'];
    _filename = json['filename'];
    _createdOn = json['created_on'];
    _createdByName = json['created_by_name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['file_id'] = this._fileId;
    data['filepath'] = this._filepath;
    data['filename'] = this._filename;
    data['created_on'] = this._createdOn;
    data['created_by_name'] = this._createdByName;
    return data;
  }
}

class SubFolders {
  String? _id;
  String? _parentId;
  String? _folderName;
  String? _accId;
  String? _customerId;
  String? _createdOn;

  SubFolders(
      {String? id,
        String? parentId,
        String? folderName,
        String? accId,
        String? customerId,
        String? createdOn}) {
    if (id != null) {
      this._id = id;
    }
    if (parentId != null) {
      this._parentId = parentId;
    }
    if (folderName != null) {
      this._folderName = folderName;
    }
    if (accId != null) {
      this._accId = accId;
    }
    if (customerId != null) {
      this._customerId = customerId;
    }
    if (createdOn != null) {
      this._createdOn = createdOn;
    }
  }

  String? get id => _id;
  set id(String? id) => _id = id;
  String? get parentId => _parentId;
  set parentId(String? parentId) => _parentId = parentId;
  String? get folderName => _folderName;
  set folderName(String? folderName) => _folderName = folderName;
  String? get accId => _accId;
  set accId(String? accId) => _accId = accId;
  String? get customerId => _customerId;
  set customerId(String? customerId) => _customerId = customerId;
  String? get createdOn => _createdOn;
  set createdOn(String? createdOn) => _createdOn = createdOn;

  SubFolders.fromJson(Map<String, dynamic> json) {
    _id = json['id'];
    _parentId = json['parent_id'];
    _folderName = json['folder_name'];
    _accId = json['acc_id'];
    _customerId = json['customer_id'];
    _createdOn = json['created_on'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this._id;
    data['parent_id'] = this._parentId;
    data['folder_name'] = this._folderName;
    data['acc_id'] = this._accId;
    data['customer_id'] = this._customerId;
    data['created_on'] = this._createdOn;
    return data;
  }
}

