// Document Service
// Handles document-related API operations

import 'dart:io';
import 'package:get/get.dart';
import 'api_service.dart';

class DocumentService extends GetxService {
  final ApiService _apiService = Get.find<ApiService>();

  // Get all documents
  Future<List<Map<String, dynamic>>> getAllDocuments() async {
    try {
      final response = await _apiService.get('/documents');
      if (response['success']) {
        return List<Map<String, dynamic>>.from(response['data']['documents']);
      }
      return [];
    } catch (e) {
      print('Get documents error: $e');
      return [];
    }
  }

  // Upload document
  Future<Map<String, dynamic>> uploadDocument(File file, {Map<String, String>? metadata}) async {
    try {
      return await _apiService.uploadFile('/documents/upload', file, fields: metadata);
    } catch (e) {
      return {'success': false, 'message': 'Failed to upload document'};
    }
  }

  // Download document
  Future<Map<String, dynamic>> downloadDocument(String documentId) async {
    try {
      return await _apiService.get('/documents/$documentId/download');
    } catch (e) {
      return {'success': false, 'message': 'Failed to download document'};
    }
  }

  // Delete document
  Future<Map<String, dynamic>> deleteDocument(String documentId) async {
    try {
      return await _apiService.delete('/documents/$documentId');
    } catch (e) {
      return {'success': false, 'message': 'Failed to delete document'};
    }
  }
}
