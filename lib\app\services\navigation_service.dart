// Navigation Service
// Enhanced navigation service with GetX routing

import 'package:get/get.dart';
import '../routes/app_routes.dart';

class NavigationService extends GetxService {
  // Navigation history
  final navigationHistory = <String>[].obs;
  
  @override
  void onInit() {
    super.onInit();
    // Initialize navigation history
    _addToHistory(Get.currentRoute);
  }

  // Add route to history
  void _addToHistory(String route) {
    navigationHistory.add(route);
    // Keep only last 10 routes
    if (navigationHistory.length > 10) {
      navigationHistory.removeAt(0);
    }
  }

  // Enhanced navigation methods
  Future<T?> toNamed<T>(String routeName, {dynamic arguments}) async {
    return await Get.toNamed<T>(routeName, arguments: arguments);
  }

  Future<T?> offNamed<T>(String routeName, {dynamic arguments}) async {
    return await Get.offNamed<T>(routeName, arguments: arguments);
  }

  Future<T?> offAllNamed<T>(String routeName, {dynamic arguments}) async {
    return await Get.offAllNamed<T>(routeName, arguments: arguments);
  }

  // Quick navigation methods
  void toHome() => offAllNamed(Routes.HOME);
  void toLogin() => offAllNamed(Routes.LOGIN_MOBILE);
  void toSplash() => offAllNamed(Routes.SPLASH);
  
  // Back navigation
  void back({dynamic result}) => Get.back(result: result);
  
  // Check if can go back
  bool canGoBack() => Get.key.currentState?.canPop() ?? false;
  
  // Get current route
  String? get currentRoute => Get.currentRoute;
  
  // Clear navigation history
  void clearHistory() => navigationHistory.clear();
}
