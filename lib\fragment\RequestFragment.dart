import 'package:connectivity_plus/connectivity_plus.dart';

import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/MyUtils.dart';
import 'package:erpcacustomer/controller/DocumentController.dart';
import 'package:erpcacustomer/model/ServiceRequestListModel.dart';
import 'package:erpcacustomer/activity/NewServicesActivity.dart';
import 'package:erpcacustomer/activity/RequiredDocumentsActivity.dart';
import 'package:flutter/material.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:get/get.dart';
import 'package:erpcacustomer/common/Constants.dart';

class RequestFragment extends StatefulWidget {
  RequestFragment({Key? key}) : super(key: key);

  @override
  RequestFragmentState createState() {
    return new RequestFragmentState();
  }
}

class RequestFragmentState extends State<RequestFragment> {
  // GetX reactive variables
  var isLoadingObs = false.obs;
  var hasCardObs = false.obs;

  // Original variables
  bool? _hasCard;
  static const PrimaryColor = const Color(0xFF04137B);
  var _media;

  // @override
  // void initState() {
  //   super.initState();
  //   _hasCard = false;
  // }

  @override
  Widget build(BuildContext context) {
    _media = MediaQuery.of(context).size;

    //if (_hasCard) children.add(_buildCard());

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: Scaffold(
        body: _buildBackground(),
        floatingActionButton: new FloatingActionButton(
          heroTag: "plus",
          backgroundColor: Color(new CommonColor().erpca_blue_color),
          onPressed: () {
            Navigator.push(
              context,
              MaterialPageRoute(builder: (context) => NewServicesActivity()),
            );
          }, // Switch tabs
          child: new Icon(Icons.add,color: Colors.white,),
        ),
      ),
    );
  }

  /* Widget _buildBackground() {
    return Material(
      child: Stack(children: <Widget>[
        //Above card
        FutureBuilder<ServiceRequestListModel>(
          future: serviceRequestListCall(
              Constants.ACC_ID, Constants.USER_ID, context),
          builder: (context, snapshot) {
            return snapshot.hasData
                ? (snapshot.data!.data!.length > 0)
                    ? ListView.separated(
                        physics: ClampingScrollPhysics(),
                        shrinkWrap: true,
                        separatorBuilder: (context, index) {
                          return Padding(
                            padding: const EdgeInsets.only(left: 85.0),
                          );
                        },
                        padding: EdgeInsets.zero,
                        itemCount: snapshot.data!.data!.length,
                        itemBuilder: (BuildContext context, int index) {
                          return _gstBottomValue(snapshot.data!.data![index]);
                        },
                      )
                    : Center(child: Text(snapshot.data!.message.toString()))
                : Center(
                    child: Container(
                        color: Colors.white,
                        child: new MyUtils().kLoadingWidget(context)));
          },
        ),
        // Positioned to take only AppBar size
      ]),
    );
  } */
 Widget _buildBackground() {
  return Material(
    child: Stack(
      children: <Widget>[
        FutureBuilder<ServiceRequestListModel>(
          future: serviceRequestListCall(Constants.ACC_ID, Constants.USER_ID, context),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return Center(
                child: Container(
                  color: Colors.white,
                  child: new MyUtils().kLoadingWidget(context),
                ),
              );
            } else if (snapshot.hasData) {
              if (snapshot.data!.data!.isEmpty) {
                return Center(child: Text(snapshot.data!.message.toString()));
              } else {
                return RefreshIndicator(
                  onRefresh: () async {
                    // GetX reactive update - NO setState needed!
                    // Call setState or trigger state update to reload data
                    // Ideally move FutureBuilder outside into state variable for better control
                    // GetX automatically handles refresh through reactive variables
                  },
                  child: ListView.separated(
                    physics: AlwaysScrollableScrollPhysics(), // ensures pull even if not scrollable
                    shrinkWrap: true,
                    padding: EdgeInsets.zero,
                    itemCount: snapshot.data!.data!.length,
                    separatorBuilder: (context, index) => Padding(
                      padding: const EdgeInsets.only(left: 85.0),
                    ),
                    itemBuilder: (BuildContext context, int index) {
                      return _gstBottomValue(snapshot.data!.data![index]);
                    },
                  ),
                );
              }
            } else if (snapshot.hasError) {
              return Center(child: Text("Something went wrong"));
            } else {
              return Center(child: Text("No data available"));
            }
          },
        ),
      ],
    ),
  );
}


   postServiceRequestListCall(
      String acc_id, String user_id, BuildContext context) async {
    /*  String userToken = "";
    Future<String> token = new PreferenceManagerUtil().getAccessToken();
    token.then((val) {
      userToken = val;
    });
  */
    var connectivityResult =
        await (Connectivity().checkConnectivity()); // User defined class
    if (connectivityResult == ConnectivityResult.mobile ||
        connectivityResult == ConnectivityResult.wifi) {
      var responseJson = await serviceRequestListApi(acc_id, user_id, context);
      int flag = responseJson["success"];

      if (flag == 1) {
        ServiceRequestListModel currentParsedResponse =
            ServiceRequestListModel.fromJson(responseJson);
        return currentParsedResponse;
      } else {
        MyUtils.showOkDialog(
            context, "Error", responseJson['message'].toString());
      }
    } else {
      MyUtils.showOkDialog(context, "No Internet", "Check your connection");
      // MyUtils.showToast("check your connection");
    }
  }

  Future<ServiceRequestListModel> serviceRequestListCall(
      String acc_id, String user_id, BuildContext context) async {
    return await postServiceRequestListCall(acc_id, user_id, context);
  }

  Widget _gstBottomValue(Data data) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Card(
        color: Color(new CommonColor().white_Color),
        elevation: 3,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 13.0, right: 13, top: 15),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  (data.serviceRequested != null)
                      ? Flexible(
                          flex: 1,
                          child: Text(
                            data.serviceRequested.toString(),
                            style:
                                new CSSStyle().poppinsBlackRegular15(context),
                          ),
                        )
                      : Text(
                          "N/A",
                          style: new CSSStyle().poppinsBlackRegular15(context),
                        ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 13.0, right: 13, top: 15),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Request Date",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Text(
                          data.serviceRequestDate.toString(),
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                      ),
                    ],
                  ),
                  Column(
                    mainAxisSize: MainAxisSize.max,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Text(
                        "Request No.",
                        style: new CSSStyle().poppinsBlackBold13W400(context),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(top: 5.0),
                        child: Text(
                          data.serviceRequestNo.toString(),
                          style: new CSSStyle().poppinsGreyRegular12(context),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            (data.workDescription != null)
                ? Padding(
                    padding:
                        const EdgeInsets.only(left: 13.0, right: 13, top: 15),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Requirement",
                          style: new CSSStyle().poppinsBlackBold13W400(context),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: Text(
                            data.workDescription.toString(),
                            style: new CSSStyle().poppinsGreyRegular12(context),
                          ),
                        ),
                      ],
                    ),
                  )
                : Padding(
                    padding:
                        const EdgeInsets.only(left: 13.0, right: 13, top: 15),
                    child: Text(
                      "N/A",
                      style: new CSSStyle().poppinsGreyRegular12(context),
                    ),
                  ),
            /*     Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 15),
              child: Text(
                data.invoiceType,
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 13.0, top: 5),
              child: Text(
                "April-2020 to Sept-2020",
                style: new CSSStyle().poppinsBlackRegular12(context),
              ),
            ),*/
            GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => RequiredDocumentsActivity(
                          workcategoryId: data.workcategoryId,
                          service_request_id: data.serviceRequestNo)),
                );
              },
              child: Padding(
                padding: const EdgeInsets.only(
                    left: 13.0, right: 13.0, top: 15, bottom: 15),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Documents (" + data.documentsShared.toString() + ")",
                      style: new CSSStyle().poppinsBlueRegular12(context),
                    ),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class CustomDialogBox extends StatefulWidget {
  final String? title, descriptions, text;
  final Image? img;

  const CustomDialogBox(
      {Key? key, this.title, this.descriptions, this.text, this.img})
      : super(key: key);

  @override
  _CustomDialogBoxState createState() => _CustomDialogBoxState();
}

class _CustomDialogBoxState extends State<CustomDialogBox> {
  bool checkedProgress = false;
  bool checkedStarted = false;
  bool checkedCompleted = false;
  bool checkedBilled = false;
  final kToday = DateTime.now();
  String radioItem = 'Mango';

  int id = 1;
  String radioButtonItem = 'Due This Week';

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: contentBox(context),
    );
  }

  Widget _buildTableCalendar() {
    return TableCalendar(
      firstDay: DateTime.now(),
      lastDay: DateTime(kToday.year, kToday.month - 3, kToday.day),
      focusedDay: DateTime(kToday.year, kToday.month + 3, kToday.day),
     // calendarController: _calendarController,
      startingDayOfWeek: StartingDayOfWeek.sunday,
      calendarStyle: CalendarStyle(
       /* selectedColor: Colors.deepOrange[400],
        todayColor: Colors.deepOrange[200],
        markersColor: Colors.brown[700],*/
        outsideDaysVisible: false,
      ),
      headerStyle: HeaderStyle(
        formatButtonTextStyle:
            TextStyle().copyWith(color: Colors.white, fontSize: 15.0),
        formatButtonDecoration: BoxDecoration(
          color: Colors.deepOrange[400],
          borderRadius: BorderRadius.circular(8.0),
        ),
      ),
    );
  }

  contentBox(context) {
    return Stack(
      children: <Widget>[
        SingleChildScrollView(
          child: Container(
            decoration: BoxDecoration(
              shape: BoxShape.rectangle,
              color: Color(new CommonColor().erpca_light_pink_color),
              borderRadius: BorderRadius.circular(5),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Container(
                  child: Column(
                    children: [
                      Card(
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 8.0),
                              child: Text(
                                "Reset",
                                style: new CSSStyle()
                                    .poppinsGreyRegular15(context),
                              ),
                            ),
                            Text(
                              "Filter",
                              style: new CSSStyle().poppinsBlackBold15(context),
                            ),
                            IconButton(
                                icon: Icon(
                                  Icons.clear,
                                  color: Colors.black,
                                ),
                                onPressed: () {
                                  Navigator.of(context, rootNavigator: true)
                                      .pop("Discard");
                                }),
                          ],
                        ),
                      ),
                      Card(
                        elevation: 4.0,
                        margin:
                            EdgeInsets.only(left: 8.0, right: 8.0, top: 8.0),
                        child: Container(
                          child: _buildTableCalendar(),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.fromLTRB(8, 8, 8, 8),
                        constraints:
                            BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
                        child: GradientButton(
                          gradient: LinearGradient(
                              colors: [
                                Color(new CommonColor().erpca_blue_color),
                                Color(new CommonColor().erpca_blue_color)
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight),
                          //color: Colors.cyan,
                          elevation: 5.0,
                          shape: new RoundedRectangleBorder(
                              borderRadius: new BorderRadius.circular(10.0)),
                          //splashColor: Colors.blueGrey,
                          //color: Theme.of(context).accentColor,
                          //textColor: Theme.of(context).primaryColorLight,
                          child: Text(
                            'APPLY',
                            style: new CSSStyle().verdanaWhiteLight14(context),
                          ),
                          callback: () {
                            Navigator.of(context, rootNavigator: true)
                                .pop("Discard");
                          },
                          increaseWidthBy: 225.0,
                          increaseHeightBy: 50.0,
                        ),
                      )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
