// Dashboard Widgets
// Specialized widgets for dashboard screens

import 'package:flutter/material.dart';
import 'package:get/get.dart';

// Dashboard Stats Card
class DashboardStatsCard extends StatelessWidget {
  final String title;
  final RxInt count;
  final IconData icon;
  final Color? color;
  final VoidCallback? onTap;

  const DashboardStatsCard({
    Key? key,
    required this.title,
    required this.count,
    required this.icon,
    this.color,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 32,
                color: color ?? Theme.of(context).primaryColor,
              ),
              SizedBox(height: 8),
              Obx(() => Text(
                count.value.toString(),
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                  color: color ?? Theme.of(context).primaryColor,
                ),
              )),
              SizedBox(height: 4),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Quick Action Button
class QuickActionButton extends StatelessWidget {
  final String title;
  final IconData icon;
  final Color? color;
  final VoidCallback onTap;

  const QuickActionButton({
    Key? key,
    required this.title,
    required this.icon,
    required this.onTap,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: 28,
                color: color ?? Theme.of(context).primaryColor,
              ),
              SizedBox(height: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Recent Activity Item
class RecentActivityItem extends StatelessWidget {
  final Map<String, dynamic> activity;

  const RecentActivityItem({
    Key? key,
    required this.activity,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: _getActivityColor(activity['type']),
        child: Icon(
          _getActivityIcon(activity['type']),
          color: Colors.white,
          size: 20,
        ),
      ),
      title: Text(
        activity['title'] ?? '',
        style: TextStyle(fontWeight: FontWeight.w500),
      ),
      subtitle: Text(activity['description'] ?? ''),
      trailing: Text(
        _formatTime(activity['timestamp']),
        style: TextStyle(
          fontSize: 12,
          color: Colors.grey[600],
        ),
      ),
    );
  }

  Color _getActivityColor(String? type) {
    switch (type) {
      case 'task':
        return Colors.blue;
      case 'document':
        return Colors.green;
      case 'invoice':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getActivityIcon(String? type) {
    switch (type) {
      case 'task':
        return Icons.task_alt;
      case 'document':
        return Icons.description;
      case 'invoice':
        return Icons.receipt;
      default:
        return Icons.info;
    }
  }

  String _formatTime(String? timestamp) {
    if (timestamp == null) return '';
    try {
      final dateTime = DateTime.parse(timestamp);
      final now = DateTime.now();
      final difference = now.difference(dateTime);
      
      if (difference.inDays > 0) {
        return '${difference.inDays}d ago';
      } else if (difference.inHours > 0) {
        return '${difference.inHours}h ago';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes}m ago';
      } else {
        return 'Just now';
      }
    } catch (e) {
      return '';
    }
  }
}

// Dashboard Header
class DashboardHeader extends StatelessWidget {
  final RxString userName;
  final RxString greeting;
  final RxInt notificationCount;
  final VoidCallback? onNotificationTap;

  const DashboardHeader({
    Key? key,
    required this.userName,
    required this.greeting,
    required this.notificationCount,
    this.onNotificationTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Obx(() => Text(
                    greeting.value,
                    style: TextStyle(
                      color: Colors.white70,
                      fontSize: 16,
                    ),
                  )),
                  SizedBox(height: 4),
                  Obx(() => Text(
                    userName.value,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  )),
                ],
              ),
            ),
            Stack(
              children: [
                IconButton(
                  onPressed: onNotificationTap,
                  icon: Icon(
                    Icons.notifications_outlined,
                    color: Colors.white,
                    size: 28,
                  ),
                ),
                Obx(() => notificationCount.value > 0
                    ? Positioned(
                        right: 8,
                        top: 8,
                        child: Container(
                          padding: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.red,
                            shape: BoxShape.circle,
                          ),
                          constraints: BoxConstraints(
                            minWidth: 16,
                            minHeight: 16,
                          ),
                          child: Text(
                            notificationCount.value > 99 ? '99+' : notificationCount.value.toString(),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                    : SizedBox.shrink()),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
