// Initial Binding
// Manages global dependency injection for the entire app

import 'package:get/get.dart';
import '../controllers/splash_controller.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';
import '../services/connectivity_service.dart';
import '../services/navigation_service.dart';

class InitialBinding extends Bindings {
  @override
  void dependencies() {
    // Core Services - Initialize with safe dependency handling

    // 1. Initialize StorageService first
    Get.putAsync<StorageService>(() async {
      final service = StorageService();
      await service.onInit(); // Ensure SharedPreferences is initialized
      return service;
    }, permanent: true);

    // 2. Initialize ApiService - it will handle StorageService dependency safely
    Get.put<ApiService>(ApiService(), permanent: true);

    // 3. Initialize other services
    Get.put<ConnectivityService>(ConnectivityService(), permanent: true);
    Get.put<NavigationService>(NavigationService(), permanent: true);

    // Splash Controller
    Get.lazyPut<SplashController>(() => SplashController());
  }
}
