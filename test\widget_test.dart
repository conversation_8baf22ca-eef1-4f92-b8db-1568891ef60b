// ERPC Customer App Widget Tests
//
// Basic tests for the ERPC Customer App with GetX integration

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';

import 'package:erpcacustomer/activity/NewServicesActivity.dart';
import 'package:erpcacustomer/activity/SplashActivity.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/routes/app_pages.dart';

void main() {
  group('ERPC Customer App Tests', () {
    setUp(() {
      // Initialize GetX for testing
      Get.testMode = true;
    });

    tearDown(() {
      // Clean up GetX instances after each test
      Get.reset();
    });

    testWidgets('App starts with splash screen', (WidgetTester tester) async {
      // Build the app with routing
      await tester.pumpWidget(
        GetMaterialApp(
          initialRoute: AppPages.INITIAL,
          getPages: AppPages.routes,
        ),
      );

      // Verify the app starts with splash screen
      expect(find.byType(SplashActivity), findsOneWidget);
    });

    testWidgets('NewServicesActivity builds without errors', (WidgetTester tester) async {
      // Build the app
      await tester.pumpWidget(
        GetMaterialApp(
          home: NewServicesActivity(),
        ),
      );

      // Verify the app builds successfully
      expect(find.byType(NewServicesActivity), findsOneWidget);
    });

    test('GetX reactive variables work correctly', () {
      // Test basic GetX reactive functionality
      final testValue = 'initial'.obs;

      expect(testValue.value, equals('initial'));

      testValue.value = 'updated';
      expect(testValue.value, equals('updated'));
    });

    testWidgets('App theme and colors are applied correctly', (WidgetTester tester) async {
      await tester.pumpWidget(
        GetMaterialApp(
          theme: ThemeData(
            primaryColor: Color(CommonColor().lightBlue),
          ),
          home: Scaffold(
            appBar: AppBar(title: Text('Test App')),
            body: Text('Test Content'),
          ),
        ),
      );

      // Verify app builds without errors
      expect(find.text('Test App'), findsOneWidget);
      expect(find.text('Test Content'), findsOneWidget);
    });
  });
}
