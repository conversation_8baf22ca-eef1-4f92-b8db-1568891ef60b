class ValidateWithWhatsAppModel {
  String? _token;
  String? _timestamp;
  String? _timezone;
  Mobile? _mobile;
  Email? _email;

  ValidateWithWhatsAppModel(
      {String? token,
        String? timestamp,
        String? timezone,
        Mobile? mobile,
        Email? email}) {
    if (token != null) {
      this._token = token;
    }
    if (timestamp != null) {
      this._timestamp = timestamp;
    }
    if (timezone != null) {
      this._timezone = timezone;
    }
    if (mobile != null) {
      this._mobile = mobile;
    }
    if (email != null) {
      this._email = email;
    }
  }

  String? get token => _token;
  set token(String? token) => _token = token;
  String? get timestamp => _timestamp;
  set timestamp(String? timestamp) => _timestamp = timestamp;
  String? get timezone => _timezone;
  set timezone(String? timezone) => _timezone = timezone;
  Mobile? get mobile => _mobile;
  set mobile(Mobile? mobile) => _mobile = mobile;
  Email? get email => _email;
  set email(Email? email) => _email = email;

  ValidateWithWhatsAppModel.fromJson(Map<String, dynamic> json) {
    _token = json['token'];
    _timestamp = json['timestamp'];
    _timezone = json['timezone'];
    _mobile =
    json['mobile'] != null ? new Mobile.fromJson(json['mobile']) : null;
    _email = json['email'] != null ? new Email.fromJson(json['email']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['token'] = this._token;
    data['timestamp'] = this._timestamp;
    data['timezone'] = this._timezone;
    if (this._mobile != null) {
      data['mobile'] = this._mobile!.toJson();
    }
    if (this._email != null) {
      data['email'] = this._email!.toJson();
    }
    return data;
  }
}

class Mobile {
  String? _name;
  String? _number;

  Mobile({String? name, String? number}) {
    if (name != null) {
      this._name = name;
    }
    if (number != null) {
      this._number = number;
    }
  }

  String? get name => _name;
  set name(String? name) => _name = name;
  String? get number => _number;
  set number(String? number) => _number = number;

  Mobile.fromJson(Map<String, dynamic> json) {
    _name = json['name'];
    _number = json['number'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this._name;
    data['number'] = this._number;
    return data;
  }
}

class Email {
  String? _name;
  String? _email;

  Email({String? name, String? email}) {
    if (name != null) {
      this._name = name;
    }
    if (email != null) {
      this._email = email;
    }
  }

  String? get name => _name;
  set name(String? name) => _name = name;
  String? get email => _email;
  set email(String? email) => _email = email;

  Email.fromJson(Map<String, dynamic> json) {
    _name = json['name'];
    _email = json['email'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['name'] = this._name;
    data['email'] = this._email;
    return data;
  }
}