import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:erpcacustomer/common/CSSStyle.dart';
import 'package:erpcacustomer/common/CommonColor.dart';
import 'package:erpcacustomer/common/CommonText.dart';
import 'package:erpcacustomer/controller/LoginUserNamePasswordController.dart';
import 'package:erpcacustomer/model/customer_login_pass_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gradient_widgets/gradient_widgets.dart';
import 'package:modal_progress_hud_nsn/modal_progress_hud_nsn.dart';
import 'package:get/get.dart';
import '../routes/app_routes.dart';


import 'SetMPinActivity.dart';

class LoginActivity extends StatefulWidget {
  LoginActivity({Key? key}) : super(key: key);

  @override
  LoginActivityState createState() {
    return new LoginActivityState();
  }
}

class LoginActivityState extends State<LoginActivity> {
  static const PrimaryColor = const Color(0xFF04137B);

  // GetX reactive variables
  var isLoading = false.obs;
  var userName = ''.obs;
  var password = ''.obs;
  var obscureText = true.obs;
  var checkedValue = false.obs;

  // Original variables
  var _media;
  bool _saving = false;
  String myToken = '';
  String userNameString = "";
  String passwordString = "";
  bool _obscureText = true;
  bool selected = true;

  // ✅ FIXED: SHA1 encryption method (confirmed server requirement)
  String generatePasswordHash(String dataIn) {
    var digest = sha1.convert(utf8.encode(dataIn));
    String hashedValue = digest.toString();
    print("🔐 SHA1 Encryption:");
    print("🔐 Input: '$dataIn'");
    print("🔐 SHA1 Output: '$hashedValue'");
    return hashedValue;
  }

  @override
  void initState() {
    super.initState();
  }

  var _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {


    _media = MediaQuery.of(context).size;

    return MaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: PrimaryColor,
        fontFamily: 'GlacialIndifference',
      ),
      home: Stack(
        children: [_buildBackground()],
      ),
    );
  }

  Widget _buildBackground() {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      body: Obx(() => ModalProgressHUD(
        inAsyncCall: isLoading.value,
        child: Container(
          height: double.infinity,
          child: SafeArea(
            child: Form(
              key: _formKey,
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12.0),
                    image: DecorationImage(
                        image: AssetImage('assets/images/login_screen.png'),
                        fit: BoxFit.fitHeight)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Padding(
                          padding:
                              EdgeInsets.only(top: 15, left: 15, right: 15),
                          child: Center(
                            child: Container(
                              height: 300,
                              child: Card(
                                elevation: 5,
                                color: Color(new CommonColor().white_Color),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: <Widget>[
                                    Container(
                                      margin: EdgeInsets.only(left: 15),
                                      child: Text(
                                        "Login",
                                        style: new CSSStyle()
                                            .poppinsBlackBold30(context),
                                      ),
                                    ),
                                    Container(
                                      margin: EdgeInsets.only(
                                          left: 15, right: 15, top: 15),
                                      child: TextFormField(
                                        validator: _validateUserName,
                                        decoration: InputDecoration(
                                          labelText: "Username",
                                          //  labelText: 'Email id',

                                          counterText: "",
                                          labelStyle: new CSSStyle()
                                              .poppinsGreyRegular15(context),
                                          errorStyle: TextStyle(
                                              color: Colors.red, fontSize: 15),
                                          suffixIcon: Padding(
                                            padding: EdgeInsets.all(0.0),
                                            child: IconButton(
                                              onPressed: _toggle,
                                              icon: Icon(
                                                Icons.check_circle,
                                                color: Color(new CommonColor()
                                                    .erpca_third_color),
                                              ),
                                              iconSize: 25,
                                              color: Colors.black54,
                                            ),
                                          ),
                                          //hintText: '',
                                        ),
                                      ),
                                    ),
                                    _addPasswordUi(),
                                    _addForgotPasswordUi(),
                                    //   _addAccessMyAccountButtonUi(),
                                    /*   Container(
                                    margin: EdgeInsets.only(
                                        left: 15.0, right: 15.0, top: 25, bottom: 0.0),
                                    child: TextFormField(
                                      validator: _validatePassword,
                                      keyboardType: TextInputType.visiblePassword,
                                      decoration: InputDecoration(
                                        labelText: 'Password',
                                        hintText: 'Minimum 6 character',
                                        //  labelText: 'Email id',

                                        counterText: "",
                                        labelStyle:
                                            new CSSStyle().poppinsGreyRegular15(context),
                                        errorStyle:
                                            TextStyle(color: Colors.red, fontSize: 15),

                                        //hintText: '',
                                      ),
                                    ),
                                  ),*/
                                    /*Row(
                                    children: <Widget>[
                                      Checkbox(
                                        value: checkedValue,
                                        onChanged: (bool newValue) {
                                          // ✅ OPTIMIZED: GetX reactive state management
                                          checkedValue.value = newValue;
                                          // setState(() { checkedValue = newValue; }); // Removed
                                        },
                                      ),
                                      */ /*  Text(
                                        "Remember me and keep my session started",
                                        style: TextStyle(
                                            inherit: true,
                                            fontSize: 14.0,
                                            color: Colors.black),
                                        textAlign: TextAlign.left,
                                      ),*/ /*
                                    ],
                                  ),
                                  Container(
                                    margin: EdgeInsets.only(
                                        left: 15.0, right: 15.0, top: 8, bottom: 15.0),
                                    child: Text(
                                      "By login you accept our Conditions, User policy and Privacy policy",
                                      style: TextStyle(
                                          inherit: true,
                                          fontSize: 16.0,
                                          color: Colors.black),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),*/
/*
                                  Padding(
                                    padding: const EdgeInsets.only(top: 12.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                      children: <Widget>[
                                        GestureDetector(
                                            onTap: () {},
                                            child: new Text(
                                              'OTP Login',
                                              style: new TextStyle(
                                                  color: Color(0xFF84A2AF), fontSize: 18),
                                            )),
                                        GestureDetector(
                                          child: new Text('Forgot Password?',
                                              style: new TextStyle(
                                                  color: Color(0xFF2E3233),
                                                  fontSize: 18)),
                                          onTap: () {},
                                        ),
                                      ],
                                    ),
                                  ),
*/
                                    /*     Padding(
                                    padding: EdgeInsets.only(top: 24, bottom: 20),
                                    child: Row(
                                      children: <Widget>[
                                        Expanded(
                                          child: Divider(
                                            color: Colors.black,
                                          ),
                                        ),
                                        Text("   OR   ",
                                            style: new TextStyle(
                                                color: Color(0xFF2E3233), fontSize: 22.0)),
                                        Expanded(
                                          child: Divider(
                                            color: Colors.black,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.all(8),
                                    padding: EdgeInsets.all(10),
                                    decoration: new BoxDecoration(
                                      borderRadius: BorderRadius.circular(4.0),
                                      color: Color(0xFF4862A3),
                                    ),
                                    child: Row(
                                      children: <Widget>[
                                        Image(
                                          fit: BoxFit.fill,
                                          image: AssetImage('assets/images/ic_facebook.png'),
                                          width: 20.0,
                                          height: 20.0,
                                        ),
                                        Text(' |  Login with Facebook',
                                            style: new TextStyle(
                                                color: Colors.white, fontSize: 20)),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    margin: EdgeInsets.all(8),
                                    decoration: new BoxDecoration(
                                      borderRadius: BorderRadius.circular(4.0),
                                      color: Color(0xFFDD4A39),
                                    ),
                                    padding: EdgeInsets.all(10),
                                    child: Row(children: <Widget>[
                                      Image(
                                        fit: BoxFit.fill,
                                        image: AssetImage('assets/images/ic_gplus.png'),
                                        width: 20.0,
                                        height: 20.0,
                                      ),
                                      Text(' |  Sign in with Google+',
                                          style: new TextStyle(
                                              color: Colors.white, fontSize: 20)),
                                    ]),
                                  ),
*/
                                    /*      Align(
                                      alignment: FractionalOffset.bottomCenter,
                                      child: Padding(
                                        padding: EdgeInsets.only(bottom: 4.0, top: 30),
                                        child: Text(
                                          "Powered by\nTrackfour Infotech Pvt Ltd",
                                          style: new TextStyle(
                                              color: Color(0xFF2E3233), fontSize: 18.0),
                                          textAlign: TextAlign.center,
                                        ),
                                      ))
*/
                                    // Padding(
                                    //   padding: EdgeInsets.all(4),
                                    //   child: Text("Trackfour Infotech Pvt Ltd",
                                    //       style: new TextStyle(
                                    //           color: Color(0xFF2E3233), fontSize: 18.0)),
                                    // ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ),
                        Row(
                          children: [
                            _addRememberMeUi(),
                            _addAccessMyAccountButtonUi()
                          ],
                        ),
                        _addOrUi(),
                        _addLoginWithOtpUi(),
                        _addRegistrationUi(),
                        _brandingUi(),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      )),
    );
  }

  _addOrUi() {
    return Container(
      margin: const EdgeInsets.only(top: 15.0, left: 00, right: 20),
      child: Row(children: <Widget>[
        Expanded(
          child: new Container(
              margin: const EdgeInsets.only(left: 20.0, right: 10.0),
              child: Divider(
                color: Colors.grey,
                height: 30,
              )),
        ),
        Text(
          "OR",
          style: new CSSStyle().poppinsGreyRegular15(context),
        ),
        Expanded(
          child: new Container(
              margin: const EdgeInsets.only(left: 10.0, right: 0.0),
              child: Divider(
                color: Colors.grey,
                height: 30,
              )),
        ),
      ]),
    );
  }

  void _showAlertDialog(String title, String message) {
    AlertDialog alertDialog = AlertDialog(
      title: Text(title),
      content: Text(message),
    );
    showDialog(context: context, builder: (_) => alertDialog);
  }

  void _signInApiCall() async {
    /*await databaseHelper.deleteAllUser();
    var noteMapList = await databaseHelper.getUserMapList();
    if (noteMapList.length <= 0) {
      int result = await databaseHelper.insertUser(userModel);
      if (result != 0) {
        handleTimeout();
      } else {
        MyUtils.showOkDialog(context, "Error", responseJson['emsg'].toString());
      }
    }*/

    //new PreferenceManagerUtil().setIsLogin(true);
  }

  // Implement proper login functionality
  Future<void> verifyLogin(String userName, String password, BuildContext context) async {
    if (userName.isEmpty) {
      _showAlertDialog("ERROR", "Enter Username");
    } else if (password.isEmpty) {
      _showAlertDialog("ERROR", "Enter Password");
    } else {
      // ✅ OPTIMIZED: GetX reactive state management
      isLoading.value = true;
      // setState(() { _saving = true; }); // Removed

      try {
        var connectivityResult = await Connectivity().checkConnectivity();
        if (connectivityResult == ConnectivityResult.mobile ||
            connectivityResult == ConnectivityResult.wifi) {

          // ✅ FIXED: Encrypt password using SHA1 before sending to API
          String encryptedPassword = generatePasswordHash(password);
          print("🔐 Username: $userName");
          print("🔐 Plain Password: $password");
          print("🔐 SHA1 Encrypted: $encryptedPassword");

          // Call the original login API with encrypted password
          CustomerLoginPassModel responseJson = await validateUserNameApi(
              userName, encryptedPassword, context);

          int status = responseJson.success!;
          print("Username Login Response: $responseJson");

          if (status == 1) {
            // ✅ OPTIMIZED: GetX navigation for better performance
            Get.to(() => SetMPinActivity());
            // Navigator.push(context, MaterialPageRoute(builder: (context) => SetMPinActivity()));
          } else {
            _showAlertDialog("Error", responseJson.message ?? "Login failed");
          }
        } else {
          _showAlertDialog("No Internet", "Check your connection");
        }
      } catch (e) {
        print("Login error: $e");
        _showAlertDialog("Error", "Login failed. Please try again.");
      } finally {
        // ✅ OPTIMIZED: GetX reactive state management
        isLoading.value = false;
        // setState(() { _saving = false; }); // Removed
      }
    }
  }

  String? _validatePassword(String? password) {
    if (password!.isEmpty) {
      return CommonText().password;
    } else if (password.length < 6) {
      return CommonText().valid_password;
    } else {
      this.password.value = password;
      this.passwordString = password;
    }
    return null;
  }

  String? _validateUserName(String? user_name) {
    if (user_name!.isEmpty) {
      return CommonText().user_name;
    } else {
      userName.value = user_name;
      userNameString = user_name;
    }
    return null;
  }

/*
  void verifyLogin(String userName, String password, BuildContext context) {
    if (userName.isEmpty) {
      _showAlertDialog("ERROR", "Enter Username");
    } else if (password.isEmpty) {
      _showAlertDialog("ERROR", "Enter Password");
    } else {
      // ✅ OPTIMIZED: GetX reactive state management
      isLoading.value = true;
      // setState(() { _saving = true; }); // Removed
      Future.delayed(Duration(seconds: 1), () async {
        //   bool isTrustFall = await TrustFall.isJailBroken;

        var connectivityResult =
            await (Connectivity().checkConnectivity()); // User defined class
        //if (!isTrustFall) {
        if (connectivityResult == ConnectivityResult.mobile ||
            connectivityResult == ConnectivityResult.wifi) {
          var responseJson = await callLoginApi(
              */
/*  userName,
              new MyUtils().generateSignatureExtraCharacter(password),*/ /*

              "4101200300000073-00",
              "156387eef71097d807fca7db18d3922bb6bd0d132bf522c4a88978241112fd518461775985",
              myToken,
              context);
          // ✅ OPTIMIZED: GetX reactive state management
          isLoading.value = false;
          // setState(() { _saving = false; }); // Removed
          String isError = responseJson['isError'].toString();

          if (isError == "1") {
            MyUtils.showOkDialog(
                context, "Error", responseJson['message'].toString());
          } else {
            LoginModel parsedResponse = LoginModel.fromJson(responseJson);
            new PreferenceManagerUtil()
                .setCorporateId(parsedResponse.result.corporateId);
            new PreferenceManagerUtil().setProposerDisplayName(
                parsedResponse.result.proposerDisplayName);
            new PreferenceManagerUtil()
                .setTokenId(parsedResponse.result.tokenID);
            new PreferenceManagerUtil()
                .setProposerPriPhone(parsedResponse.result.proposerPriPhone);
            new PreferenceManagerUtil()
                .setPolicyType(parsedResponse.result.policyType);
            new PreferenceManagerUtil()
                .setProposerPriMobile(parsedResponse.result.proposerPriMobile);
            new PreferenceManagerUtil()
                .setProposerPriEmail(parsedResponse.result.proposerPriEmail);
            new PreferenceManagerUtil()
                .setPolicyCode(parsedResponse.result.policyCode);
          */
/*  Navigator.pushReplacement(
              context,
              MaterialPageRoute(builder: (context) => DashBoardScreen()),
            );*/ /*

          }
        } else {
          // ✅ OPTIMIZED: GetX reactive state management
          isLoading.value = false;
          // setState(() { _saving = false; }); // Removed

          MyUtils.showOkDialog(context, "No Internet", "Check your connection");
          // MyUtils.showToast("check your connection");
        }

        */
/*      } else {
          // ✅ OPTIMIZED: GetX reactive state management
          isLoading.value = false;
          // setState(() { _saving = false; }); // Removed
          MyUtils.showOkDialog(
              context, "Sorry", "This Device is not secure");

        }*/ /*

      });
    }
  }
*/

  _addPasswordUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: Obx(() => TextFormField(
        keyboardType: TextInputType.text,
        //key: passKey,

        obscureText: obscureText.value,  // ✅ OPTIMIZED: GetX reactive variable
        inputFormatters: [

        ],
        validator: _validatePassword,
        //   controller: passwordController,
        style: new CSSStyle().ptSansBlackRegular15(context),
        //validator: _validatePassword,
        decoration: InputDecoration(
          labelText: 'Password',
          hintText: 'Minimum 6 character',
          labelStyle: new CSSStyle().poppinsGreyRegular15(context),
          errorStyle: TextStyle(color: Colors.red, fontSize: 15),
          suffixIcon: Padding(
            padding: EdgeInsets.all(0.0),
            child: IconButton(
              onPressed: _toggle,
              icon: (_obscureText == false)
                  ? Icon(Icons.remove_red_eye)
                  : Icon(Icons.visibility_off),
              iconSize: 25,
              color: Colors.black54,
            ),
          ),
        ),
        cursorColor: Colors.black,
      )),  // ✅ FIXED: Close Obx widget properly
    );
  }

  _addForgotPasswordUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: <Widget>[
              InkWell(
                child: Text(
                  "Forgot Password?",
                  style: new CSSStyle().poppinsblueTextRegular12(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _addLoginWithOtpUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 8),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              InkWell(
                child: Text(
                  "Login with OTP",
                  style: new CSSStyle().poppinsblueTextRegular14(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  _addRegistrationUi() {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15, top: 15),
      child: GestureDetector(
        onTap: () {
          /*  Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ForgotPasswordActivity(),
              ));*/
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 10, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Text(
                "New to ERPCA ? ",
                style: new CSSStyle().ptSansBlackRegular16(context),
              ),
              InkWell(
                child: Text(
                  "Activate account",
                  style: new CSSStyle().poppinsblueTextRegular14(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _brandingUi() {
    String assetNameMale = 'images/eoxegen_logo.svg';

    return Padding(
      padding: const EdgeInsets.only(top: 15.0, bottom: 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Column(
            children: [
              Text(
                "Copyright © 2020",
                style: new CSSStyle().poppinsGreyRegular10(context),
              ),
              Text(
                "Woodapple Software Solution Pvt. Ltd.",
                style: new CSSStyle().poppinsGreyRegular10(context),
              ),
            ],
          ),
        ],
      ),
    );
  }

  _addRememberMeUi() {
    return Expanded(
      flex: 1,
      child: Container(
        margin: EdgeInsets.fromLTRB(15, 0, 0, 0),
        child: Row(
          children: [
            Obx(() => Checkbox(
                value: checkedValue.value,  // ✅ OPTIMIZED: GetX reactive variable
                checkColor: Colors.white,
                activeColor: Colors.green,
                onChanged: (val) {
                  // ✅ OPTIMIZED: GetX reactive state management
                  checkedValue.value = !checkedValue.value;
                  // this.setState(() { this.selected = !this.selected; }); // Removed
                })),
            Text(
              "Remember me ",
              style: new CSSStyle().poppinsGreyRegular10(context),
            )
          ],
        ),
      ),
    );
  }

  _addAccessMyAccountButtonUi() {
    return Expanded(
      flex: 1,
      child: Container(
        margin: EdgeInsets.fromLTRB(0, 15, 15, 0),
        constraints: BoxConstraints(maxHeight: 50.0, minHeight: 50.0),
        child: GradientButton(
          gradient: LinearGradient(colors: [
            Color(new CommonColor().erpca_first_color),
            Color(new CommonColor().erpca_second_color)
          ], begin: Alignment.centerLeft, end: Alignment.centerRight),
          //color: Colors.cyan,
          elevation: 5.0,
          shape: new RoundedRectangleBorder(
              borderRadius: new BorderRadius.circular(10.0)),
          //splashColor: Colors.blueGrey,
          //color: Theme.of(context).accentColor,
          //textColor: Theme.of(context).primaryColorLight,
          child: Text(
            'LOGIN',
            style: new CSSStyle().verdanaWhiteLight14(context),
          ),
          callback: () {
            // ✅ OPTIMIZED: GetX reactive state management
            if (_formKey.currentState!.validate()) {
              isLoading.value = true;
              verifyLogin(userName.value, password.value, context);
            }
            // setState(() { ... }); // Removed - GetX handles reactive updates
          },
          increaseWidthBy: 225.0,
          increaseHeightBy: 50.0,
        ),
      ),
    );
  }

  void _toggle() {
    // ✅ OPTIMIZED: GetX reactive state management
    obscureText.value = !obscureText.value;
    // setState(() { _obscureText = !_obscureText; }); // Removed
  }
}
