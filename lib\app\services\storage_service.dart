// Storage Service
// Handles local data storage using SharedPreferences

import 'package:get/get.dart';
import 'package:shared_preferences/shared_preferences.dart';

class StorageService extends GetxService {
  late SharedPreferences _prefs;

  // Storage keys
  static const String _tokenKey = 'auth_token';
  static const String _userNameKey = 'user_name';
  static const String _contactPhoneKey = 'contact_phone';
  static const String _selectedCompanyKey = 'selected_company';
  static const String _mpinKey = 'mpin';
  static const String _isFirstTimeKey = 'is_first_time';
  static const String _appVersionKey = 'app_version';
  static const String _lastLoginKey = 'last_login';

  @override
  Future<void> onInit() async {
    super.onInit();
    await initialize();
  }

  // Initialize SharedPreferences
  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // Authentication related storage
  Future<void> saveToken(String token) async {
    await _prefs.setString(_tokenKey, token);
  }

  Future<String> getToken() async {
    return _prefs.getString(_tokenKey) ?? '';
  }

  Future<void> saveUserName(String userName) async {
    await _prefs.setString(_userNameKey, userName);
  }

  Future<String> getUserName() async {
    return _prefs.getString(_userNameKey) ?? '';
  }

  Future<void> saveContactPhone(String phone) async {
    await _prefs.setString(_contactPhoneKey, phone);
  }

  Future<String> getContactPhone() async {
    return _prefs.getString(_contactPhoneKey) ?? '';
  }

  Future<void> saveSelectedCompany(String company) async {
    await _prefs.setString(_selectedCompanyKey, company);
  }

  Future<String> getSelectedCompany() async {
    return _prefs.getString(_selectedCompanyKey) ?? '';
  }

  Future<void> saveMPin(String mpin) async {
    await _prefs.setString(_mpinKey, mpin);
  }

  Future<String> getMPin() async {
    return _prefs.getString(_mpinKey) ?? '';
  }

  // App state storage
  Future<void> setFirstTime(bool isFirstTime) async {
    await _prefs.setBool(_isFirstTimeKey, isFirstTime);
  }

  Future<bool> isFirstTime() async {
    return _prefs.getBool(_isFirstTimeKey) ?? true;
  }

  Future<void> saveAppVersion(String version) async {
    await _prefs.setString(_appVersionKey, version);
  }

  Future<String> getAppVersion() async {
    return _prefs.getString(_appVersionKey) ?? '';
  }

  Future<void> saveLastLogin(DateTime dateTime) async {
    await _prefs.setString(_lastLoginKey, dateTime.toIso8601String());
  }

  Future<DateTime?> getLastLogin() async {
    final dateString = _prefs.getString(_lastLoginKey);
    if (dateString != null) {
      return DateTime.tryParse(dateString);
    }
    return null;
  }

  // Generic storage methods
  Future<void> saveString(String key, String value) async {
    await _prefs.setString(key, value);
  }

  Future<String> getString(String key, {String defaultValue = ''}) async {
    return _prefs.getString(key) ?? defaultValue;
  }

  Future<void> saveInt(String key, int value) async {
    await _prefs.setInt(key, value);
  }

  Future<int> getInt(String key, {int defaultValue = 0}) async {
    return _prefs.getInt(key) ?? defaultValue;
  }

  Future<void> saveBool(String key, bool value) async {
    await _prefs.setBool(key, value);
  }

  Future<bool> getBool(String key, {bool defaultValue = false}) async {
    return _prefs.getBool(key) ?? defaultValue;
  }

  Future<void> saveDouble(String key, double value) async {
    await _prefs.setDouble(key, value);
  }

  Future<double> getDouble(String key, {double defaultValue = 0.0}) async {
    return _prefs.getDouble(key) ?? defaultValue;
  }

  Future<void> saveStringList(String key, List<String> value) async {
    await _prefs.setStringList(key, value);
  }

  Future<List<String>> getStringList(String key) async {
    return _prefs.getStringList(key) ?? [];
  }

  // Remove specific key
  Future<void> remove(String key) async {
    await _prefs.remove(key);
  }

  // Check if key exists
  bool containsKey(String key) {
    return _prefs.containsKey(key);
  }

  // Clear all stored data
  Future<void> clearAll() async {
    await _prefs.clear();
  }

  // Clear authentication data only
  Future<void> clearAuthData() async {
    await _prefs.remove(_tokenKey);
    await _prefs.remove(_userNameKey);
    await _prefs.remove(_contactPhoneKey);
    await _prefs.remove(_selectedCompanyKey);
    await _prefs.remove(_mpinKey);
    await _prefs.remove(_lastLoginKey);
  }

  // Get all keys
  Set<String> getAllKeys() {
    return _prefs.getKeys();
  }

  // Backup and restore functionality
  Future<Map<String, dynamic>> exportData() async {
    final Map<String, dynamic> data = {};
    for (String key in _prefs.getKeys()) {
      data[key] = _prefs.get(key);
    }
    return data;
  }

  Future<void> importData(Map<String, dynamic> data) async {
    for (String key in data.keys) {
      final value = data[key];
      if (value is String) {
        await _prefs.setString(key, value);
      } else if (value is int) {
        await _prefs.setInt(key, value);
      } else if (value is double) {
        await _prefs.setDouble(key, value);
      } else if (value is bool) {
        await _prefs.setBool(key, value);
      } else if (value is List<String>) {
        await _prefs.setStringList(key, value);
      }
    }
  }

  // Utility methods
  Future<bool> isLoggedIn() async {
    final token = await getToken();
    final mpin = await getMPin();
    return token.isNotEmpty && mpin.isNotEmpty;
  }

  Future<bool> hasMPin() async {
    final mpin = await getMPin();
    return mpin.isNotEmpty;
  }

  Future<bool> hasUserData() async {
    final userName = await getUserName();
    final phone = await getContactPhone();
    return userName.isNotEmpty && phone.isNotEmpty;
  }
}
