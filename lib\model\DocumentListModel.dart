class DocumentListModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  DocumentListModel(
      {int? status,
        String? message,
        int? success,
      List<Data>? data,
        String? errorDev}) {
    this._status = status;
    this._message = message;
    this._success = success;
    this._data = data;
    this._errorDev = errorDev;
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  DocumentListModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _docTime;
  String? _filePath;
  String? _uploadedOn;

  Data({String? docTime, String? filePath, String? uploadedOn}) {
    this._docTime = docTime;
    this._filePath = filePath;
    this._uploadedOn = uploadedOn;
  }

  String? get docTime => _docTime;
  set docTime(String? docTime) => _docTime = docTime;
  String? get filePath => _filePath;
  set filePath(String? filePath) => _filePath = filePath;
  String? get uploadedOn => _uploadedOn;
  set uploadedOn(String? uploadedOn) => _uploadedOn = uploadedOn;

  Data.fromJson(Map<String, dynamic> json) {
    _docTime = json['doc_time'];
    _filePath = json['file_path'];
    _uploadedOn = json['uploaded_on'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['doc_time'] = this._docTime;
    data['file_path'] = this._filePath;
    data['uploaded_on'] = this._uploadedOn;
    return data;
  }
}
