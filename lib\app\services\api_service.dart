// API Service
// Handles all HTTP requests and API communication

import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:get/get.dart';
import 'storage_service.dart';

class ApiService extends GetxService {
  static const String baseUrl = 'https://your-api-base-url.com/api';
  static const Duration timeoutDuration = Duration(seconds: 30);

  StorageService? _storageService;

  late http.Client _client;
  Map<String, String> _defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  };

  @override
  Future<void> onInit() async {
    super.onInit();
    _client = http.Client();
    await initialize();
  }

  @override
  void onClose() {
    _client.close();
    super.onClose();
  }

  // Initialize API service
  Future<void> initialize() async {
    try {
      // Try to get StorageService if it's available
      if (Get.isRegistered<StorageService>()) {
        _storageService = Get.find<StorageService>();
        await _updateAuthHeaders();
      } else {
        // Schedule a retry to connect to StorageService later
        _scheduleStorageServiceConnection();
      }
    } catch (e) {
      log('[API SERVICE] StorageService not available yet: $e');
      // Schedule a retry to connect to StorageService later
      _scheduleStorageServiceConnection();
    }
  }

  // Schedule a retry to connect to StorageService
  void _scheduleStorageServiceConnection() {
    Future.delayed(Duration(milliseconds: 500), () async {
      try {
        if (_storageService == null && Get.isRegistered<StorageService>()) {
          _storageService = Get.find<StorageService>();
          await _updateAuthHeaders();
          log('[API SERVICE] StorageService connected successfully');
        }
      } catch (e) {
        log('[API SERVICE] Still cannot connect to StorageService: $e');
      }
    });
  }

  // Update authentication headers
  Future<void> _updateAuthHeaders() async {
    try {
      if (_storageService != null) {
        final token = await _storageService!.getToken();
        if (token.isNotEmpty) {
          _defaultHeaders['Authorization'] = 'Bearer $token';
        }
      }
    } catch (e) {
      log('[API SERVICE] Error updating auth headers: $e');
    }
  }

  // Manually set storage service after it's initialized
  void setStorageService(StorageService storageService) {
    _storageService = storageService;
    log('[API SERVICE] StorageService set successfully');
  }

  // Check if storage service is available
  bool get hasStorageService => _storageService != null;

  // Force refresh auth headers (useful after login)
  Future<void> refreshAuthHeaders() async {
    try {
      if (_storageService == null && Get.isRegistered<StorageService>()) {
        _storageService = Get.find<StorageService>();
      }
      await _updateAuthHeaders();
      log('[API SERVICE] Auth headers refreshed');
    } catch (e) {
      log('[API SERVICE] Error refreshing auth headers: $e');
    }
  }

  // GET request
  Future<Map<String, dynamic>> get(String endpoint, {Map<String, String>? headers}) async {
    try {
      // Try to update auth headers if storage service is available
      try {
        await _updateAuthHeaders();
      } catch (e) {
        log('[API SERVICE] Could not update auth headers: $e');
      }

      final url = '$baseUrl$endpoint';
      final requestHeaders = {..._defaultHeaders, ...?headers};

      // ✅ API Request Logging
      log('[URL] : $url');

      final response = await _client
          .get(
            Uri.parse(url),
            headers: requestHeaders,
          )
          .timeout(timeoutDuration);

      // ✅ API Response Logging
      log('[RESPONSE STATUS] : ${response.statusCode}');
      log('[RESPONSE BODY] : ${response.body}');

      return _handleResponse(response);
    } catch (e) {
      // ✅ API Error Logging
      log('[ERROR] : $e');
      return _handleError(e);
    }
  }

  // POST request
  Future<Map<String, dynamic>> post(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    try {
      // Try to update auth headers if storage service is available
      try {
        await _updateAuthHeaders();
      } catch (e) {
        log('[API SERVICE] Could not update auth headers: $e');
      }

      final url = '$baseUrl$endpoint';
      final requestHeaders = {..._defaultHeaders, ...?headers};
      final requestBody = body != null ? jsonEncode(body) : null;

      // ✅ API Request Logging
      log('[URL] : $url\n[PARAMS] : ${body ?? {}}');

      final response = await _client
          .post(
            Uri.parse(url),
            headers: requestHeaders,
            body: requestBody,
          )
          .timeout(timeoutDuration);

      // ✅ API Response Logging
      log('[RESPONSE STATUS] : ${response.statusCode}');
      log('[RESPONSE BODY] : ${response.body}');

      return _handleResponse(response);
    } catch (e) {
      // ✅ API Error Logging
      log('[ERROR] : $e');
      return _handleError(e);
    }
  }

  // PUT request
  Future<Map<String, dynamic>> put(
    String endpoint, {
    Map<String, dynamic>? body,
    Map<String, String>? headers,
  }) async {
    try {
      await _updateAuthHeaders();

      final url = '$baseUrl$endpoint';
      final requestHeaders = {..._defaultHeaders, ...?headers};
      final requestBody = body != null ? jsonEncode(body) : null;

      // ✅ API Request Logging
      log('[URL] : $url\n[PARAMS] : ${body ?? {}}');

      final response = await _client
          .put(
            Uri.parse(url),
            headers: requestHeaders,
            body: requestBody,
          )
          .timeout(timeoutDuration);

      // ✅ API Response Logging
      log('[RESPONSE STATUS] : ${response.statusCode}');
      log('[RESPONSE BODY] : ${response.body}');

      return _handleResponse(response);
    } catch (e) {
      // ✅ API Error Logging
      log('[ERROR] : $e');
      return _handleError(e);
    }
  }

  // DELETE request
  Future<Map<String, dynamic>> delete(
    String endpoint, {
    Map<String, String>? headers,
  }) async {
    try {
      await _updateAuthHeaders();

      final url = '$baseUrl$endpoint';
      final requestHeaders = {..._defaultHeaders, ...?headers};

      // ✅ API Request Logging
      log('[URL] : $url');

      final response = await _client
          .delete(
            Uri.parse(url),
            headers: requestHeaders,
          )
          .timeout(timeoutDuration);

      // ✅ API Response Logging
      log('[RESPONSE STATUS] : ${response.statusCode}');
      log('[RESPONSE BODY] : ${response.body}');

      return _handleResponse(response);
    } catch (e) {
      // ✅ API Error Logging
      log('[ERROR] : $e');
      return _handleError(e);
    }
  }

  // Upload file
  Future<Map<String, dynamic>> uploadFile(
    String endpoint,
    File file, {
    Map<String, String>? fields,
    String fileField = 'file',
  }) async {
    try {
      await _updateAuthHeaders();
      
      final request = http.MultipartRequest('POST', Uri.parse('$baseUrl$endpoint'));
      
      // Add headers
      request.headers.addAll(_defaultHeaders);
      
      // Add fields
      if (fields != null) {
        request.fields.addAll(fields);
      }
      
      // Add file
      request.files.add(await http.MultipartFile.fromPath(fileField, file.path));
      
      final streamedResponse = await request.send().timeout(timeoutDuration);
      final response = await http.Response.fromStream(streamedResponse);
      
      return _handleResponse(response);
    } catch (e) {
      return _handleError(e);
    }
  }

  // Handle HTTP response
  Map<String, dynamic> _handleResponse(http.Response response) {
    try {
      final Map<String, dynamic> data = jsonDecode(response.body);
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        return {
          'success': true,
          'data': data,
          'statusCode': response.statusCode,
        };
      } else {
        return {
          'success': false,
          'message': data['message'] ?? 'Request failed',
          'statusCode': response.statusCode,
          'data': data,
        };
      }
    } catch (e) {
      return {
        'success': false,
        'message': 'Failed to parse response',
        'statusCode': response.statusCode,
      };
    }
  }

  // Handle errors
  Map<String, dynamic> _handleError(dynamic error) {
    String message = 'Network error occurred';
    
    if (error is SocketException) {
      message = 'No internet connection';
    } else if (error is HttpException) {
      message = 'HTTP error occurred';
    } else if (error is FormatException) {
      message = 'Invalid response format';
    }
    
    return {
      'success': false,
      'message': message,
      'error': error.toString(),
    };
  }

  // Set authentication token
  void setAuthToken(String token) {
    _defaultHeaders['Authorization'] = 'Bearer $token';
  }

  // Clear authentication token
  void clearAuthToken() {
    _defaultHeaders.remove('Authorization');
  }

  // Check if authenticated
  bool get isAuthenticated => _defaultHeaders.containsKey('Authorization');
}
