import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FABBottomAppBarItem {
  FABBottomAppBarItem({this.iconData, this.text});
  IconData? iconData;
  String? text;
}

class FABBottomAppBar extends StatefulWidget {
  FABBottomAppBar({
    this.items,
    this.centerItemText,
    this.height: 60.0,
    this.iconSize: 24.0,
    this.backgroundColor,
    this.color,
    this.selectedColor,
    this.notchedShape,
    this.onTabSelected,
  }) {
    assert(this.items!.length == 2 || this.items!.length == 5);
  }
  final List<FABBottomAppBarItem>? items;
  final String? centerItemText;
  final double? height;
  final double? iconSize;
  final Color? backgroundColor;
  final Color? color;
  final Color? selectedColor;
  final NotchedShape? notchedShape;
  final ValueChanged<int>? onTabSelected;

  @override
  State<StatefulWidget> createState() => FABBottomAppBarState();
}

class FABBottomAppBarState extends State<FABBottomAppBar> {
  // ✅ OPTIMIZED: GetX reactive variable for state management
  var selectedIndexObs = 0.obs;

  // Original variable (keeping for compatibility)
  int _selectedIndex = 0;

  _updateIndex(int index) {
    widget.onTabSelected!(index);
    // ✅ OPTIMIZED: GetX reactive state management
    _selectedIndex = index;
    selectedIndexObs.value = index;
    // setState(() { _selectedIndex = index; }); // Removed
  }

  @override
  Widget build(BuildContext context) {
    // ✅ OPTIMIZED: Obx wrapper for GetX reactive updates
    return Obx(() {
      List<Widget> items = List.generate(widget.items!.length, (int index) {
        return _buildTabItem(
          item: widget.items![index],
          index: index,
          onPressed: _updateIndex,
        );
      });
      items.insert(items.length >> 1, _buildMiddleTabItem());

      return BottomAppBar(
        shape: widget.notchedShape,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: items,
        ),
        color: widget.backgroundColor,
      );
    });
  }

  Widget _buildMiddleTabItem() {
    return Expanded(
      child: SizedBox(
        height: widget.height,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SizedBox(height: widget.iconSize),
            Text(
              widget.centerItemText ?? '',
              style: TextStyle(color: widget.color),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTabItem({
    FABBottomAppBarItem? item,
    int? index,
    ValueChanged<int>? onPressed,
  }) {
    Color? color = _selectedIndex == index ? widget.selectedColor : widget.color;
    return Expanded(
      child: SizedBox(
        height: widget.height,
        child: Material(
          type: MaterialType.transparency,
          child: InkWell(
            onTap: () => onPressed!(index!),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Icon(item!.iconData!, color: color, size: 17),
                Padding(
                  padding: const EdgeInsets.only(top: 3.0),
                  child: Text(
                    item.text.toString(),
                    style: TextStyle(color: color, fontSize: 12),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
