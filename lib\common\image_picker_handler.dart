import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';

import 'image_picker_dialog.dart';

class ImagePickerHandler {
  ImagePickerDialog? imagePicker;
  AnimationController? _controller;
  ImagePickerListener? _listener;

  ImagePickerHandler(this._listener, this._controller);

  openCamera() async {
    imagePicker!.dismissDialog();
    PickedFile? image =
        await ImagePicker.platform.pickImage(source: ImageSource.camera);
    cropImage(image!);
  }

  openGallery() async {
    imagePicker!.dismissDialog();
    PickedFile? image =
        await ImagePicker.platform.pickImage(source: ImageSource.gallery);
    cropImage(image!);
  }

  void init() {
    imagePicker = new ImagePickerDialog(this, _controller);
    imagePicker!.initState();
  }

  Future cropImage(PickedFile image) async {
    CroppedFile? croppedFile = await ImageCropper(). cropImage(
      sourcePath: image.path,
      maxWidth: 512,
      maxHeight: 512,
    );
    if (croppedFile != null) {
      _listener!.userImage(File(croppedFile.path));
    }
  }

  showDialog(BuildContext context) {
    imagePicker!.getImage(context);
  }
}

abstract class ImagePickerListener {
  userImage(File _image);
}
