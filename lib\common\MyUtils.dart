import 'dart:async';
import 'dart:convert';
import 'dart:core';
import 'dart:core';
import 'dart:core';
import 'dart:io';
import 'dart:math';

import 'package:crypto/crypto.dart';
import 'package:erpcacustomer/common/sizeconfig.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_downloader/flutter_downloader.dart';
import 'package:flutter_email_sender/flutter_email_sender.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:path_provider/path_provider.dart' as p;
import 'package:path_provider/path_provider.dart';
// import 'package:share_extend/share_extend.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:whatsapp_unilink/whatsapp_unilink.dart';
import 'package:share_plus/share_plus.dart';

import 'Constants.dart';
//import 'package:flutter_share/flutter_share.dart';

class MyUtils {
  final _random = new Random();
  String _localPath = '';



  /*static void showToast(String message) {
    Fluttertoast.showToast(
      backgroundColor: Colors.grey,
      msg: message,
      toastLength: Toast.LENGTH_LONG,
    );
  }*/
  Widget kLoadingWidget(context) {
    return Center(
      child: SpinKitFadingCircle(
        color: Colors.blueAccent,
        size: 40.0,
      ),
    );
  }
  Widget kLoadingWidgetLarge(context) {
    return Center(
      child: SpinKitFadingCircle(
        color: Colors.blueAccent,
        size: 60.0,
      ),
    );
  }

  Future<void> shareFile(String filepathData,String name) async {
    await SharePlus.instance.share(
      ShareParams(
        text: name,
        //uri:
      )
    );
  }

  String checkAndroidOrIos()  {
    String deviceType = "";
    if ((Platform.isAndroid)) {
      deviceType = "Android";
    } else {
      deviceType = "Ios";
    }
    return deviceType;
  }
  getDownloadUrl(BuildContext context, String urlPath, bool sendEmail) async {
    List<String> attachment = <String>[];
    final dir = await p.getTemporaryDirectory();
    String taskId;

    if (Platform.isAndroid) {
      _localPath = "/sdcard/download/erpcacustomer";
    } else {
      _localPath = (await getApplicationDocumentsDirectory()).path+"/erpcacustomer";
    }
    Directory savedDir = Directory(_localPath);
    bool hasExisted = await savedDir.exists();
    if (!hasExisted) {
      savedDir.create();
    }
    taskId = (await FlutterDownloader.enqueue(
      url: urlPath,
      savedDir: _localPath,
      saveInPublicStorage: true,
      showNotification:
      true, // show download progress in status bar (for Android)
      openFileFromNotification:
      true, // click on notification to open downloaded file (for Android)
    ).then((value) {
      if (sendEmail) {
        if (urlPath.split("/").last.contains("?")) {
          List<String> array_Data = urlPath.split("/").last.split("?");
          attachment.add(_localPath + "/" + array_Data[0]);
          //  new MyUtils().sendMailWithAttachment("", "", attachment, context);
          SharePlus.instance.share(/* attachment, text: '' */ ShareParams(
        text: "",
        //uri:
      ));
        } else {
          attachment.add(_localPath + "/" + urlPath.split("/").last);
          SharePlus.instance.share(ShareParams(text: ""));

          //  new MyUtils().sendMailWithAttachment("", "", attachment, context);
        }
      }
      return value;
    }).catchError((error) {
    }).onError((error, stackTrace) {
      return error.toString();
    }).whenComplete(() {
      if (sendEmail) {
        if (urlPath.split("/").last.contains("?")) {
          List<String> array_Data = urlPath.split("/").last.split("?");
          attachment.add(_localPath + "/" + array_Data[0]);
          //  new MyUtils().sendMailWithAttachment("", "", attachment, context);
          SharePlus.instance.share(ShareParams(
        text: "",
        //uri:
      ));
        } else {
          attachment.add(_localPath + "/" + urlPath.split("/").last);
          SharePlus.instance.share(ShareParams(
        text: "",
        //uri:
      ));

          //  new MyUtils().sendMailWithAttachment("", "", attachment, context);
        }
      }
    }))!;
  }

  getDownloadUrl1(String filePath) async {
    var url = filePath;
    if (await canLaunch(url)) {
      await launch(url,
      );
    } else {
      throw 'Could not launch $url';
    }
  }
  Future<bool> _openDownloadedFile(taskId) {
    if (taskId != null && taskId.status == DownloadTaskStatus.complete) {
      return FlutterDownloader.open(taskId: taskId);
    } else {
      return Future.value(false);
    }
  }

  launchURL(String url) async {
    if (await canLaunch(url)) {
      await launch(
        url,
      );
    } else {
      throw 'Unable to open url : $url';
    }
  }

  launchWhatsApp(String phone) async {
    final link = WhatsAppUnilink(
      phoneNumber: phone,
      text: "",
    );
    // Convert the WhatsAppUnilink instance to a string.
    // Use either Dart's string interpolation or the toString() method.
    // The "launch" method is part of "url_launcher".
    await launch('$link');
  }

  Future<void> sendMailWithAttachment(String subject, String body,
      List<String> attachment, BuildContext context) async {
    final Email email = Email(
      body: '',
      subject: '',
      recipients: [''],
      cc: [''],
      bcc: [''],
      attachmentPaths: attachment,
      isHTML: false,
    );

    String platformResponse;

    try {
      await FlutterEmailSender.send(email);
      platformResponse = 'success';
    } catch (error) {
      platformResponse = error.toString();
    }
  }

  Future<String> _findLocalPath(BuildContext context) async {
    final directory = Theme.of(context).platform == TargetPlatform.android
        ? await (getTemporaryDirectory() as FutureOr<Directory>)
        : await getApplicationDocumentsDirectory();
    return directory.path;
  }

  sendMail(
      String email, String subject, String body, BuildContext context) async {
    // Android and iOS

    final Email emailDatta = Email(
      body: '',
      subject: '',
      recipients: [''],
      cc: [email],
      bcc: [''],
      isHTML: false,
    );

    String platformResponse;

    try {
      await FlutterEmailSender.send(emailDatta);
      platformResponse = 'success';
    } catch (error) {
      platformResponse = error.toString();
    }
  }

  callMe(String phone, BuildContext context) async {
    // Android
    String uri = "tel:" + phone + "";
    if (await canLaunch(uri)) {
      await launch(uri);
    } else {
      // iOS
      String uri = "tel:" + phone + "";
      if (await canLaunch(uri)) {
        await launch(uri);
      } else {
        showOkDialog(context, "Sorry", 'Could not launch $phone');
        throw 'Could not launch $uri';
      }
    }
  }

  static bool isEmail(String em) {
    String p =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';

    RegExp regExp = new RegExp(p);

    return regExp.hasMatch(em);
  }

  String generateSignature(String dataIn) {
    var encodedKey = utf8.encode(Constants.SHA_KEY); // signature=encryption key
    var hmacSha256 = new Hmac(sha256, encodedKey); // HMAC-SHA256 with key
    var bytesDataIn = utf8.encode(dataIn); // encode the data to Unicode.
    var digest = hmacSha256.convert(bytesDataIn); // encrypt target data

    String singedValue = digest.toString();
    return singedValue;
  }

  String generateSignatureExtraCharacter(String dataIn) {
    var encodedKey = utf8.encode(Constants.SHA_KEY); // signature=encryption key
    var hmacSha256 = new Hmac(sha256, encodedKey); // HMAC-SHA256 with key
    var bytesDataIn = utf8.encode(dataIn); // encode the data to Unicode.
    var digest = hmacSha256.convert(bytesDataIn); // encrypt target data

    String singedValue = next(10000, 99999).toString() +
        digest.toString() +
        next(10000, 99999).toString();
    return singedValue;
  }

  int next(int min, int max) => min + _random.nextInt(max - min);

  static void showOkDialog(
    BuildContext context,
    String title,
    String content,
  ) {
    // flutter defined function
    showDialog(
      context: context,
      builder: (BuildContext context) {
        // return object of type Dialog
        return new CupertinoAlertDialog(
          title: new Text(title),
          content: Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: new Text(content),
          ),
          actions: <Widget>[
            CupertinoDialogAction(
              onPressed: () {
                Navigator.of(context, rootNavigator: true).pop("Discard");
              },
              isDefaultAction: true,
              child: Text("OK"),
            ),
          ],
        );
      },
    );
  }


  static showBottomSheetSuccessDialog(context, msg, Color color, {bool isSuccess = true}) {
    return showModalBottomSheet(
        shape: const RoundedRectangleBorder(
          // <-- SEE HERE
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        context: context,
        builder: (context) {
          return SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 10, top: 10),
                  //  height: 0.25 * height,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(
                          top: 0.0,
                          left: 20,
                          right: 20,
                          bottom: 20,
                        ),
                        child: Container(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                InkWell(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Container(
                                    margin: EdgeInsets.only(right: 5),
                                    alignment: Alignment.topRight,
                                    child: Icon(
                                      Icons.cancel,
                                      size: 20,
                                    ),
                                  ),
                                ),
                                Container(
                                  height: 50,
                                  width: 50,
                                  child: Image(
                                    fit: BoxFit.fill,
                                    image: isSuccess ? AssetImage('assets/images/success_action_icon.png') : AssetImage('assets/images/cancel.png'),
                                  ),
                                ),
                                Text(
                                    msg,
                                    style: TextStyle(
                                      color: color,
                                      //color: Color.fromARGB(255, 132, 132, 132),
                                      fontSize: 16,
                                      fontFamily: 'Schyler',
                                    ),
                                    textAlign: TextAlign.center),
                                SizedBox(
                                  width: 10,
                                ),
                              ],
                            )),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        });
  }
}
