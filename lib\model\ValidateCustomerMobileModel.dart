class ValidateCustomerMobileModel {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  int? _otp;
  String? _errorDev;

  ValidateCustomerMobileModel(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        int? otp,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (otp != null) {
      this._otp = otp;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  int? get otp => _otp;
  set otp(int? otp) => _otp = otp;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  ValidateCustomerMobileModel.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _otp = json['otp'];
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['otp'] = this._otp;
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _userId;
  String? _customerGroup;
  String? _clientName;
  String? _clientBusinessName;
  String? _photoThumb;

  Data(
      {String? userId,
        String? customerGroup,
        String? clientName,
        String? clientBusinessName,
        String? photoThumb}) {
    if (userId != null) {
      this._userId = userId;
    }
    if (customerGroup != null) {
      this._customerGroup = customerGroup;
    }
    if (clientName != null) {
      this._clientName = clientName;
    }
    if (clientBusinessName != null) {
      this._clientBusinessName = clientBusinessName;
    }
    if (photoThumb != null) {
      this._photoThumb = photoThumb;
    }
  }

  String? get userId => _userId;
  set userId(String? userId) => _userId = userId;
  String? get customerGroup => _customerGroup;
  set customerGroup(String? customerGroup) => _customerGroup = customerGroup;
  String? get clientName => _clientName;
  set clientName(String? clientName) => _clientName = clientName;
  String? get clientBusinessName => _clientBusinessName;
  set clientBusinessName(String? clientBusinessName) =>
      _clientBusinessName = clientBusinessName;
  String? get photoThumb => _photoThumb;
  set photoThumb(String? photoThumb) => _photoThumb = photoThumb;

  Data.fromJson(Map<String, dynamic> json) {
    _userId = json['user_id'];
    _customerGroup = json['customer_group'];
    _clientName = json['client_name'];
    _clientBusinessName = json['client_business_name'];
    _photoThumb = json['photo_thumb'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['user_id'] = this._userId;
    data['customer_group'] = this._customerGroup;
    data['client_name'] = this._clientName;
    data['client_business_name'] = this._clientBusinessName;
    data['photo_thumb'] = this._photoThumb;
    return data;
  }
}
