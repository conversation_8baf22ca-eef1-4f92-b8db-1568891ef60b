class CustomerDocumentScreen3Model {
  int? _status;
  String? _message;
  int? _success;
  List<Data>? _data;
  String? _errorDev;

  CustomerDocumentScreen3Model(
      {int? status,
        String? message,
        int? success,
        List<Data>? data,
        String? errorDev}) {
    if (status != null) {
      this._status = status;
    }
    if (message != null) {
      this._message = message;
    }
    if (success != null) {
      this._success = success;
    }
    if (data != null) {
      this._data = data;
    }
    if (errorDev != null) {
      this._errorDev = errorDev;
    }
  }

  int? get status => _status;
  set status(int? status) => _status = status;
  String? get message => _message;
  set message(String? message) => _message = message;
  int? get success => _success;
  set success(int? success) => _success = success;
  List<Data>? get data => _data;
  set data(List<Data>? data) => _data = data;
  String? get errorDev => _errorDev;
  set errorDev(String? errorDev) => _errorDev = errorDev;

  CustomerDocumentScreen3Model.fromJson(Map<String, dynamic> json) {
    _status = json['status'];
    _message = json['message'];
    _success = json['success'];
    if (json['data'] != null) {
      _data = <Data>[];
      json['data'].forEach((v) {
        _data!.add(new Data.fromJson(v));
      });
    }
    _errorDev = json['error_dev'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this._status;
    data['message'] = this._message;
    data['success'] = this._success;
    if (this._data != null) {
      data['data'] = this._data!.map((v) => v.toJson()).toList();
    }
    data['error_dev'] = this._errorDev;
    return data;
  }
}

class Data {
  String? _taskId;
  String? _taskTitle;
  String? _dueDate;
  String? _asessmentPeriod;
  String? _wcDocName;
  String? _documentPath;

  Data(
      {String? taskId,
        String? taskTitle,
        String? dueDate,
        String? asessmentPeriod,
        String? wcDocName,
        String? documentPath}) {
    if (taskId != null) {
      this._taskId = taskId;
    }
    if (taskTitle != null) {
      this._taskTitle = taskTitle;
    }
    if (dueDate != null) {
      this._dueDate = dueDate;
    }
    if (asessmentPeriod != null) {
      this._asessmentPeriod = asessmentPeriod;
    }
    if (wcDocName != null) {
      this._wcDocName = wcDocName;
    }
    if (documentPath != null) {
      this._documentPath = documentPath;
    }
  }

  String? get taskId => _taskId;
  set taskId(String? taskId) => _taskId = taskId;
  String? get taskTitle => _taskTitle;
  set taskTitle(String? taskTitle) => _taskTitle = taskTitle;
  String? get dueDate => _dueDate;
  set dueDate(String? dueDate) => _dueDate = dueDate;
  String? get asessmentPeriod => _asessmentPeriod;
  set asessmentPeriod(String? asessmentPeriod) =>
      _asessmentPeriod = asessmentPeriod;
  String? get wcDocName => _wcDocName;
  set wcDocName(String? wcDocName) => _wcDocName = wcDocName;
  String? get documentPath => _documentPath;
  set documentPath(String? documentPath) => _documentPath = documentPath;

  Data.fromJson(Map<String, dynamic> json) {
    _taskId = json['task_id'];
    _taskTitle = json['task_title'];
    _dueDate = json['due_date'];
    _asessmentPeriod = json['asessment_period'];
    _wcDocName = json['wc_doc_name'];
    _documentPath = json['document_path'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['task_id'] = this._taskId;
    data['task_title'] = this._taskTitle;
    data['due_date'] = this._dueDate;
    data['asessment_period'] = this._asessmentPeriod;
    data['wc_doc_name'] = this._wcDocName;
    data['document_path'] = this._documentPath;
    return data;
  }
}
