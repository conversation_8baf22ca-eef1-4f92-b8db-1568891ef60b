// GetX App Routes
// Defines all route names for the ERPC Customer App

abstract class Routes {
  // Initial/Splash Routes
  static const INITIAL = '/';
  static const SPLASH = '/splash';
  
  // Authentication Routes
  static const LOGIN = '/login';
  static const LOGIN_MOBILE = '/login-mobile';
  static const SELECT_USER = '/select-user';
  static const CHECK_MPIN = '/check-mpin';
  
  // Main App Routes
  static const HOME = '/home';
  static const DASHBOARD = '/dashboard';
  
  // Feature Routes
  static const INVOICES = '/invoices';
  static const REQUESTS = '/requests';
  static const SUBMIT_REQUEST = '/submit-request';
  static const DOCUMENTS = '/documents';
  static const MY_ACCOUNT = '/my-account';
  static const NOTIFICATIONS = '/notifications';
  
  // Service Routes
  static const NEW_SERVICES = '/new-services';
  static const SERVICES_LIST = '/services-list';
  
  // Settings Routes
  static const SETTINGS = '/settings';
  static const PROFILE = '/profile';
}
